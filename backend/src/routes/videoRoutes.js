import express from 'express';
import videoController from '../controllers/videoController.js';

const router = express.Router();

// Public routes (for frontend)

/**
 * @route   GET /api/videos
 * @desc    Get all published videos with pagination
 * @access  Public
 * @query   page?, limit?, category?, featured?
 */
router.get('/', videoController.getAllVideos);

// Admin routes (protected - you can add auth middleware later)

/**
 * @route   GET /api/videos/admin/all
 * @desc    Get all videos (including unpublished) for admin
 * @access  Admin
 */
router.get('/admin/all', videoController.getAllVideosAdmin);

/**
 * @route   GET /api/videos/admin/:id
 * @desc    Get video by ID for admin
 * @access  Admin
 */
router.get('/admin/:id', videoController.getVideoByIdAdmin);

/**
 * @route   POST /api/videos/admin
 * @desc    Create new video
 * @access  Admin
 */
router.post('/admin', videoController.createVideo);

/**
 * @route   PUT /api/videos/admin/:id
 * @desc    Update video
 * @access  Admin
 */
router.put('/admin/:id', videoController.updateVideo);

/**
 * @route   DELETE /api/videos/admin/:id
 * @desc    Delete video
 * @access  Admin
 */
router.delete('/admin/:id', videoController.deleteVideo);

/**
 * @route   PATCH /api/videos/admin/:id/toggle
 * @desc    Toggle video active status
 * @access  Admin
 */
router.patch('/admin/:id/toggle', videoController.toggleVideoStatus);

/**
 * @route   PATCH /api/videos/admin/:id/publish
 * @desc    Publish/unpublish video
 * @access  Admin
 */
router.patch('/admin/:id/publish', videoController.togglePublishStatus);

/**
 * @route   PATCH /api/videos/admin/:id/feature
 * @desc    Toggle video featured status
 * @access  Admin
 */
router.patch('/admin/:id/feature', videoController.toggleFeaturedStatus);

/**
 * @route   PATCH /api/videos/admin/order
 * @desc    Update video display order
 * @access  Admin
 */
router.patch('/admin/order', videoController.updateDisplayOrder);

/**
 * @route   GET /api/videos/admin/analytics/:id
 * @desc    Get video analytics
 * @access  Admin
 */
router.get('/admin/analytics/:id', videoController.getVideoAnalytics);

/**
 * @route   GET /api/videos/admin/statistics
 * @desc    Get video statistics for admin dashboard
 * @access  Admin
 */
router.get('/admin/statistics', videoController.getVideoStatistics);

export default router;
