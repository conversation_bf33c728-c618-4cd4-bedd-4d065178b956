import express from 'express';
import ProductController from '../controllers/ProductController.js';

const router = express.Router();

// Public product routes (no authentication required)

// GET /api/products - Get all products with filtering and pagination
router.get('/', ProductController.getAllProducts);

// GET /api/products/featured - Get featured products
router.get('/featured', ProductController.getFeaturedProducts);

// GET /api/products/bestsellers - Get bestseller products
router.get('/bestsellers', ProductController.getBestsellerProducts);

// GET /api/products/new-arrivals - Get new arrival products
router.get('/new-arrivals', ProductController.getNewArrivalProducts);

// GET /api/products/trending - Get trending products
router.get('/trending', ProductController.getTrendingProducts);

// GET /api/products/categories - Get all categories
router.get('/categories', ProductController.getCategories);

// GET /api/products/search - Search products
router.get('/search', ProductController.searchProducts);

// GET /api/products/filters - Get product filters for frontend
router.get('/filters', ProductController.getProductFilters);

// GET /api/products/category/:categoryId - Get products by category
router.get('/category/:categoryId', ProductController.getProductsByCategory);

// GET /api/products/slug/:slug - Get product by slug
router.get('/slug/:slug', ProductController.getProductBySlug);

// GET /api/products/:id - Get product by ID (must be last to avoid conflicts)
router.get('/:id', ProductController.getProductById);

// GET /api/products/:id/variants - Get product variants (colors/sizes)
router.get('/:id/variants', ProductController.getProductVariants);

// GET /api/products/:id/related - Get related products (enhanced)
router.get('/:id/related', ProductController.getRelatedProducts);

// GET /api/products/:id/recommendations - Get product recommendations (alias for related)
router.get('/:id/recommendations', ProductController.getRelatedProducts);

// GET /api/products/:id/gallery - Get product gallery images
router.get('/:id/gallery', ProductController.getProductGallery);

// GET /api/products/:id/reviews - Get product reviews
router.get('/:id/reviews', ProductController.getProductReviews);

export default router;
