import ProductService from '../services/ProductService.js';
import Product from '../shared/models/productModel.js';
import Category from '../shared/models/categoryModel.js';

class ProductController {
  // Get all products with filtering and pagination
  static async getAllProducts(req, res) {
    try {
      const filters = {
        category: req.query.category,
        subcategory: req.query.subcategory,
        brand: req.query.brand,
        minPrice: req.query.minPrice,
        maxPrice: req.query.maxPrice,
        inStock: req.query.inStock,
        minRating: req.query.minRating,
        material: req.query.material,
        style: req.query.style,
        occasion: req.query.occasion,
        season: req.query.season,
        search: req.query.search,
        sortBy: req.query.sortBy,
        page: req.query.page,
        limit: req.query.limit
      };

      // Remove undefined values
      Object.keys(filters).forEach(key => {
        if (filters[key] === undefined) {
          delete filters[key];
        }
      });

      const result = await ProductService.getAllProducts(filters);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get product by ID
  static async getProductById(req, res) {
    try {
      const { id } = req.params;
      const result = await ProductService.getProductById(id);

      if (!result.success) {
        return res.status(404).json(result);
      }

      res.status(200).json({
        success: true,
        data: {
          product: result.data
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get product by slug
  static async getProductBySlug(req, res) {
    try {
      const { slug } = req.params;
      const result = await ProductService.getProductBySlug(slug);

      if (!result.success) {
        return res.status(404).json(result);
      }

      res.status(200).json({
        success: true,
        data: {
          product: result.data
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get featured products
  static async getFeaturedProducts(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 8;
      const result = await ProductService.getFeaturedProducts(limit);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get bestseller products
  static async getBestsellerProducts(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 8;
      const result = await ProductService.getBestsellerProducts(limit);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get new arrival products
  static async getNewArrivalProducts(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 8;
      const result = await ProductService.getNewArrivalProducts(limit);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get trending products
  static async getTrendingProducts(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 8;
      const result = await ProductService.getTrendingProducts(limit);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get all categories
  static async getCategories(req, res) {
    try {
      const categories = await Category.find({ is_active: true })
        .select('name slug description image banner_image icon is_featured sort_order parent_id')
        .sort({ sort_order: 1, name: 1 })
        .lean();

      // Organize categories into parent and subcategories
      const parentCategories = categories.filter(cat => !cat.parent_id);
      const subcategories = categories.filter(cat => cat.parent_id);

      // Add subcategories to their parent categories
      const categoriesWithSubs = parentCategories.map(parent => ({
        ...parent,
        subcategories: subcategories.filter(sub =>
          sub.parent_id && sub.parent_id.toString() === parent._id.toString()
        )
      }));

      res.status(200).json({
        success: true,
        data: {
          categories: categoriesWithSubs,
          total: parentCategories.length,
          featured: categoriesWithSubs.filter(cat => cat.is_featured)
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get products by category
  static async getProductsByCategory(req, res) {
    try {
      const { categoryId } = req.params;
      const options = {
        page: req.query.page,
        limit: req.query.limit,
        sortBy: req.query.sortBy
      };

      const result = await ProductService.getProductsByCategory(categoryId, options);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Search products
  static async searchProducts(req, res) {
    try {
      const { q: searchTerm } = req.query;

      if (!searchTerm) {
        return res.status(400).json({
          success: false,
          message: 'Search term is required'
        });
      }

      const filters = {
        search: searchTerm,
        page: req.query.page,
        limit: req.query.limit,
        sortBy: req.query.sortBy
      };

      const result = await ProductService.getAllProducts(filters);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get product filters (for frontend filter UI)
  static async getProductFilters(req, res) {
    try {
      // Get unique brands
      const brands = await Product.distinct('brand', { is_active: true });
      
      // Get unique materials
      const materials = await Product.distinct('material', { is_active: true });
      
      // Get unique styles
      const styles = await Product.distinct('style', { is_active: true });
      
      // Get unique occasions
      const occasions = await Product.distinct('occasion', { is_active: true });
      
      // Get unique seasons
      const seasons = await Product.distinct('season', { is_active: true });
      
      // Get price range
      const priceRange = await Product.aggregate([
        { $match: { is_active: true } },
        {
          $group: {
            _id: null,
            minPrice: { $min: '$price.current' },
            maxPrice: { $max: '$price.current' }
          }
        }
      ]);

      res.status(200).json({
        success: true,
        data: {
          brands: brands.filter(Boolean).sort(),
          materials: materials.filter(Boolean).sort(),
          styles: styles.filter(Boolean).sort(),
          occasions: occasions.filter(Boolean).sort(),
          seasons: seasons.filter(Boolean).sort(),
          priceRange: priceRange[0] || { minPrice: 0, maxPrice: 0 }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get product variants (different colors/sizes)
  static async getProductVariants(req, res) {
    try {
      const { id } = req.params;
      const result = await ProductService.getProductById(id);

      if (!result.success) {
        return res.status(404).json(result);
      }

      const product = result.data;

      res.status(200).json({
        success: true,
        data: {
          product_id: product.product_id,
          name: product.name,
          colors: product.colors,
          sizes: product.sizes,
          available_combinations: product.colors.filter(c => c.is_available).length * 
                                 product.sizes.filter(s => s.is_available).length
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get related products (enhanced with relationship types)
  static async getRelatedProducts(req, res) {
    try {
      const { id } = req.params;
      const limit = parseInt(req.query.limit) || 8;
      const relationshipType = req.query.type; // 'similar', 'complementary', 'alternative', etc.

      // Try to find product by MongoDB _id first, then by product_id
      let product = null;

      // Check if id looks like a MongoDB ObjectId (24 hex characters)
      if (id.match(/^[0-9a-fA-F]{24}$/)) {
        product = await Product.findById(id);
      } else {
        // Otherwise, search by product_id
        product = await Product.findOne({ product_id: id });
      }

      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      let relatedProducts = [];

      // First, try to get explicitly defined related products
      if (product.related_products && product.related_products.length > 0) {
        const relatedQuery = Product.find({
          _id: { $in: product.related_products.map(rp => rp.product_id) },
          is_active: true,
          availability: 'In Stock'
        })
        .populate('category', 'name slug')
        .lean();

        if (relationshipType) {
          // Filter by relationship type if specified
          const filteredRelatedIds = product.related_products
            .filter(rp => rp.relationship_type === relationshipType)
            .map(rp => rp.product_id);

          relatedProducts = await Product.find({
            _id: { $in: filteredRelatedIds },
            is_active: true,
            availability: 'In Stock'
          })
          .populate('category', 'name slug')
          .sort({ 'rating.average_rating': -1 })
          .limit(limit)
          .lean();
        } else {
          relatedProducts = await relatedQuery
            .sort({ 'rating.average_rating': -1 })
            .limit(limit);
        }
      }

      // If not enough related products found, find similar products by category/brand
      if (relatedProducts.length < limit) {
        const remainingLimit = limit - relatedProducts.length;
        const existingIds = relatedProducts.map(p => p._id.toString());

        const similarProducts = await Product.find({
          _id: { $ne: product._id, $nin: existingIds },
          $or: [
            { category: product.category },
            { brand: product.brand },
            { material: product.material },
            { style: product.style }
          ],
          is_active: true,
          availability: 'In Stock'
        })
          .populate('category', 'name slug')
          .sort({ 'rating.average_rating': -1 })
          .limit(remainingLimit)
          .lean();

        relatedProducts = [...relatedProducts, ...similarProducts];
      }

      res.status(200).json({
        success: true,
        data: {
          products: relatedProducts,
          total: relatedProducts.length,
          relationship_type: relationshipType || 'mixed'
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get product gallery images
  static async getProductGallery(req, res) {
    try {
      const { id } = req.params;
      const product = await Product.findById(id).select('product_images gallery_images name').lean();

      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      const allImages = [
        ...product.product_images.map(img => ({ ...img, source: 'primary' })),
        ...product.gallery_images.map(img => ({ ...img, source: 'gallery' }))
      ].sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));

      res.status(200).json({
        success: true,
        data: {
          product_name: product.name,
          images: allImages,
          total_images: allImages.length
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get product reviews
  static async getProductReviews(req, res) {
    try {
      const { id } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const sortBy = req.query.sortBy || 'newest';

      const product = await Product.findById(id);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      // Sort reviews
      let sortedReviews = [...product.rating.reviews];
      switch (sortBy) {
        case 'newest':
          sortedReviews.sort((a, b) => new Date(b.review_date) - new Date(a.review_date));
          break;
        case 'oldest':
          sortedReviews.sort((a, b) => new Date(a.review_date) - new Date(b.review_date));
          break;
        case 'highest_rating':
          sortedReviews.sort((a, b) => b.rating - a.rating);
          break;
        case 'lowest_rating':
          sortedReviews.sort((a, b) => a.rating - b.rating);
          break;
        case 'most_helpful':
          sortedReviews.sort((a, b) => b.helpful_count - a.helpful_count);
          break;
      }

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedReviews = sortedReviews.slice(startIndex, endIndex);

      const totalReviews = sortedReviews.length;
      const totalPages = Math.ceil(totalReviews / limit);

      res.status(200).json({
        success: true,
        data: {
          reviews: paginatedReviews,
          pagination: {
            currentPage: page,
            totalPages,
            totalReviews,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          },
          rating_summary: {
            average_rating: product.rating.average_rating,
            reviews_count: product.rating.reviews_count,
            rating_breakdown: product.rating.rating_breakdown
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}

export default ProductController;
