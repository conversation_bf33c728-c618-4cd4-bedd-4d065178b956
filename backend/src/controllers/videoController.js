import videoService from '../services/videoService.js';

class VideoController {
  // Get all published videos with pagination (for frontend)
  async getAllVideos(req, res) {
    try {
      const { page = 1, limit = 10, category, featured } = req.query;
      
      const filters = {};
      if (category) filters.category = category;
      if (featured === 'true') filters.isFeatured = true;
      
      const videos = await videoService.getAllVideos(page, limit, filters);
      
      res.status(200).json({
        success: true,
        data: videos,
        message: 'Videos fetched successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }



  // Admin methods

  // Get all videos for admin (including unpublished)
  async getAllVideosAdmin(req, res) {
    try {
      const { page = 1, limit = 20, category, status } = req.query;
      
      const filters = {};
      if (category) filters.category = category;
      if (status) {
        if (status === 'published') filters.isPublished = true;
        if (status === 'unpublished') filters.isPublished = false;
        if (status === 'featured') filters.isFeatured = true;
        if (status === 'active') filters.isActive = true;
        if (status === 'inactive') filters.isActive = false;
      }
      
      const videos = await videoService.getAllVideosAdmin(page, limit, filters);
      
      res.status(200).json({
        success: true,
        data: videos,
        message: 'Admin videos fetched successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // Get video by ID for admin
  async getVideoByIdAdmin(req, res) {
    try {
      const { id } = req.params;
      const video = await videoService.getVideoByIdAdmin(id);
      
      res.status(200).json({
        success: true,
        data: video,
        message: 'Video fetched successfully'
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        message: error.message
      });
    }
  }

  // Create new video
  async createVideo(req, res) {
    try {
      const videoData = req.body;
      const video = await videoService.createVideo(videoData);
      
      res.status(201).json({
        success: true,
        data: video,
        message: 'Video created successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  // Update video
  async updateVideo(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      const video = await videoService.updateVideo(id, updateData);
      
      res.status(200).json({
        success: true,
        data: video,
        message: 'Video updated successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  // Delete video
  async deleteVideo(req, res) {
    try {
      const { id } = req.params;
      const video = await videoService.deleteVideo(id);
      
      res.status(200).json({
        success: true,
        data: video,
        message: 'Video deleted successfully'
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        message: error.message
      });
    }
  }



  // Toggle featured status
  async toggleFeaturedStatus(req, res) {
    try {
      const { id } = req.params;
      const video = await videoService.toggleFeaturedStatus(id);
      
      res.status(200).json({
        success: true,
        data: video,
        message: 'Video featured status updated successfully'
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        message: error.message
      });
    }
  }


}

export default new VideoController();
