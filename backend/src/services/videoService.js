import Video from '../shared/models/videoModel.js';

class VideoService {
  // Get all videos with pagination and filters (for frontend)
  async getAllVideos(page = 1, limit = 10, filters = {}) {
    try {
      const skip = (page - 1) * limit;
      
      const query = { 
        isPublished: true, 
        isActive: true,
        ...filters
      };
      
      const [videos, totalVideos] = await Promise.all([
        Video.find(query)
          .sort({ displayOrder: 1, publishedAt: -1 })
          .skip(skip)
          .limit(parseInt(limit)),
        Video.countDocuments(query)
      ]);

      return {
        videos,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalVideos / limit),
          totalVideos,
          hasNext: skip + videos.length < totalVideos,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      throw new Error(`Error fetching videos: ${error.message}`);
    }
  }



  // Admin methods

  // Get all videos for admin (including unpublished)
  async getAllVideosAdmin(page = 1, limit = 20, filters = {}) {
    try {
      const skip = (page - 1) * limit;
      
      const [videos, total] = await Promise.all([
        Video.find(filters)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(parseInt(limit)),
        Video.countDocuments(filters)
      ]);

      return {
        videos,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total,
          limit: parseInt(limit)
        }
      };
    } catch (error) {
      throw new Error(`Error fetching admin videos: ${error.message}`);
    }
  }

  // Get video by ID for admin
  async getVideoByIdAdmin(id) {
    try {
      const video = await Video.findById(id);
      if (!video) {
        throw new Error('Video not found');
      }
      return video;
    } catch (error) {
      throw new Error(`Error fetching video: ${error.message}`);
    }
  }

  // Create new video
  async createVideo(videoData) {
    try {
      const video = new Video(videoData);
      await video.save();
      return video;
    } catch (error) {
      throw new Error(`Error creating video: ${error.message}`);
    }
  }

  // Update video
  async updateVideo(id, updateData) {
    try {
      const video = await Video.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );
      
      if (!video) {
        throw new Error('Video not found');
      }
      
      return video;
    } catch (error) {
      throw new Error(`Error updating video: ${error.message}`);
    }
  }

  // Delete video
  async deleteVideo(id) {
    try {
      const video = await Video.findByIdAndDelete(id);
      if (!video) {
        throw new Error('Video not found');
      }
      return video;
    } catch (error) {
      throw new Error(`Error deleting video: ${error.message}`);
    }
  }



  // Toggle featured status
  async toggleFeaturedStatus(id) {
    try {
      const video = await Video.findById(id);
      if (!video) {
        throw new Error('Video not found');
      }
      
      video.isFeatured = !video.isFeatured;
      await video.save();
      
      return video;
    } catch (error) {
      throw new Error(`Error toggling featured status: ${error.message}`);
    }
  }


}

export default new VideoService();
