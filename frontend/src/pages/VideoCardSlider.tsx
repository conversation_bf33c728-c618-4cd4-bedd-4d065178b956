import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import VideoCard from '../components/cards/VideoCard';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// Simple Video interface to avoid import issues
interface Video {
  _id: string;
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  isFeatured: boolean;
  isActive: boolean;
  isPublished: boolean;
  category: string;
  tags: string[];
  viewCount: number;
  likeCount: number;
  shareCount: number;
  displayOrder: number;
  thumbnailUrl?: string;
  duration?: string;
}

interface VideoCardSliderProps {
  videos: Video[];
  title?: string;
  subtitle?: string;
  autoplay?: boolean;
  delay?: number;
  showNavigation?: boolean;
  showPagination?: boolean;
}

const VideoCardSlider: React.FC<VideoCardSliderProps> = ({
  videos,
  title = "Our Videos",
  subtitle = "Discover our latest collections and styling tips",
  autoplay = true,
  delay = 5000,
  showNavigation = true,
  showPagination = true,
}) => {
  if (!videos || videos.length === 0) {
    return null;
  }

  return (
    <section className="w-full py-8 md:py-12 lg:py-16 bg-gray-50">
      {/* Header */}
      <div className="text-center mb-8 px-4">
        <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          {title}
        </h2>
        {subtitle && (
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            {subtitle}
          </p>
        )}
      </div>

      {/* Full Width Video Slider */}
      <div className="relative w-full">
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={16}
          slidesPerView={1.2}
          centeredSlides={false}
          autoplay={autoplay ? {
            delay,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          } : false}
          navigation={showNavigation}
          pagination={showPagination ? {
            clickable: true,
            dynamicBullets: true,
          } : false}
          breakpoints={{
            480: {
              slidesPerView: 1.5,
              spaceBetween: 16,
            },
            640: {
              slidesPerView: 2.2,
              spaceBetween: 20,
            },
            768: {
              slidesPerView: 2.5,
              spaceBetween: 24,
            },
            1024: {
              slidesPerView: 3.2,
              spaceBetween: 28,
            },
            1280: {
              slidesPerView: 4.2,
              spaceBetween: 32,
            },
            1536: {
              slidesPerView: 5.2,
              spaceBetween: 36,
            },
          }}
          className="video-slider-full-width"
        >
          {videos.map((video, index) => (
            <SwiperSlide key={video.id || video._id}>
              <VideoCard
                videoUrl={video.videoUrl}
                index={index}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
};

export default VideoCardSlider;
