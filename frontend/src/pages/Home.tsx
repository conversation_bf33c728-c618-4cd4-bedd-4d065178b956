import React from "react";
import { useEffect, useRef, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/hooks/hooks";
import {
  HomeBanner,
  ProductSlider,
  NewArrivals,
  Bestsellers,
  KurtiCollection,
  LehengaCollection,
  CustomerReviews,
  SubscriptionSection,
} from "@/components/home";
import {
  fetchFeaturedProducts,
  fetchNewArrivalProducts,
  fetchBestsellerProducts
} from "@/store/slices/products/productSlice";
import VideoCardSlider from "@/components/cards/VideoCardSlider";
import BannerSlider from "@/components/BannerSlider/BannerSlider";

import SimpleImageCardSlider from "@/components/SimpleImageCardSlider/SimpleImageCardSlider";
import {
  fetchKurtiBanners,
  fetchLehengaBanners,
  selectKurtiBanners,
  selectLehengaBanners,
  selectKurtiBannersLoading,
  selectLehengaBannersLoading
} from "@/store/slices/banners/bannerSlice";
import { videoService, type Video } from "@/services/videoService";
import type { BannerImage } from "@/components/BannerSlider/BannerSlider";
import subscriptionBackgroundImage from "@/assets/imagesVidoes/images/banner-images/homeDeckBanner03.jpg";


const Home: React.FC = () => {
  const dispatch = useAppDispatch();
  // Video state
  const [allVideos, setAllVideos] = React.useState<Video[]>([]);
  const [videosLoading, setVideosLoading] = React.useState(false);

  // Banner state from Redux
  const kurtiBanners = useAppSelector(selectKurtiBanners);
  const lehengaBanners = useAppSelector(selectLehengaBanners);
  const kurtiBannersLoading = useAppSelector(selectKurtiBannersLoading);
  const lehengaBannersLoading = useAppSelector(selectLehengaBannersLoading);

  // Convert Banner to BannerImage format for compatibility - memoized to prevent infinite re-renders
  const convertBannerToBannerImage = React.useCallback((banners: any[]): BannerImage[] => {
    return banners.map(banner => ({
      id: banner._id || banner.id,
      title: banner.title,
      subtitle: banner.subtitle,
      desktopImage: banner.desktopImage,
      mobileImage: banner.mobileImage,
      link: banner.ctaLink || banner.link || '#',
      buttonText: banner.ctaText || banner.buttonText || 'Shop Now',
      isActive: banner.isActive,
      category: banner.category,
      altText: banner.altText || banner.title
    }));
  }, []);

  // Convert banners for use in BannerSlider - memoized
  const kurtiBannerImages = React.useMemo(() =>
    convertBannerToBannerImage(kurtiBanners), [kurtiBanners, convertBannerToBannerImage]);
  const lehengaBannerImages = React.useMemo(() =>
    convertBannerToBannerImage(lehengaBanners), [lehengaBanners, convertBannerToBannerImage]);

  // Function to fetch videos using service
  const fetchVideos = useCallback(async () => {
    setVideosLoading(true);
    try {
      const videos = await videoService.getAllVideos();
      setAllVideos(videos);
      console.log('Fetched videos:', videos.length);
    } catch (error) {
      console.error("Error fetching videos:", error);
      setAllVideos([]); // Set empty array on error
    } finally {
      setVideosLoading(false);
    }
  }, []);

  // Function to fetch banner data using Redux
  const fetchBannerData = useCallback(() => {
    // Dispatch Redux actions to fetch banners
    dispatch(fetchKurtiBanners());
    dispatch(fetchLehengaBanners());
  }, [dispatch]);

  const initialized = useRef(false);
  useEffect(() => {
    if (!initialized.current) {
      console.log('Home: Initializing data fetch...');

      // Fetch videos
      fetchVideos();

      // Fetch product data for home page
      dispatch(fetchFeaturedProducts(8));
      dispatch(fetchNewArrivalProducts(8));
      dispatch(fetchBestsellerProducts(8));

      // Fetch banner data
      fetchBannerData();

      initialized.current = true;
    }
  }, [dispatch, fetchBannerData, fetchVideos]);
  return (
    <div className="pt-[calc(10px+4rem)]">
      {" "}
      {/* Account for navbar height (10px for promo + ~4rem for menu) */}
      <HomeBanner
        title="Luxury Ethnic Wear"
        subtitle="Discover our exquisite collection of premium ethnic wear designed for the modern woman"
        ctaText="Explore Collection"
      />



      <ProductSlider
        title="Featured Collection"
        subtitle="Explore our handpicked selection of premium ethnic wear for every occasion"
        category="mixed"
        limit={8}
      />
      <NewArrivals
        title="New Arrivals"
        subtitle="Discover our latest collection of exquisite ethnic wear crafted with premium fabrics and intricate designs"
        limit={8}
      />
      <Bestsellers
        title="Bestsellers"
        subtitle="Our most popular products loved by customers across India"
        limit={8}
      />
      {/* Kurti Banner Slider */}
      <section className="w-full">
        {kurtiBannerImages.length > 0 && (
          <BannerSlider
            key="kurti-banner-slider"
            banners={kurtiBannerImages}
            loading={kurtiBannersLoading}
            bannerType="Kurti"
            autoplay={true}
            delay={5000}
            pauseOnHover={true}
            showNavigation={true}
            showPagination={true}
          />
        )}
      </section>
      <KurtiCollection
        title="Kurti Collections"
        subtitle="Explore our exquisite collection of premium kurtis designed for the modern woman"
        limit={8}
      />
      {/* Style Showcase Section */}
      <section className="w-full bg-gray-50">
        <div className="w-full text-center py-8 md:py-12 lg:py-16 px-4 sm:px-6">
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Style Inspiration
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Explore our curated collection of trending styles and discover your perfect look
          </p>
        </div>
        <SimpleImageCardSlider
          heading="Latest Styles"
          autoSlideInterval={3000}
        />
      </section>



      {/* Full-width Video Slider */}
      {videosLoading ? (
        <div className="w-full py-6 overflow-hidden">
          <div className="flex space-x-4 px-4 overflow-x-auto">
            {[...Array(6)].map((_, index) => (
              <div
                key={index}
                className="flex-none w-[300px] h-[400px] bg-gray-200 animate-pulse rounded-lg"
                style={{ animationDelay: `${index * 100}ms` }}
              ></div>
            ))}
          </div>
        </div>
      ) : allVideos.length > 0 ? (
        <VideoCardSlider
          videos={allVideos}
          title="Our Videos"
          subtitle="Discover our latest collections and styling tips"
        />
      ) : (
        <div className="w-full py-8 text-center">
          <p className="text-gray-500">No videos available</p>
        </div>
      )}

      {/* Lehenga Banner Slider */}
      <section className="w-full">
        {lehengaBannerImages.length > 0 && (
          <BannerSlider
            key="lehenga-banner-slider"
            banners={lehengaBannerImages}
            loading={lehengaBannersLoading}
            bannerType="Lehenga"
            autoplay={true}
            delay={6000}
            pauseOnHover={true}
            showNavigation={true}
            showPagination={true}
          />
        )}
      </section>

      <LehengaCollection
        title="Lehenga Collections"
        subtitle="Discover our stunning collection of designer lehengas perfect for weddings and special occasions"
        limit={8}
      />

      <CustomerReviews
        title="Our Customers Love Us"
        subtitle="Read what our customers have to say about their shopping experience"
      />
      <SubscriptionSection
        title="Join Our Community"
        subtitle="Subscribe to receive exclusive offers, early access to new collections, and style inspiration"
        backgroundImage={subscriptionBackgroundImage}
      />
    </div>
  );
};

export default Home;
