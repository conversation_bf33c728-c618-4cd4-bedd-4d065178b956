import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { ChevronRight, AlertCircle, Star } from "lucide-react";

import {
  ProductImageGallery,
  ProductInfo,
  ProductTabs,
  RelatedProducts,
} from "@/components/product";
import CustomerReviews from "@/components/product/CustomerReviews";
import { useAppDispatch, useAppSelector } from "@/hooks/hooks";
import { addToCart } from "@/store/slices/cart/cartSlice";
import {
  addToWishlist,
  removeFromWishlist,
  selectIsInWishlist,
  fetchWishlist,
} from "@/store/slices/wishlist/wishlistSlice";
import {
  fetchProductById,
  fetchProductBySlug,
  fetchRecommendations,
  selectCurrentProduct,
  selectCurrentProductLoading,
  selectCurrentProductError,
  selectRecommendedProducts,
  type Product
} from "@/store/slices/products/productSlice";
import { getFullImageUrl } from "@/utils/imageUtils";
import { getCategoryName } from "@/utils/productUtils";
import { toast } from "@/utils/toast";


// Helper function to convert backend product to UI format
const convertProductForUI = (backendProduct: any) => {
  if (!backendProduct || !backendProduct.price) return null;

  // More flexible stock checking based on actual backend API structure
  const getStockValue = (product: any) => {
    // Check stock_status field (matches backend API)
    if (product.stock_status) {
      const status = product.stock_status.toLowerCase();
      if (status === 'in stock' || status === 'limited stock' || status === 'available') {
        // Calculate total stock from sizes if available
        if (product.sizes && Array.isArray(product.sizes)) {
          const totalStock = product.sizes.reduce((total: number, size: any) => {
            return total + (size.available_stock || 0);
          }, 0);
          return totalStock > 0 ? totalStock : 50; // Use calculated stock or default to 50
        }
        return 50; // Default to available
      }
      if (status === 'out of stock' || status === 'unavailable') {
        return 0;
      }
    }

    // Check availability field (matches backend API)
    if (product.availability) {
      const availability = product.availability.toLowerCase();
      if (availability === 'in stock' || availability === 'available') {
        return 50;
      }
      if (availability === 'out of stock' || availability === 'unavailable') {
        return 0;
      }
    }

    // Check for stock_quantity directly
    if (product.stock_quantity !== undefined) {
      return product.stock_quantity > 0 ? product.stock_quantity : 0;
    }

    // Default to available (50 in stock) since most products in the API are available
    console.log('No specific stock info found, defaulting to available');
    return 50;
  };

  return {
    id: backendProduct.product_id,
    name: backendProduct.name,
    price: backendProduct.price?.original || 0,
    discountPrice: backendProduct.price?.current !== backendProduct.price?.original ? backendProduct.price?.current : undefined,
    rating: backendProduct.rating?.average_rating || 0,
    reviewCount: backendProduct.rating?.reviews_count || 0,
    description: backendProduct.description || '',
    sizes: backendProduct.sizes?.map((s: any) => s.size_name || s.size) || [],
    colors: backendProduct.colors?.map((c: any) => c.color_name || c.color) || [],
    stock: getStockValue(backendProduct),
    category: getCategoryName(backendProduct.category),
    subcategory: backendProduct.subcategory || '',
    images: backendProduct.product_images?.map((img: any) => getFullImageUrl(img.image_url)) || [],
    isNew: backendProduct.is_new_arrival || false,
  };
};

// Product interface is now imported from productSlice

// Define the Review type
interface Review {
  id: string;
  userName: string;
  rating: number;
  date: string;
  comment: string;
  verified: boolean;
  userImage?: string;
}

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // Redux state
  const product = useAppSelector(selectCurrentProduct);
  const relatedProducts = useAppSelector(selectRecommendedProducts);
  const isLoading = useAppSelector(selectCurrentProductLoading);
  const error = useAppSelector(selectCurrentProductError);
  const isInWishlist = useAppSelector(selectIsInWishlist(product?.product_id || ''));

  // Local state
  const [reviews, setReviews] = useState<Review[]>([]);
  const [selectedImage, setSelectedImage] = useState<string>("");
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState<string>("");
  const [selectedColor, setSelectedColor] = useState<string>("");
  const [isAddingToWishlist, setIsAddingToWishlist] = useState(false);
  const [showZoomModal, setShowZoomModal] = useState(false);

  // Fetch product data and wishlist on component mount
  useEffect(() => {
    if (id) {
      console.log('ProductDetail: Fetching data for product slug:', id);
      // Fetch product by slug from backend (id parameter is actually the slug)
      dispatch(fetchProductBySlug(id));
      // Fetch recommendations for this product (will use product_id once product is loaded)
      // Note: We'll need to fetch recommendations after we get the product data
      // Fetch user's wishlist to check if product is in wishlist
      dispatch(fetchWishlist());
    }
  }, [dispatch, id]);

  // Fetch recommendations once product is loaded
  useEffect(() => {
    if (product && product.product_id) {
      console.log('ProductDetail: Fetching recommendations for product_id:', product.product_id);
      dispatch(fetchRecommendations({ id: product.product_id, limit: 4 }));
    }
  }, [dispatch, product?.product_id]);

  // Debug effect for related products
  useEffect(() => {
    console.log('ProductDetail: Related products updated:', {
      count: relatedProducts?.length || 0,
      products: relatedProducts?.slice(0, 2) // Log first 2 for debugging
    });
  }, [relatedProducts]);

  // Set selected image when product loads
  useEffect(() => {
    if (product && product.product_images && product.product_images.length > 0) {
      const primaryImage = product.product_images.find(img => img.is_primary);
      const imageUrl = primaryImage?.image_url || product.product_images[0].image_url;
      // Add backend URL prefix if not already present
      setSelectedImage(getFullImageUrl(imageUrl));
    }
  }, [product]);

  // Set default size and color when product loads
  useEffect(() => {
    if (product) {
      if (product.sizes && product.sizes.length > 0) {
        const availableSize = product.sizes.find(size => size.is_available);
        setSelectedSize(availableSize?.size_name || product.sizes[0].size_name);
      }
      if (product.colors && product.colors.length > 0) {
        const availableColor = product.colors.find(color => color.is_available);
        setSelectedColor(availableColor?.color_name || product.colors[0].color_name);
      }
    }
  }, [product]);

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= 10) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    if (product) {
      dispatch(
        addToCart({
          id: product.product_id,
          title: product.name,
          description: product.description,
          price: product.price?.current || 0,
          image: getFullImageUrl(product.product_images[0]?.image_url || ''),
          category: product.category,
          quantity: quantity,
          rating: {
            rate: product.rating.average_rating,
            count: product.rating.reviews_count
          },
        })
      );

      // Show success notification
      toast.success(`Added ${product.name} (${quantity}) to cart!`, {
        duration: 3000
      });
    }
  };

  const handleBuyNow = () => {
    if (product) {
      // First add the product to cart
      dispatch(
        addToCart({
          id: product.product_id,
          title: product.name,
          description: product.description,
          price: product.price?.current || 0,
          image: getFullImageUrl(product.product_images[0]?.image_url || ''),
          category: product.category,
          quantity: quantity,
          rating: {
            rate: product.rating.average_rating,
            count: product.rating.reviews_count
          },
        })
      );

      // Navigate to cart instead of checkout to avoid auth issues
      navigate("/cart");
    }
  };

  const handleWishlist = async () => {
    if (!product) return;

    setIsAddingToWishlist(true);
    try {
      if (isInWishlist) {
        // Remove from wishlist - need to find the item ID first
        await dispatch(removeFromWishlist(product.product_id));
        // Show success notification
        toast.info(`Removed ${product.name} from wishlist`, {
          duration: 2500
        });
      } else {
        // Add to wishlist
        await dispatch(
          addToWishlist({
            productId: product.product_id,
            productDetails: {
              name: product.name,
              price: product.price?.current || 0,
              discountPrice: product.price?.original !== product.price?.current ? product.price?.original : undefined,
              image: getFullImageUrl(product.product_images[0]?.image_url || ''),
              sku: product.slug,
              brand: product.brand
            },
            priceWhenAdded: product.price?.current || 0,
            variant: {
              color: selectedColor,
              size: selectedSize
            }
          })
        );
        // Show success notification
        toast.success(`Added ${product.name} to wishlist!`, {
          duration: 2500
        });
      }
    } catch (error) {
      console.error("Error updating wishlist:", error);
      // Show error notification
      toast.error("There was an error updating your wishlist. Please try again.", {
        duration: 4000
      });
    } finally {
      setIsAddingToWishlist(false);
    }
  };

  const handleShare = async () => {
    if (!product) return;

    const shareData = {
      title: `${product.name} - Sajawat Sarees`,
      text: `Check out this amazing ${product.name} at Sajawat Sarees! ${product.price?.current ? `Only ₹${product.price.current}` : ''}`,
      url: window.location.href,
    };

    try {
      // Check if native sharing is available
      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        toast.success('Product shared successfully!', {
          duration: 2000
        });
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        toast.info('Product link copied to clipboard!', {
          duration: 3000
        });
      }
    } catch (error) {
      console.error('Error sharing product:', error);

      // Fallback to clipboard if sharing fails
      try {
        await navigator.clipboard.writeText(window.location.href);
        toast.info('Product link copied to clipboard!', {
          duration: 3000
        });
      } catch (clipboardError) {
        console.error('Failed to copy to clipboard:', clipboardError);

        // Final fallback: show error message
        toast.error('Unable to share product. Please copy the URL manually.', {
          duration: 4000
        });
      }
    }
  };

  // Show loading skeleton
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="flex items-center space-x-2 mb-6">
            <div className="h-4 bg-gray-200 rounded w-16"></div>
            <div className="h-4 bg-gray-200 rounded-full w-4"></div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="h-4 bg-gray-200 rounded-full w-4"></div>
            <div className="h-4 bg-gray-200 rounded w-32"></div>
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Image skeleton */}
            <div className="lg:w-2/5">
              <div className="bg-gray-200 aspect-square rounded-lg mb-4 relative">
                <div className="absolute top-4 left-4">
                  <div className="bg-gray-300 h-6 w-16 rounded"></div>
                </div>
              </div>
              <div className="grid grid-cols-5 gap-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className="bg-gray-200 aspect-square rounded"
                  ></div>
                ))}
              </div>
            </div>

            {/* Content skeleton */}
            <div className="lg:w-3/5">
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="flex mb-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className="h-4 w-4 bg-gray-300 rounded-full mr-1"
                  ></div>
                ))}
                <div className="h-4 bg-gray-200 rounded w-24 ml-2"></div>
              </div>

              <div className="mt-6 flex items-center">
                <div className="h-8 bg-gray-300 rounded w-24 mr-3"></div>
                <div className="h-6 bg-gray-200 rounded w-20"></div>
              </div>

              <div className="my-6">
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>

              <div className="h-px bg-gray-200 my-6"></div>

              <div className="mb-4">
                <div className="h-5 bg-gray-200 rounded w-20 mb-3"></div>
                <div className="flex space-x-2">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="h-8 w-10 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>

              <div className="mb-4">
                <div className="h-5 bg-gray-200 rounded w-20 mb-3"></div>
                <div className="flex space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div
                      key={i}
                      className="h-8 w-8 bg-gray-200 rounded-full"
                    ></div>
                  ))}
                </div>
              </div>

              <div className="h-px bg-gray-200 my-6"></div>

              <div className="flex flex-col gap-3 mb-6">
                <div className="flex gap-2">
                  <div className="h-10 bg-gray-300 rounded flex-1"></div>
                  <div className="h-10 bg-gray-300 rounded flex-1"></div>
                </div>
                <div className="flex gap-2">
                  <div className="h-10 bg-gray-200 rounded flex-1"></div>
                  <div className="h-10 w-10 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show product not found message
  if (error || (!isLoading && !product)) {
    return (
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <div className="mb-6">
            <div className="mx-auto w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center">
              <AlertCircle className="h-10 w-10 text-gray-400" />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Product Not Available
          </h2>
          <p className="text-gray-600 mb-6">
            We couldn't find this product in any category. It may have been
            removed or is temporarily unavailable.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link
              to="/products"
              className="px-5 py-2.5 bg-[#000907] text-white rounded-md hover:bg-[#1D1616] transition-colors duration-300"
            >
              Browse All Products
            </Link>
            <Link
              to="/category"
              className="px-5 py-2.5 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-300"
            >
              View Categories
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Calculate discount percentage
  const discountPercentage = product?.price?.discount_percentage || 0;

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-12">

        {/* Responsive Breadcrumb */}
        <nav className="hidden sm:flex mb-6 sm:mb-8 text-sm">
          <div className="flex items-center space-x-2 text-gray-500">
            <Link
              to="/"
              className="hover:text-gray-900 transition-colors duration-200"
            >
              Home
            </Link>
            <ChevronRight className="h-4 w-4" />
            <Link
              to="/category"
              className="hover:text-gray-900 transition-colors duration-200"
            >
              Categories
            </Link>
            <ChevronRight className="h-4 w-4" />
            <Link
              to={`/category/${getCategoryName(product.category).toLowerCase()}`}
              className="hover:text-gray-900 transition-colors duration-200"
            >
              {getCategoryName(product.category)}
            </Link>
            {product.subcategory && (
              <>
                <ChevronRight className="h-4 w-4" />
                <Link
                  to={`/subcategory/${product.subcategory.toLowerCase()}`}
                  className="hover:text-gray-900 transition-colors duration-200"
                >
                  {product.subcategory}
                </Link>
              </>
            )}
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-900 font-medium">
              {product.name}
            </span>
          </div>
        </nav>

        {/* Responsive Main Product Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 mb-12 sm:mb-16">
          {/* Product Image Gallery */}
          <div className="order-1">
            <ProductImageGallery
              productId={product.product_id}
              productName={product.name}
              primaryImages={product.product_images?.map(img => getFullImageUrl(img.image_url)) || []}
              isNew={product.is_new_arrival}
              discountPercentage={discountPercentage}
              showZoomModal={showZoomModal}
              onZoomToggle={() => setShowZoomModal(!showZoomModal)}
            />
          </div>

          {/* Product Info */}
          <div className="order-2 lg:order-2">
            <ProductInfo
              product={convertProductForUI(product)}
              selectedSize={selectedSize}
              selectedColor={selectedColor}
              quantity={quantity}
              isInWishlist={isInWishlist}
              isAddingToWishlist={isAddingToWishlist}
              onSizeSelect={setSelectedSize}
              onColorSelect={setSelectedColor}
              onQuantityChange={handleQuantityChange}
              onAddToCart={handleAddToCart}
              onBuyNow={handleBuyNow}
              onToggleWishlist={handleWishlist}
              onShare={handleShare}
            />
          </div>
        </div>

        {/* Responsive Product Tabs */}
        <div className="mb-12 sm:mb-16">
          <ProductTabs product={convertProductForUI(product)} reviews={reviews} />
        </div>

        {/* Enhanced Customer Reviews */}
        <div className="mb-12 sm:mb-16">
          <CustomerReviews
            productId={product.product_id}
            productName={product.name}
          />
        </div>

        {/* Enhanced Related Products */}
        <div>
          <RelatedProducts
            productId={product.product_id}
            products={relatedProducts.map(convertProductForUI).filter(Boolean)}
            title="You Might Also Like"
            subtitle="Discover more beautiful pieces from our collection"
          />
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
