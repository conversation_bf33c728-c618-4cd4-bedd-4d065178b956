import React, { useState, useEffect } from "react";
import { Maximize, X, ChevronLeft, ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { productService } from "@/services/productService";

interface ProductImage {
  image_url: string;
  alt_text: string;
  caption?: string;
  image_type: 'main' | 'gallery' | 'detail' | 'lifestyle' | 'model' | 'fabric' | 'size_chart';
  sort_order: number;
  source: 'primary' | 'gallery';
}

interface ProductImageGalleryProps {
  productId: string;
  productName: string;
  primaryImages?: string[]; // Fallback for existing components
  isNew?: boolean;
  discountPercentage?: number;
  showZoomModal: boolean;
  onZoomToggle: () => void;
}

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  productId,
  productName,
  primaryImages = [],
  isNew,
  discountPercentage,
  showZoomModal,
  onZoomToggle,
}) => {
  const [allImages, setAllImages] = useState<ProductImage[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use primary images first, then try to fetch gallery images from backend
  useEffect(() => {
    const setupImages = async () => {
      try {
        setLoading(true);

        // If primary images are provided, use them first
        if (primaryImages && primaryImages.length > 0) {
          const primaryImageObjects: ProductImage[] = primaryImages.map((url, index) => ({
            image_url: url,
            alt_text: `${productName} - View ${index + 1}`,
            image_type: 'main' as const,
            sort_order: index,
            source: 'primary' as const
          }));
          setAllImages(primaryImageObjects);
          setLoading(false);
          return;
        }

        // If no primary images, try to fetch gallery images from backend
        if (productId) {
          const response = await productService.getProductGallery(productId);

          if (response.success && response.data.images) {
            setAllImages(response.data.images);
          } else {
            setError('No images available');
            setAllImages([]);
          }
        }
      } catch (err) {
        console.error('Error setting up images:', err);
        setError('Failed to load images');
        setAllImages([]);
      } finally {
        setLoading(false);
      }
    };

    setupImages();
  }, [productId, productName, primaryImages]);

  const selectedImage = allImages[selectedImageIndex];
  const showThumbnails = allImages.length > 1;

  const handleImageSelect = (index: number) => {
    setSelectedImageIndex(index);
  };

  const handlePrevImage = () => {
    setSelectedImageIndex((prev) => (prev > 0 ? prev - 1 : allImages.length - 1));
  };

  const handleNextImage = () => {
    setSelectedImageIndex((prev) => (prev < allImages.length - 1 ? prev + 1 : 0));
  };

  if (loading) {
    return (
      <div className="w-full">
        <div className="aspect-[4/5] w-full bg-gray-200 animate-pulse rounded-lg mb-6"></div>
        <div className="grid grid-cols-4 gap-2">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="aspect-square bg-gray-200 animate-pulse rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error || allImages.length === 0) {
    return (
      <div className="w-full">
        <div className="aspect-[4/5] w-full bg-gray-100 flex items-center justify-center rounded-lg">
          <p className="text-gray-500">No images available</p>
        </div>
      </div>
    );
  }
  return (
    <>
      {/* Enhanced Image zoom modal with navigation */}
      {showZoomModal && selectedImage && (
        <div
          className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-4"
          onClick={onZoomToggle}
        >
          <button
            onClick={onZoomToggle}
            className="absolute top-6 right-6 text-white hover:text-gray-300 z-10"
          >
            <X className="h-8 w-8" />
          </button>

          {/* Navigation arrows in zoom modal */}
          {allImages.length > 1 && (
            <>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handlePrevImage();
                }}
                className="absolute left-6 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10"
              >
                <ChevronLeft className="h-8 w-8" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleNextImage();
                }}
                className="absolute right-6 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10"
              >
                <ChevronRight className="h-8 w-8" />
              </button>
            </>
          )}

          <div className="text-center">
            <img
              src={selectedImage.image_url}
              alt={selectedImage.alt_text}
              className="max-h-[80vh] max-w-[90vw] object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            {selectedImage.caption && (
              <p className="text-white mt-4 text-sm">{selectedImage.caption}</p>
            )}
            <p className="text-gray-300 mt-2 text-xs">
              {selectedImageIndex + 1} of {allImages.length}
            </p>
          </div>
        </div>
      )}

      {/* Modern Main Product Images */}
      <div className="w-full">
        <div className="relative mb-6 bg-gray-50 overflow-hidden">
          {/* Modern Badges */}
          {(isNew || (discountPercentage && discountPercentage > 0)) && (
            <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
              {isNew && (
                <Badge className="bg-gray-900 text-white px-3 py-1 text-xs font-medium">
                  NEW
                </Badge>
              )}
              {discountPercentage && discountPercentage > 0 && (
                <Badge className="bg-red-500 text-white px-3 py-1 text-xs font-medium">
                  {discountPercentage}% OFF
                </Badge>
              )}
            </div>
          )}

          {/* Enhanced Main Image with navigation */}
          <div className="aspect-[4/5] w-full cursor-pointer relative group" onClick={onZoomToggle}>
            <img
              src={selectedImage.image_url}
              alt={selectedImage.alt_text}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />

            {/* Navigation arrows for main image */}
            {allImages.length > 1 && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePrevImage();
                  }}
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300"
                >
                  <ChevronLeft className="h-4 w-4 text-gray-900" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleNextImage();
                  }}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300"
                >
                  <ChevronRight className="h-4 w-4 text-gray-900" />
                </button>
              </>
            )}

            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300 flex items-center justify-center">
              <div className="bg-white/90 p-3 transform scale-0 group-hover:scale-100 transition-all duration-300">
                <Maximize className="h-5 w-5 text-gray-900" />
              </div>
            </div>

            {/* Image counter */}
            {allImages.length > 1 && (
              <div className="absolute bottom-4 right-4 bg-black/60 text-white px-2 py-1 rounded text-xs">
                {selectedImageIndex + 1}/{allImages.length}
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Responsive Thumbnail Images */}
        {showThumbnails && (
          <div className="grid grid-cols-4 gap-2 sm:gap-3">
            {allImages.map((image, index) => {
              const isSelected = selectedImageIndex === index;
              const imageTypeLabels = {
                main: 'Main',
                gallery: 'Gallery',
                detail: 'Detail',
                lifestyle: 'Lifestyle',
                model: 'Model',
                fabric: 'Fabric',
                size_chart: 'Size Chart'
              };

              return (
                <button
                  key={index}
                  onClick={() => handleImageSelect(index)}
                  className={`aspect-square bg-gray-50 overflow-hidden transition-all duration-200 relative group ${
                    isSelected
                      ? "ring-1 sm:ring-2 ring-gray-900"
                      : "hover:ring-1 hover:ring-gray-400"
                  }`}
                  title={image.alt_text || `View ${imageTypeLabels[image.image_type]} image`}
                >
                  <img
                    src={image.image_url}
                    alt={image.alt_text}
                    className="w-full h-full object-cover"
                  />

                  {/* Image type indicator */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs px-1 py-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
                    {imageTypeLabels[image.image_type]}
                  </div>

                  {/* Selected indicator */}
                  {isSelected && (
                    <div className="absolute inset-0 bg-gray-900/20 flex items-center justify-center">
                      <div className="w-3 h-3 bg-gray-900 rounded-full"></div>
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        )}
      </div>
    </>
  );
};

export default ProductImageGallery;
