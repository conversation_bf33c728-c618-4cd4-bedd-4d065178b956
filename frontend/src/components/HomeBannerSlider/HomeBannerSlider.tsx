import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, EffectFade, A11y } from 'swiper/modules';
import {
  selectKurtiBanners,
  selectKurtiBannersLoading,
  fetchKurtiBanners,
  type Banner,
} from '@/store/slices/banners/bannerSlice';
import { useAppDispatch, useAppSelector } from '@/hooks/hooks';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

// Import component styles
import './HomeBannerSlider.css';

interface HomeBannerSliderProps {
  autoplay?: boolean;
  delay?: number;
  pauseOnHover?: boolean;
  showNavigation?: boolean;
  showPagination?: boolean;
}

const HomeBannerSlider: React.FC<HomeBannerSliderProps> = ({
  autoplay = true,
  delay = 5000,
  pauseOnHover = true,
  showNavigation = true,
  showPagination = true,
}) => {
  const dispatch = useAppDispatch();
  const banners = useAppSelector(selectKurtiBanners);
  const loading = useAppSelector(selectKurtiBannersLoading);
  const [autoplayEnabled, setAutoplayEnabled] = useState(autoplay);

  useEffect(() => {
    if (banners.length === 0 && !loading) {
      dispatch(fetchKurtiBanners());
    }
  }, [banners.length, loading, dispatch]);

  // Handle pause on hover if enabled
  const handleMouseEnter = () => {
    if (pauseOnHover && autoplay) {
      setAutoplayEnabled(false);
    }
  };

  const handleMouseLeave = () => {
    if (pauseOnHover && autoplay) {
      setAutoplayEnabled(true);
    }
  };



  if (loading) {
    return (
      <div className="w-full h-screen md:h-[600px] lg:h-[650px] xl:h-[700px] mx-auto overflow-hidden bg-gradient-to-r from-gray-200 to-gray-300 animate-pulse flex flex-col items-center justify-center">
        <div className="w-16 h-16 border-4 border-[#1D1616] border-t-white rounded-full animate-spin mb-4"></div>
        <div className="text-[#1D1616] font-medium text-lg">Loading banners...</div>
        <div className="text-gray-500 text-sm mt-2">Please wait while we prepare your experience</div>
      </div>
    );
  }

  if (banners.length === 0) {
    return (
      <div className="w-full h-screen md:h-[600px] lg:h-[650px] xl:h-[700px] mx-auto overflow-hidden bg-gray-100 flex flex-col items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <div className="text-gray-700 font-medium text-lg">No banners available</div>
        <div className="text-gray-500 text-sm mt-2 max-w-md text-center px-4">
          We couldn't find any banners to display. Please check back later or contact the administrator.
        </div>
        <button
          onClick={() => dispatch(fetchKurtiBanners())}
          className="mt-5 px-4 py-1.5 bg-[#1D1616] text-white rounded-lg hover:bg-[#000907] transition-colors duration-300 text-sm"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div
      className="relative w-full overflow-hidden h-full"
      style={{ zIndex: 10 }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Container with responsive height - now 100% width and full height on mobile */}
      <div className="w-full h-screen md:h-[600px] lg:h-[650px] xl:h-[700px] overflow-hidden banner-container">
        <Swiper
          modules={[Navigation, Pagination, Autoplay, EffectFade, A11y]}
          spaceBetween={0}
          slidesPerView={1}
          effect="fade"
          navigation={showNavigation}
          pagination={showPagination ? {
            clickable: true,
            dynamicBullets: true,
            renderBullet: (index: number, className: string) => {
              return `<span class="${className}" role="button" aria-label="Go to slide ${index + 1}"></span>`;
            },
          } : false}
          autoplay={autoplayEnabled ? {
            delay: delay,
            disableOnInteraction: false,
            pauseOnMouseEnter: pauseOnHover,
          } : false}
          loop={true}
          a11y={{
            prevSlideMessage: 'Previous slide',
            nextSlideMessage: 'Next slide',
            firstSlideMessage: 'This is the first slide',
            lastSlideMessage: 'This is the last slide',
            paginationBulletMessage: 'Go to slide {{index}}',
          }}
          initialSlide={0}
          className="w-full h-full"
        >
          {banners.map((banner: Banner) => (
            <SwiperSlide key={banner._id || banner.id} className="relative h-full">
              {/* Desktop Image (hidden on small screens, visible on md and up) */}
              <div className="hidden md:block w-full h-full">
                <img
                  src={banner.desktopImage}
                  alt={banner.altText}
                  className="w-full h-full object-cover object-center transition-all duration-700 hover:scale-105"
                  style={{ minHeight: '100%' }}
                  loading="lazy"
                  fetchPriority="high"
                />
                {/* Gradient overlay for better text visibility - different for Summer Collection */}
                <div className={`absolute inset-0 ${(banner._id || banner.id) === '1' ? 'bg-gradient-to-b from-black/60 via-transparent to-black/40' : 'bg-gradient-to-t from-black/60 via-transparent to-black/30'} opacity-75 banner-gradient-overlay`}></div>
              </div>

              {/* Mobile Image (visible on small screens, hidden on md and up) */}
              <div className="block md:hidden w-full h-full">
                <img
                  src={banner.mobileImage}
                  alt={banner.altText}
                  className="w-full h-full object-cover object-center transition-all duration-700 hover:scale-105"
                  style={{ minHeight: '100vh' }}
                  loading="lazy"
                  fetchPriority="high"
                />
                {/* Gradient overlay for better text visibility - different for Summer Collection */}
                <div className={`absolute inset-0 ${(banner._id || banner.id) === '1' ? 'bg-gradient-to-b from-black/70 via-transparent to-black/40' : 'bg-gradient-to-t from-black/70 via-transparent to-black/30'} opacity-75 banner-gradient-overlay`}></div>
              </div>

              {/* Banner Content with animation - consistent positioning for all slides */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white p-4 max-w-xl mx-auto banner-content">
                  <span className="inline-block px-3 py-1 bg-[#6d3d3d]/60 text-white text-xs uppercase tracking-wider rounded-full mb-4 backdrop-blur-sm">
                    Featured
                  </span>
                  <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 drop-shadow-lg leading-tight">
                    {banner.title}
                  </h2>
                  {(banner.ctaText || (banner as any).buttonText) && (
                    <Link
                      to={banner.ctaLink || (banner as any).link || '#'}
                      className="inline-block bg-transparent border-2 border-white hover:bg-white/20 text-white font-medium py-2 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 backdrop-blur-sm banner-button group text-sm"
                    >
                      <span className="flex items-center">
                        {banner.ctaText || (banner as any).buttonText}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </span>
                    </Link>
                  )}
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Navigation arrows are handled by Swiper's built-in navigation */}

      {/* Styles moved to HomeBannerSlider.css */}
    </div>
  );
};

export default HomeBannerSlider;
