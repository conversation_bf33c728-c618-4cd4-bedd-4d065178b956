import React, { useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { ArrowR<PERSON>, Star } from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/hooks/hooks";
import {
  fetchBestsellerProducts,
  selectBestsellerProducts,
  selectBestsellerLoading,
  selectBestsellerError,
  type Product
} from "@/store/slices/products/productSlice";
import { getCategoryName } from "@/utils/productUtils";
import { getFullImageUrl } from "@/utils/imageUtils";

interface BestsellersProps {
  title?: string;
  subtitle?: string;
  limit?: number;
}

// Helper functions
const formatPrice = (price: number, currency: string = 'USD') => {
  if (currency === 'USD') {
    return `$${price.toFixed(2)}`;
  }
  return `₹${(price * 75).toFixed(0)}`;
};

const getPrimaryImage = (product: Product) => {
  const primaryImage = product.product_images.find(img => img.is_primary);
  return primaryImage ? getFullImageUrl(primaryImage.image_url) : '';
};

const getDiscountPercentage = (original: number, current: number) => {
  return Math.round(((original - current) / original) * 100);
};

const Bestsellers: React.FC<BestsellersProps> = ({
  title = "Bestsellers",
  subtitle = "Our most popular products loved by customers",
  limit = 8,
}) => {
  const dispatch = useAppDispatch();
  const products = useAppSelector(selectBestsellerProducts);
  const loading = useAppSelector(selectBestsellerLoading);
  const error = useAppSelector(selectBestsellerError);

  // Fetch bestseller products
  useEffect(() => {
    dispatch(fetchBestsellerProducts(limit));
  }, [dispatch, limit]);

  // Debug logging
  useEffect(() => {
    console.log('Bestsellers component - products:', products.length, 'loading:', loading, 'error:', error);
  }, [products, loading, error]);

  // Render stars for rating
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star
        key={index}
        size={14}
        className={`${
          index < Math.floor(rating)
            ? "text-yellow-500 fill-yellow-500"
            : index < rating
            ? "text-yellow-500 fill-yellow-500 opacity-50"
            : "text-gray-300"
        }`}
      />
    ));
  };



  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section className="py-16 bg-gradient-to-br from-yellow-50 to-amber-100">
      <div className="container mx-auto px-4 sm:px-6">
        {/* Section Header */}
        <div className="text-center mb-12">
          <motion.span
            className="inline-block text-sm uppercase tracking-widest text-yellow-600 mb-3 font-semibold"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Customer Favorites
          </motion.span>

          <motion.h2
            className="text-3xl md:text-4xl font-bold text-[#000907] mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            {title}
          </motion.h2>

          <motion.div
            className="h-1 w-20 bg-yellow-600 mx-auto mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
          />

          <motion.p
            className="text-zinc-600 max-w-2xl mx-auto text-lg"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="w-12 h-12 border-4 border-yellow-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-yellow-600 font-medium">Loading bestsellers...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <div className="text-red-500 mb-4">{error || "Failed to load bestsellers"}</div>
            <button
              onClick={() => dispatch(fetchBestsellerProducts(limit))}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Products Grid */}
        {!loading && !error && products.length > 0 && (
          <motion.div
            className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
          >
            {products.map((product) => {
              const primaryImage = getPrimaryImage(product);
              const hasDiscount = product.price.original > product.price.current;
              const discountPercentage = hasDiscount ? getDiscountPercentage(product.price.original, product.price.current) : 0;

              return (
                <motion.div
                  key={product.product_id}
                  className="group relative bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300"
                  variants={itemVariants}
                  whileHover={{ y: -5 }}
                >
                  <Link to={`/product/${product.slug}`} className="block">
                    <div className="relative overflow-hidden aspect-[3/4]">
                      {/* Product Image */}
                      <motion.img
                        src={primaryImage}
                        alt={product.name}
                        className="w-full h-full object-cover"
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.6 }}
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder-product.jpg';
                        }}
                      />

                      {/* Badges */}
                      <div className="absolute top-2 left-2 flex flex-col gap-1">
                        {product.is_bestseller && (
                          <span className="bg-yellow-500 text-white text-[10px] xs:text-xs px-2 xs:px-3 py-0.5 xs:py-1 rounded-full font-medium shadow-sm">
                            Bestseller
                          </span>
                        )}
                        {hasDiscount && (
                          <span className="bg-red-500 text-white text-[10px] xs:text-xs px-2 xs:px-3 py-0.5 xs:py-1 rounded-full font-medium shadow-sm">
                            -{discountPercentage}%
                          </span>
                        )}
                      </div>

                      {/* Quick Shop Button */}
                      <div className="absolute bottom-0 left-0 right-0 p-2 sm:p-4 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <button className="w-full bg-white text-[#000907] py-1 sm:py-2 rounded-md text-xs sm:text-sm font-medium hover:bg-yellow-600 hover:text-white transition-colors duration-300 shadow-sm">
                          Quick Shop
                        </button>
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="p-2 sm:p-4">
                      <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between mb-1">
                        <span className="text-[10px] xs:text-xs text-zinc-500 uppercase tracking-wider">
                          {getCategoryName(product.category)}
                        </span>
                        <div className="flex items-center gap-1 mt-1 xs:mt-0">
                          {renderStars(product.rating.average_rating)}
                          <span className="text-[10px] xs:text-xs text-zinc-500 ml-1">
                            ({product.rating.reviews_count})
                          </span>
                        </div>
                      </div>

                      <h3 className="font-medium text-[#000907] text-sm sm:text-base group-hover:text-yellow-700 transition-colors duration-300 mb-1 truncate">
                        {product.name}
                      </h3>

                      <div className="flex flex-wrap items-center gap-1 sm:gap-2">
                        <span className="font-semibold text-[#000907] text-sm sm:text-base">
                          {formatPrice(product.price.current, product.price.currency)}
                        </span>
                        {hasDiscount && (
                          <span className="text-xs text-zinc-500 line-through">
                            {formatPrice(product.price.original, product.price.currency)}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                </motion.div>
              );
            })}
          </motion.div>
        )}

        {/* Empty State */}
        {!loading && !error && products.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">No bestseller products found</div>
          </div>
        )}

        {/* View All Link */}
        {!loading && !error && products.length > 0 && (
          <div className="text-center mt-12">
            <Link to="/shop?filter=bestsellers">
              <motion.button
                className="inline-flex items-center gap-2 px-6 py-3 border-2 border-yellow-600 text-yellow-600 font-medium rounded-md hover:bg-yellow-600 hover:text-white transition-all duration-300"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                View All Bestsellers
                <motion.span
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <ArrowRight size={18} />
                </motion.span>
              </motion.button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default Bestsellers;
