import React, { useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { ArrowR<PERSON>, Star } from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/hooks/hooks";
import {
  fetchLehengaProducts,
  selectLehengaProducts,
  selectLehengaProductsLoading,
  selectLehengaProductsError,
  type Product
} from "@/store/slices/products/productSlice";

interface LehengaCollectionProps {
  title?: string;
  subtitle?: string;
  limit?: number;
}

// Helper functions
const formatPrice = (price: number, currency: string = 'USD') => {
  if (currency === 'USD') {
    return `$${price.toFixed(2)}`;
  }
  return `₹${(price * 75).toFixed(0)}`;
};

const getPrimaryImage = (product: Product) => {
  const primaryImage = product.product_images.find(img => img.is_primary);
  return primaryImage ? `http://localhost:5000${primaryImage.image_url}` : '';
};

const getDiscountPercentage = (original: number, current: number) => {
  return Math.round(((original - current) / original) * 100);
};

const LehengaCollection: React.FC<LehengaCollectionProps> = ({
  title = "Lehenga Collections",
  subtitle = "Discover our stunning collection of premium lehengas for special occasions",
  limit = 8,
}) => {
  const dispatch = useAppDispatch();
  const products = useAppSelector(selectLehengaProducts);
  const loading = useAppSelector(selectLehengaProductsLoading);
  const error = useAppSelector(selectLehengaProductsError);

  // Fetch lehenga products on component mount
  useEffect(() => {
    dispatch(fetchLehengaProducts(limit));
  }, [dispatch, limit]);

  // Render stars for rating
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star
        key={index}
        size={14}
        className={`${
          index < Math.floor(rating)
            ? "text-yellow-500 fill-yellow-500"
            : index < rating
            ? "text-yellow-500 fill-yellow-500 opacity-50"
            : "text-gray-300"
        }`}
      />
    ));
  };



  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section className="py-16 bg-gradient-to-br from-purple-50 to-violet-100">
      <div className="container mx-auto px-4 sm:px-6">
        {/* Section Header */}
        <div className="text-center mb-12">
          <motion.span
            className="inline-block text-sm uppercase tracking-widest text-purple-600 mb-3 font-semibold"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Bridal Collection
          </motion.span>

          <motion.h2
            className="text-3xl md:text-4xl font-bold text-[#000907] mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            {title}
          </motion.h2>

          <motion.div
            className="h-1 w-20 bg-purple-600 mx-auto mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
          />

          <motion.p
            className="text-zinc-600 max-w-2xl mx-auto text-lg"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-purple-600 font-medium">
              Loading lehenga collections...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <div className="text-red-500 mb-4">
              {error || "Failed to load lehenga collections"}
            </div>
            <button
              onClick={() => dispatch(fetchLehengaProducts(limit))}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Products Grid */}
        {!loading && !error && products.length > 0 && (
          <motion.div
            className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
          >
            {products.slice(0, limit).map((product) => {
              const primaryImage = getPrimaryImage(product);
              const hasDiscount = product.price.original > product.price.current;
              const discountPercentage = hasDiscount ? getDiscountPercentage(product.price.original, product.price.current) : 0;

              return (
                <motion.div
                  key={product.product_id}
                  className="group relative bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300"
                  variants={itemVariants}
                  whileHover={{ y: -5 }}
                >
                  <Link to={`/product/${product.slug}`} className="block">
                    <div className="relative overflow-hidden aspect-[3/4]">
                      {/* Product Image */}
                      <motion.img
                        src={primaryImage}
                        alt={product.name}
                        className="w-full h-full object-cover"
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.6 }}
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder-product.jpg';
                        }}
                      />

                      {/* Badges */}
                      <div className="absolute top-2 left-2 flex flex-col gap-1">
                        {(typeof product.category === 'string' ? product.category : product.category?.name) === "Lehengas" && (
                          <span className="bg-purple-500 text-white text-[10px] xs:text-xs px-2 xs:px-3 py-0.5 xs:py-1 rounded-full font-medium shadow-sm">
                            Lehenga
                          </span>
                        )}
                        {hasDiscount && (
                          <span className="bg-red-500 text-white text-[10px] xs:text-xs px-2 xs:px-3 py-0.5 xs:py-1 rounded-full font-medium shadow-sm">
                            -{discountPercentage}%
                          </span>
                        )}
                      </div>

                      {/* Quick Shop Button */}
                      <div className="absolute bottom-0 left-0 right-0 p-2 sm:p-4 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <button className="w-full bg-white text-[#000907] py-1 sm:py-2 rounded-md text-xs sm:text-sm font-medium hover:bg-purple-600 hover:text-white transition-colors duration-300 shadow-sm">
                          Quick Shop
                        </button>
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="p-2 sm:p-4">
                      <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between mb-1">
                        <span className="text-[10px] xs:text-xs text-zinc-500 uppercase tracking-wider">
                          {typeof product.category === 'string' ? product.category : product.category?.name || 'Category'}
                        </span>
                        <div className="flex items-center gap-1 mt-1 xs:mt-0">
                          {renderStars(product.rating.average_rating)}
                          <span className="text-[10px] xs:text-xs text-zinc-500 ml-1">
                            ({product.rating.reviews_count})
                          </span>
                        </div>
                      </div>

                      <h3 className="font-medium text-[#000907] text-sm sm:text-base group-hover:text-purple-700 transition-colors duration-300 mb-1 truncate">
                        {product.name}
                      </h3>

                      <div className="flex flex-wrap items-center gap-1 sm:gap-2">
                        <span className="font-semibold text-[#000907] text-sm sm:text-base">
                          {formatPrice(product.price.current, product.price.currency)}
                        </span>
                        {hasDiscount && (
                          <span className="text-xs text-zinc-500 line-through">
                            {formatPrice(product.price.original, product.price.currency)}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                </motion.div>
              );
            })}
          </motion.div>
        )}

        {/* Empty State */}
        {!loading && !error && products.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">No lehenga products found</div>
          </div>
        )}

        {/* View All Link */}
        {!loading && !error && products.length > 0 && (
          <div className="text-center mt-12">
            <Link to="/shop?category=Lehengas">
              <motion.button
                className="inline-flex items-center gap-2 px-6 py-3 border-2 border-purple-600 text-purple-600 font-medium rounded-md hover:bg-purple-600 hover:text-white transition-all duration-300"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                View All Lehenga Collections
                <motion.span
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <ArrowRight size={18} />
                </motion.span>
              </motion.button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default LehengaCollection;
