import React, { useState, useRef, useEffect } from "react";
import { motion, useInView, useAnimation } from "framer-motion";
import { Link } from "react-router-dom";
import { ChevronLeft, ChevronRight, ShoppingBag, Heart, Eye, Star } from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/hooks/hooks";
import {
  fetchKurtiProducts,
  fetchLehengaProducts,
  selectKurtiProducts,
  selectLehengaProducts,
  selectKurtiProductsLoading,
  selectLehengaProductsLoading,
  selectKurtiProductsError,
  selectLehengaProductsError,
  type Product
} from "@/store/slices/products/productSlice";
import { getCategoryName } from "@/utils/productUtils";

interface ProductSliderProps {
  title?: string;
  subtitle?: string;
  category?: 'kurti' | 'lehenga';
  limit?: number;
}

// Helper functions
const formatPrice = (price: number, currency: string = 'USD') => {
  if (currency === 'USD') {
    return `$${price.toFixed(2)}`;
  }
  return `₹${(price * 75).toFixed(0)}`;
};

const getPrimaryImage = (product: Product) => {
  const primaryImage = product.product_images.find(img => img.is_primary);
  return primaryImage ? `http://localhost:5000${primaryImage.image_url}` : '';
};

const getDiscountPercentage = (original: number, current: number) => {
  return Math.round(((original - current) / original) * 100);
};

const ProductSlider: React.FC<ProductSliderProps> = ({
  title = "Featured Products",
  subtitle = "Discover our handpicked collection of premium ethnic wear",
  category = 'kurti',
  limit = 8,
}) => {
  const dispatch = useAppDispatch();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(4);
  const [hoveredProduct, setHoveredProduct] = useState<string | null>(null);
  const sliderRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);

  const isInView = useInView(titleRef, { once: true, amount: 0.3, layoutEffect: false });
  const controls = useAnimation();

  // Redux state based on category
  const kurtiProducts = useAppSelector(selectKurtiProducts);
  const kurtiLoading = useAppSelector(selectKurtiProductsLoading);
  const kurtiError = useAppSelector(selectKurtiProductsError);

  const lehengaProducts = useAppSelector(selectLehengaProducts);
  const lehengaLoading = useAppSelector(selectLehengaProductsLoading);
  const lehengaError = useAppSelector(selectLehengaProductsError);

  // Get products based on category
  const products = category === 'lehenga' ? lehengaProducts : kurtiProducts;
  const loading = category === 'lehenga' ? lehengaLoading : kurtiLoading;
  const error = category === 'lehenga' ? lehengaError : kurtiError;

  // Fetch products based on category
  useEffect(() => {
    if (category === 'lehenga') {
      dispatch(fetchLehengaProducts(limit));
    } else {
      dispatch(fetchKurtiProducts(limit));
    }
  }, [dispatch, category, limit]);

  // Debug logging
  useEffect(() => {
    console.log('ProductSlider - category:', category, 'products:', products.length, 'loading:', loading);
  }, [category, products, loading]);

  useEffect(() => {
    if (isInView) {
      controls.start("visible");
    }
  }, [isInView, controls]);

  // Handle responsive slider
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setItemsPerView(1);
      } else if (window.innerWidth < 768) {
        setItemsPerView(2);
      } else if (window.innerWidth < 1024) {
        setItemsPerView(3);
      } else {
        setItemsPerView(4);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Calculate max index
  const maxIndex = Math.max(0, products.length - itemsPerView);

  // Navigation functions
  const nextSlide = () => {
    setCurrentIndex(prev => Math.min(prev + 1, maxIndex));
  };

  const prevSlide = () => {
    setCurrentIndex(prev => Math.max(prev - 1, 0));
  };

  // Use products directly (limit applied in slice)
  const displayProducts = products.slice(0, limit);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.1, 0.25, 1.0]
      }
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="w-full py-20 bg-white relative">
        <div className="w-full px-4 sm:px-6">
          <div className="text-center mb-12 max-w-6xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-[#000907] mb-4">{title}</h2>
            <p className="text-zinc-600 max-w-2xl mx-auto text-lg">{subtitle}</p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 max-w-[1400px] mx-auto">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 aspect-[3/4] rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full py-20 bg-white relative">
        <div className="w-full px-4 sm:px-6 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-[#000907] mb-4">{title}</h2>
          <p className="text-red-500 mb-4">Failed to load products. Please try again later.</p>
          <button
            onClick={() => {
              if (category === 'lehenga') {
                dispatch(fetchLehengaProducts(limit));
              } else {
                dispatch(fetchKurtiProducts(limit));
              }
            }}
            className="px-6 py-2 bg-[#000907] text-white rounded-md hover:bg-[#000907]/90 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // No products state
  if (!displayProducts || displayProducts.length === 0) {
    return (
      <div className="w-full py-20 bg-white relative">
        <div className="w-full px-4 sm:px-6 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-[#000907] mb-4">{title}</h2>
          <p className="text-gray-500">No products available at the moment.</p>
        </div>
      </div>
    );
  }

  // Product card component
  const ProductCard = ({ product, index }: { product: Product, index: number }) => {
    const primaryImage = getPrimaryImage(product);
    const hasDiscount = product.price.original > product.price.current;
    const discountPercentage = hasDiscount ? getDiscountPercentage(product.price.original, product.price.current) : 0;

    return (
      <motion.div
        className="group relative flex-shrink-0 w-full sm:w-1/2 md:w-1/3 lg:w-1/4 px-3"
        variants={itemVariants}
        onMouseEnter={() => setHoveredProduct(product.product_id)}
        onMouseLeave={() => setHoveredProduct(null)}
      >
        <Link to={`/product/${product.slug}`} className="block">
          <div className="relative overflow-hidden rounded-lg bg-gray-100 aspect-[3/4] mb-4 shadow-sm">
            {/* Product Image */}
            <motion.div
              className="w-full h-full"
              animate={{
                scale: hoveredProduct === product.product_id ? 1.05 : 1
              }}
              transition={{ duration: 0.6, ease: [0.25, 0.1, 0.25, 1.0] }}
            >
              <img
                src={primaryImage}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/placeholder-product.jpg';
                }}
              />
            </motion.div>

            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2 z-10">
              {product.is_new_arrival && (
                <span className="bg-[#000907] text-white text-xs px-3 py-1 rounded-full font-medium tracking-wide shadow-sm">New</span>
              )}
              {hasDiscount && (
                <span className="bg-red-500 text-white text-xs px-3 py-1 rounded-full font-medium tracking-wide shadow-sm">-{discountPercentage}%</span>
              )}
            </div>

            {/* Action Buttons */}
            <motion.div
              className="absolute right-3 top-3 flex flex-col gap-2 z-10"
              initial={{ opacity: 0, x: 20 }}
              animate={{
                opacity: hoveredProduct === product.product_id ? 1 : 0,
                x: hoveredProduct === product.product_id ? 0 : 20
              }}
              transition={{ duration: 0.3 }}
            >
              <motion.button
                className="w-9 h-9 rounded-full bg-white shadow-md flex items-center justify-center text-zinc-700 hover:text-[#000907] hover:scale-110 transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <Heart size={18} />
              </motion.button>
              <motion.button
                className="w-9 h-9 rounded-full bg-white shadow-md flex items-center justify-center text-zinc-700 hover:text-[#000907] hover:scale-110 transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <Eye size={18} />
              </motion.button>
            </motion.div>

            {/* Quick Add Button */}
            <motion.div
              className="absolute bottom-0 left-0 w-full p-3 z-10"
              initial={{ opacity: 0, y: 20 }}
              animate={{
                opacity: hoveredProduct === product.product_id ? 1 : 0,
                y: hoveredProduct === product.product_id ? 0 : 20
              }}
              transition={{ duration: 0.3 }}
            >
              <motion.button
                className="w-full bg-white text-[#000907] py-3 rounded-md font-medium flex items-center justify-center gap-2 hover:bg-[#000907] hover:text-white transition-all duration-300 shadow-md"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                <ShoppingBag size={16} />
                <span>Add to Bag</span>
              </motion.button>
            </motion.div>

            {/* Overlay on hover */}
            <motion.div
              className="absolute inset-0 bg-black/10"
              initial={{ opacity: 0 }}
              animate={{ opacity: hoveredProduct === product.product_id ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            />
          </div>

          {/* Product Info */}
          <div className="space-y-2">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-xs text-zinc-500 uppercase tracking-wider">
                  {getCategoryName(product.category)}
                </p>
                <h3 className="font-medium text-[#000907] group-hover:text-zinc-700 transition-all duration-300">{product.name}</h3>
              </div>

              <div className="text-right">
                <div className="flex items-center gap-1">
                  <Star size={14} className="text-yellow-500 fill-yellow-500" />
                  <span className="text-sm font-medium">{product.rating.average_rating.toFixed(1)}</span>
                  <span className="text-xs text-zinc-500">({product.rating.reviews_count})</span>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <p className="text-sm font-semibold">{formatPrice(product.price.current, product.price.currency)}</p>
                {hasDiscount && (
                  <p className="text-xs text-zinc-500 line-through">{formatPrice(product.price.original, product.price.currency)}</p>
                )}
              </div>

              {product.colors && product.colors.length > 0 && (
                <div className="flex gap-1">
                  {product.colors.slice(0, 3).map((color, i) => (
                    <div
                      key={i}
                      className="w-3 h-3 rounded-full border border-gray-200"
                      style={{ backgroundColor: color.color_hex }}
                      title={color.color_name}
                    />
                  ))}
                  {product.colors.length > 3 && (
                    <span className="text-xs text-zinc-500">+{product.colors.length - 3}</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </Link>
      </motion.div>
    );
  };

  return (
    <div className="w-full py-20 bg-white relative">
      <div className="w-full px-4 sm:px-6">
        {/* Section Header */}
        <div className="text-center mb-12 max-w-6xl mx-auto">
          <motion.span
            className="inline-block text-sm uppercase tracking-widest text-zinc-500 mb-3"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Our Collection
          </motion.span>

          <motion.h2
            ref={titleRef}
            className="text-4xl md:text-5xl font-bold text-[#000907] mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            {title}
          </motion.h2>

          <motion.div
            className="h-1 w-20 bg-[#000907] mx-auto mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
          />

          <motion.p
            className="text-zinc-600 max-w-2xl mx-auto text-lg"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Product Slider */}
        <div className="relative max-w-[1400px] mx-auto">
          {/* Navigation Buttons */}
          <motion.div
            className="absolute top-1/2 left-0 -translate-y-1/2 -translate-x-5 z-10"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
          >
            <motion.button
              onClick={prevSlide}
              disabled={currentIndex === 0}
              className={`p-3 rounded-full bg-white shadow-lg text-[#000907] ${currentIndex === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-[#000907] hover:text-white'} transition-all duration-300`}
              whileHover={currentIndex !== 0 ? { scale: 1.1, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" } : {}}
              whileTap={currentIndex !== 0 ? { scale: 0.95 } : {}}
            >
              <ChevronLeft size={24} strokeWidth={1.5} />
            </motion.button>
          </motion.div>

          <motion.div
            className="absolute top-1/2 right-0 -translate-y-1/2 translate-x-5 z-10"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
          >
            <motion.button
              onClick={nextSlide}
              disabled={currentIndex === maxIndex}
              className={`p-3 rounded-full bg-white shadow-lg text-[#000907] ${currentIndex === maxIndex ? 'opacity-50 cursor-not-allowed' : 'hover:bg-[#000907] hover:text-white'} transition-all duration-300`}
              whileHover={currentIndex !== maxIndex ? { scale: 1.1, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" } : {}}
              whileTap={currentIndex !== maxIndex ? { scale: 0.95 } : {}}
            >
              <ChevronRight size={24} strokeWidth={1.5} />
            </motion.button>
          </motion.div>

          {/* Slider Container */}
          <div className="overflow-hidden px-2">
            <motion.div
              ref={sliderRef}
              className="flex"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              style={{ x: `-${currentIndex * (100 / itemsPerView)}%` }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              {displayProducts.map((product, index) => (
                <ProductCard key={product.product_id} product={product} index={index} />
              ))}
            </motion.div>
          </div>

          {/* Pagination Dots */}
          <div className="flex justify-center mt-8 gap-2">
            {Array.from({ length: Math.ceil(displayProducts.length / itemsPerView) }).map((_, index) => (
              <motion.button
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === Math.floor(currentIndex / itemsPerView)
                    ? 'bg-[#000907] w-6'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
                onClick={() => setCurrentIndex(index * itemsPerView)}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
              />
            ))}
          </div>
        </div>

        {/* View All Link */}
        <motion.div
          className="text-center mt-12 max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
        >
          <Link to="/shop">
            <motion.button
              className="inline-flex items-center gap-2 px-8 py-3 border-2 border-[#000907] text-[#000907] font-medium rounded-md hover:bg-[#000907] hover:text-white transition-all duration-300"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
            >
              View All Products
              <motion.span
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <ChevronRight size={18} />
              </motion.span>
            </motion.button>
          </Link>
        </motion.div>
      </div>
    </div>
  );
};

export default ProductSlider;
