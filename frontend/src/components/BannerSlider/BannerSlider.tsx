import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import {
  Navigation,
  Pagination,
  Autoplay,
  EffectFade,
  A11y,
} from "swiper/modules";
// import { useAppDispatch, useAppSelector } from "@/hooks/hooks";
import { useCategorySliderBanners } from "@/hooks/useBanners";
import type { Banner } from "@/store/slices/banners/bannerSlice";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/effect-fade";

// Import component styles - reusing the same styles as HomeBannerSlider
import "@/components/HomeBannerSlider/HomeBannerSlider.css";

// Generic banner interface
export interface BannerImage {
  id: string;
  title: string;
  subtitle?: string;
  desktopImage?: string;
  mobileImage?: string;
  desktopBanner?: string; // For backward compatibility
  mobileBanner?: string; // For backward compatibility
  link: string;
  buttonText?: string;
  isActive?: boolean;
  category?: string;
  altText?: string;
}

interface BannerSliderProps {
  banners?: BannerImage[]; // Accept banners as props (for backward compatibility)
  loading?: boolean; // Accept loading state as props (for backward compatibility)
  bannerType?: string; // Type of banner for display (e.g., "Kurti", "Lehenga", "Saree")
  category?: string; // Category to filter banners by
  autoplay?: boolean;
  delay?: number;
  pauseOnHover?: boolean;
  showNavigation?: boolean;
  showPagination?: boolean;
}

const BannerSlider: React.FC<BannerSliderProps> = ({
  banners: propBanners = [],
  loading: propLoading = false,
  bannerType = "Collection",
  category,
  autoplay = true,
  delay = 5000,
  pauseOnHover = true,
  showNavigation = true,
  showPagination = true,
}) => {
  // Use dynamic banners if no props provided
  const {
    banners: dynamicBanners,
    loading: dynamicLoading,
    handleBannerClick,
    // usingFallback
  } = useCategorySliderBanners(category);

  // Use prop banners if provided, otherwise use dynamic banners
  const banners = propBanners.length > 0 ? propBanners : dynamicBanners;
  const loading = propLoading || dynamicLoading;

  const [autoplayEnabled, setAutoplayEnabled] = useState(autoplay);
  const [shuffledBanners, setShuffledBanners] = useState<(BannerImage | Banner)[]>([]);

  // Shuffle banners on component mount for randomness
  useEffect(() => {
    if (banners.length > 0) {
      const shuffled = [...banners].sort(() => Math.random() - 0.5);
      setShuffledBanners(shuffled);
    }
  }, [banners]);

  // Handle pause on hover if enabled
  const handleMouseEnter = () => {
    if (pauseOnHover && autoplay) {
      setAutoplayEnabled(false);
    }
  };

  const handleMouseLeave = () => {
    if (pauseOnHover && autoplay) {
      setAutoplayEnabled(true);
    }
  };

  if (loading) {
    return (
      <div className="w-full h-screen md:h-[600px] lg:h-[650px] xl:h-[700px] mx-auto overflow-hidden bg-gradient-to-r from-gray-200 to-gray-300 animate-pulse flex flex-col items-center justify-center">
        <div className="w-16 h-16 border-4 border-[#000000] border-t-white rounded-full animate-spin mb-4"></div>
        <div className="text-[#000000] font-medium text-lg">
          Loading {bannerType} banners...
        </div>
        <div className="text-gray-500 text-sm mt-2">
          Please wait while we prepare your experience
        </div>
      </div>
    );
  }

  if (shuffledBanners.length === 0) {
    return (
      <div className="w-full h-screen md:h-[600px] lg:h-[650px] xl:h-[700px] mx-auto overflow-hidden bg-gray-100 flex flex-col items-center justify-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-16 w-16 text-gray-400 mb-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
        <div className="text-gray-700 font-medium text-lg">
          No {bannerType} banners available
        </div>
        <div className="text-gray-500 text-sm mt-2 max-w-md text-center px-4">
          We couldn't find any {bannerType.toLowerCase()} banners to display. Please check back later.
        </div>
      </div>
    );
  }

  return (
    <div
      className="relative w-full overflow-hidden h-full"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Container with responsive height - now 100% width and full height on mobile */}
      <div className="w-full h-screen md:h-[600px] lg:h-[650px] xl:h-[700px] mx-auto overflow-hidden banner-container">
        <Swiper
          modules={[Navigation, Pagination, Autoplay, EffectFade, A11y]}
          spaceBetween={0}
          slidesPerView={1}
          effect="fade"
          navigation={showNavigation}
          pagination={
            showPagination
              ? {
                  clickable: true,
                  dynamicBullets: true,
                  renderBullet: (index: number, className: string) => {
                    return `<span class="${className}" role="button" aria-label="Go to slide ${
                      index + 1
                    }"></span>`;
                  },
                }
              : false
          }
          autoplay={
            autoplayEnabled
              ? {
                  delay: delay,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: pauseOnHover,
                }
              : false
          }
          loop={true}
          a11y={{
            prevSlideMessage: "Previous slide",
            nextSlideMessage: "Next slide",
            firstSlideMessage: "This is the first slide",
            lastSlideMessage: "This is the last slide",
            paginationBulletMessage: "Go to slide {{index}}",
          }}
          initialSlide={0}
          className="w-full h-full"
        >
          {shuffledBanners.map((banner: BannerImage | Banner) => {
            // Handle both BannerImage and Banner types
            const bannerId = 'id' in banner ? banner.id : banner._id;
            const bannerTitle = banner.title;
            const bannerLink = 'link' in banner ? banner.link : banner.ctaLink;
            const bannerButtonText = 'buttonText' in banner ? banner.buttonText : (banner as any).ctaText;
            const desktopImg = 'desktopImage' in banner ? banner.desktopImage : (banner as any).desktopBanner;
            const mobileImg = 'mobileImage' in banner ? banner.mobileImage : (banner as any).mobileBanner;

            return (
            <SwiperSlide key={bannerId} className="relative h-full">
              {/* Desktop Image (hidden on small screens, visible on md and up) */}
              <div className="hidden md:block w-full h-full">
                <img
                  src={desktopImg}
                  alt={banner.altText || bannerTitle}
                  className="w-full h-full object-cover object-center transition-all duration-700 hover:scale-105"
                  style={{ minHeight: "100%" }}
                  loading="lazy"
                  fetchPriority="high"
                />
                {/* Gradient overlay for better text visibility */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/30 opacity-75 banner-gradient-overlay"></div>
              </div>

              {/* Mobile Image (visible on small screens, hidden on md and up) */}
              <div className="block md:hidden w-full h-full">
                <img
                  src={mobileImg}
                  alt={banner.altText || bannerTitle}
                  className="w-full h-full object-cover object-center transition-all duration-700 hover:scale-105"
                  style={{ minHeight: "100vh" }}
                  loading="lazy"
                  fetchPriority="high"
                />
                {/* Gradient overlay for better text visibility */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/30 opacity-75 banner-gradient-overlay"></div>
              </div>

              {/* Banner Content with animation - consistent positioning for all slides */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white p-4 max-w-xl mx-auto banner-content">
                  <span className="inline-block px-3 py-1 bg-[#000000]/60 text-white text-xs uppercase tracking-wider rounded-full mb-4 backdrop-blur-sm">
                    {bannerType} Collection
                  </span>
                  <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 drop-shadow-lg leading-tight">
                    {bannerTitle}
                  </h2>
                  {bannerButtonText && (
                    <Link
                      to={bannerLink}
                      onClick={() => '_id' in banner && handleBannerClick(banner._id)}
                      className="inline-block px-5 py-2 bg-[#000000] text-white rounded-lg hover:bg-[#333333] transition-colors duration-300 shadow-lg hover:shadow-xl text-sm"
                    >
                      {bannerButtonText}
                    </Link>
                  )}
                </div>
              </div>
            </SwiperSlide>
            );
          })}
        </Swiper>
      </div>
    </div>
  );
};

export default BannerSlider;
