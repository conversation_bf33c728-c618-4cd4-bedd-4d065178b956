/* Video Slider Styles - Simplified CSS */
.video-slider-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* Video Slider Header Styles */
.video-slider-header {
  text-align: center;
  padding: 0 20px 24px;
  max-width: 800px;
  margin: 0 auto;
}

.video-slider-title {
  font-size: 28px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8px;
}

.video-slider-subtitle {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
}

.video-slider-track {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth;
}

.video-slider-track::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.video-slider-inner {
  display: flex;
  padding: 0 4px;
}

/* Video Card Styles */
.video-card-slider-item {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 12px;
  overflow: hidden;
  background: #1a1a1a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-card-slider-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* Loading Indicator */
.video-loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 2;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Indicator */
.video-error-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(240, 240, 240, 0.95);
  color: #666;
  text-align: center;
  z-index: 2;
}

.video-error-indicator p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/* Swiper Customization */
.video-slider-full-width {
  width: 100%;
  padding: 0 20px;
}

.video-slider-full-width .swiper-slide {
  height: auto;
}

.video-slider-full-width .swiper-button-next,
.video-slider-full-width .swiper-button-prev {
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  width: 44px;
  height: 44px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.video-slider-full-width .swiper-button-next:hover,
.video-slider-full-width .swiper-button-prev:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.video-slider-full-width .swiper-button-next::after,
.video-slider-full-width .swiper-button-prev::after {
  font-size: 18px;
  font-weight: bold;
}

.video-slider-full-width .swiper-pagination-bullet {
  background: #333;
  opacity: 0.3;
  transition: all 0.3s ease;
}

.video-slider-full-width .swiper-pagination-bullet-active {
  opacity: 1;
  transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-card-slider-item {
    height: 300px;
  }
  
  .video-slider-title {
    font-size: 24px;
  }
  
  .video-slider-full-width {
    padding: 0 16px;
  }
  
  .video-slider-full-width .swiper-button-next,
  .video-slider-full-width .swiper-button-prev {
    width: 36px;
    height: 36px;
  }
  
  .video-slider-full-width .swiper-button-next::after,
  .video-slider-full-width .swiper-button-prev::after {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .video-card-slider-item {
    height: 250px;
  }
  
  .video-slider-title {
    font-size: 20px;
  }
  
  .video-slider-subtitle {
    font-size: 14px;
  }
}
