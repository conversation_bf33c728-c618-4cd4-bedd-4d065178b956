import React, { useState, useEffect, useRef } from "react";

// Define the props for VideoCard
interface VideoCardProps {
  videoUrl: string;
  index: number;
}

const VideoCard: React.FC<VideoCardProps> = ({
  videoUrl,
  index
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Handle video events
  const handleCanPlay = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    console.error(`Failed to load video: ${videoUrl}`);
  };

  // Auto-play videos
  useEffect(() => {
    const videoElement = videoRef.current;
    if (videoElement) {
      // Set up autoplay
      videoElement.muted = true;

      // Add a small delay based on index to stagger video loading
      const delay = index * 200;
      const timer = setTimeout(() => {
        // For imported videos, we need to ensure the video is loaded
        if (videoElement.readyState >= 2) { // HAVE_CURRENT_DATA or higher
          videoElement.play().catch(error => {
            console.error("Error auto-playing video:", error);
            setHasError(true);
          });
        } else {
          // If not loaded yet, wait for the loadeddata event
          const handleLoaded = () => {
            videoElement.play().catch(error => {
              console.error("Error auto-playing video after load:", error);
              setHasError(true);
            });
            videoElement.removeEventListener('loadeddata', handleLoaded);
          };
          videoElement.addEventListener('loadeddata', handleLoaded);
        }
      }, delay);

      return () => {
        clearTimeout(timer);
        if (videoElement) {
          videoElement.pause();
          // Don't clear src for imported videos as it can cause issues
        }
      };
    }
  }, [index, videoUrl]);

  return (
    <div className="video-card-slider-item relative w-full h-[400px] rounded-lg overflow-hidden bg-gray-900">
      {/* Loading indicator */}
      {isLoading && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      )}

      {/* Error indicator */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
          <div className="text-center text-gray-500">
            <div className="text-2xl mb-2">📹</div>
            <p>Video unavailable</p>
          </div>
        </div>
      )}

      {/* Video Element */}
      <video
        ref={videoRef}
        src={videoUrl}
        className={`w-full h-full object-cover ${hasError ? 'hidden' : ''}`}
        playsInline
        muted
        loop
        autoPlay
        preload="auto"
        onCanPlay={handleCanPlay}
        onError={handleError}
      />
    </div>
  );
};

export default VideoCard;
