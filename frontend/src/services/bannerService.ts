const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Banner interface
export interface Banner {
  _id: string;
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  bannerType: string;
  category: string;
  desktopImage: string;
  mobileImage?: string;
  thumbnailImage?: string;
  ctaText: string;
  ctaLink: string;
  isActive: boolean;
  displayOrder: number;
  textColor?: string;
  backgroundColor?: string;
  overlayOpacity?: number;
  textAlignment?: 'left' | 'center' | 'right';
  startDate?: string;
  endDate?: string;
  altText?: string;
  seoTitle?: string;
  seoDescription?: string;
  clickCount?: number;
  impressionCount?: number;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  createdAt?: string;
  updatedAt?: string;
}

// API Response interface
interface BannerApiResponse {
  success: boolean;
  data: Banner[];
  message?: string;
}

// Banner Service
export const bannerService = {
  // Fetch all banners
  async getAllBanners(params?: { page?: number; limit?: number }): Promise<Banner[]> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());

      const url = `${API_BASE_URL}/banners${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      console.log('Fetching banners from:', url);

      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: BannerApiResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch banners');
      }

      console.log('Banners fetched successfully:', data.data?.length || 0);
      return data.data || [];
    } catch (error) {
      console.error('Error fetching banners:', error);
      throw error;
    }
  },

  // Get banners by type (kurti, lehenga, etc.)
  async getBannersByType(bannerType: string): Promise<Banner[]> {
    try {
      const url = `${API_BASE_URL}/banners/type/${bannerType}`;
      console.log('Fetching banners by type from:', url);

      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: BannerApiResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch banners by type');
      }

      console.log(`${bannerType} banners fetched successfully:`, data.data?.length || 0);
      return data.data || [];
    } catch (error) {
      console.error(`Error fetching ${bannerType} banners:`, error);
      throw error;
    }
  },

  // Get Kurti banners
  async getKurtiBanners(): Promise<Banner[]> {
    return this.getBannersByType('kurti');
  },

  // Get Lehenga banners
  async getLehengaBanners(): Promise<Banner[]> {
    return this.getBannersByType('lehenga');
  }
};

export default bannerService;
