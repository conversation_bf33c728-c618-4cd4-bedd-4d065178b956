/**
 * Base API Service - Centralized API configuration and utilities
 * All API calls should go through this service for consistency
 */

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:5000/api";

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Base API configuration
const defaultConfig: RequestInit = {
  credentials: 'include', // Always include cookies for authentication
  headers: {
    'Content-Type': 'application/json',
  },
};

/**
 * Base API call function with error handling and cookie support
 */
export const apiCall = async <T = any>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config: RequestInit = {
    ...defaultConfig,
    ...options,
    headers: {
      ...defaultConfig.headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    
    // Handle different response types
    const contentType = response.headers.get('content-type');
    let responseData;
    
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }

    if (!response.ok) {
      // Handle API errors
      const error: ApiError = {
        message: responseData?.message || responseData || `HTTP error! status: ${response.status}`,
        status: response.status,
        code: responseData?.code,
      };
      throw error;
    }

    return responseData;
  } catch (error) {
    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw {
        message: 'Network error. Please check your connection.',
        status: 0,
      } as ApiError;
    }
    
    // Re-throw API errors
    throw error;
  }
};

/**
 * GET request helper
 */
export const apiGet = <T = any>(endpoint: string): Promise<T> => {
  return apiCall<T>(endpoint, { method: 'GET' });
};

/**
 * POST request helper
 */
export const apiPost = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return apiCall<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
};

/**
 * PUT request helper
 */
export const apiPut = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return apiCall<T>(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
};

/**
 * DELETE request helper
 */
export const apiDelete = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return apiCall<T>(endpoint, {
    method: 'DELETE',
    body: data ? JSON.stringify(data) : undefined,
  });
};

/**
 * File upload helper
 */
export const apiUpload = <T = any>(
  endpoint: string, 
  file: File, 
  additionalData?: Record<string, any>
): Promise<T> => {
  const formData = new FormData();
  formData.append('file', file);
  
  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, value);
    });
  }

  return apiCall<T>(endpoint, {
    method: 'POST',
    body: formData,
    headers: {}, // Let browser set Content-Type for FormData
  });
};

/**
 * API endpoints configuration
 */
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    SEND_OTP: '/auth/send-otp',
    AUTHENTICATE: '/auth/auth',
    QUICK_LOGIN: '/auth/quick-login',
    RE_AUTHENTICATE: '/auth/re-authenticate',
    VERIFY: '/auth/verify',
    LOGOUT: '/auth/logout',
  },
  
  // User Profile
  USER: {
    PROFILE: '/user/profile',
    PROFILE_PHOTO: '/user/profile/photo',
    PREFERENCES: '/user/preferences',
    UPLOAD_AVATAR: '/user/upload-avatar',
    ADDRESSES: '/user/addresses',
    ADDRESS_DETAILS: (id: string) => `/user/addresses/${id}`,
    SET_DEFAULT_ADDRESS: (id: string) => `/user/addresses/${id}/default`,
  },
  
  // Orders
  ORDERS: {
    LIST: '/orders',
    DETAILS: (id: string) => `/orders/${id}`,
    CREATE: '/orders',
    CANCEL: (id: string) => `/orders/${id}/cancel`,
  },
  
  // Products
  PRODUCTS: {
    LIST: '/products',
    DETAILS: (id: string) => `/products/${id}`,
    SEARCH: '/products/search',
    CATEGORIES: '/products/categories',
  },

  // Content APIs
  CONTENT: {
    // Banners
    BANNERS: '/content/banners',
    BANNERS_BY_TYPE: (type: string) => `/content/banners/${type}`,
    BANNERS_ACTIVE: '/content/banners/active',
    BANNERS_MAIN_HOME: '/content/banners/main-home',

    // Feature Categories (Products)
    FEATURE_CATEGORIES: '/content/feature-categories',
    FEATURE_CATEGORIES_FEATURED: '/content/feature-categories/featured',
    FEATURE_CATEGORIES_SALE: '/content/feature-categories/sale',
    FEATURE_CATEGORIES_NEW: '/content/feature-categories/new',
    FEATURE_CATEGORIES_BY_TYPE: (category: string) => `/content/feature-categories/category/${category}`,
    FEATURE_CATEGORY_BY_ID: (id: string) => `/content/feature-categories/${id}`,
  },
  
  // Cart
  CART: {
    GET: '/user/cart',
    ADD: '/user/cart/items',
    UPDATE: '/user/cart/items',
    REMOVE: '/user/cart/items',
    CLEAR: '/user/cart',
  },

  // Wishlist
  WISHLIST: {
    GET: '/user/wishlist',
    ADD: '/user/wishlist/items',
    REMOVE: '/user/wishlist/items',
  },

  // Subscription endpoints
  SUBSCRIPTIONS: {
    SUBSCRIBE: '/subscriptions/subscribe',
    UNSUBSCRIBE: '/subscriptions/unsubscribe',
    STATS: '/subscriptions/stats',
    ALL: '/subscriptions',
    ACTIVE: '/subscriptions/active',
    BY_ID: (id: string) => `/subscriptions/${id}`,
    UPDATE: (id: string) => `/subscriptions/${id}`,
    DELETE: (id: string) => `/subscriptions/${id}`,
    BULK_UNSUBSCRIBE: '/subscriptions/bulk-unsubscribe',
  },

  // Customer Review endpoints
  REVIEWS: {
    ALL: '/reviews',
    PUBLISHED: '/reviews/published',
    FEATURED: '/reviews/featured',
    STATS: '/reviews/stats',
    BY_CATEGORY: (category: string) => `/reviews/category/${category}`,
    BY_ID: (id: string) => `/reviews/${id}`,
    CREATE: '/reviews',
    UPDATE: (id: string) => `/reviews/${id}`,
    DELETE: (id: string) => `/reviews/${id}`,
    APPROVE: (id: string) => `/reviews/${id}/approve`,
    REJECT: (id: string) => `/reviews/${id}/reject`,
    MARK_HELPFUL: (id: string) => `/reviews/${id}/helpful`,
  },
} as const;

/**
 * Request interceptor for adding common headers
 */
export const addRequestInterceptor = (interceptor: (config: RequestInit) => RequestInit) => {
  // This would be implemented if we switch to axios or similar
  console.warn('Request interceptors not implemented with fetch API');
};

/**
 * Response interceptor for handling common responses
 */
export const addResponseInterceptor = (
  onSuccess: (response: any) => any,
  onError: (error: any) => any
) => {
  // This would be implemented if we switch to axios or similar
  console.warn('Response interceptors not implemented with fetch API');
};

/**
 * Utility to check if error is authentication related
 */
export const isAuthError = (error: any): boolean => {
  return error?.status === 401 || error?.code === 'UNAUTHORIZED';
};

/**
 * Utility to check if error is network related
 */
export const isNetworkError = (error: any): boolean => {
  return error?.status === 0 || error?.message?.includes('Network error');
};

/**
 * Utility to format error message for display
 */
export const getErrorMessage = (error: any): string => {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.error) return error.error;
  return 'An unexpected error occurred';
};

/**
 * API Service object for consistent API calls
 */
export const apiService = {
  get: apiGet,
  post: apiPost,
  put: apiPut,
  delete: apiDelete,
  upload: apiUpload,
  call: apiCall,
};
