const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Video interface
export interface Video {
  _id: string;
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  isFeatured: boolean;
  isActive: boolean;
  isPublished: boolean;
  category: string;
  tags: string[];
  viewCount: number;
  likeCount: number;
  shareCount: number;
  displayOrder: number;
  thumbnailUrl?: string;
  duration?: string;
}

// API Response interface
interface VideoApiResponse {
  success: boolean;
  data: {
    videos: Video[];
    pagination?: {
      currentPage: number;
      totalPages: number;
      totalVideos: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  message?: string;
}

// Video Service
export const videoService = {
  // Fetch all videos
  async getAllVideos(params?: { page?: number; limit?: number }): Promise<Video[]> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());

      const url = `${API_BASE_URL}/videos${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      console.log('Fetching videos from:', url);

      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: VideoApiResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch videos');
      }

      console.log('Videos fetched successfully:', data.data.videos?.length || 0);
      return data.data.videos || [];
    } catch (error) {
      console.error('Error fetching videos:', error);
      throw error;
    }
  },

  // Get featured videos (filter from all videos)
  async getFeaturedVideos(): Promise<Video[]> {
    try {
      const allVideos = await this.getAllVideos();
      return allVideos.filter(video => video.isFeatured);
    } catch (error) {
      console.error('Error fetching featured videos:', error);
      throw error;
    }
  },

  // Get published videos only
  async getPublishedVideos(): Promise<Video[]> {
    try {
      const allVideos = await this.getAllVideos();
      return allVideos.filter(video => video.isPublished && video.isActive);
    } catch (error) {
      console.error('Error fetching published videos:', error);
      throw error;
    }
  }
};

export default videoService;
