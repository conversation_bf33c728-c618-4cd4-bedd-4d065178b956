import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/hooks';
import {
  fetchBannersByType,
  recordBannerClick,
  selectHomeBanners,
  selectCategorySliderBanners,
  selectPromotionalBanners,
  selectSeasonalBanners,
  selectCollectionBanners,
  selectBannerLoading,
  selectBannerError,
  selectBannersByCategory,
  selectActiveBanners,
  type Banner
} from '@/store/slices/banners/bannerSlice';

// Default fallback banners for when server is unavailable
const getDefaultBanners = (bannerType: string): Banner[] => {
  const defaultBanners: Record<string, Banner[]> = {
    'home-main': [
      {
        _id: 'default-home-1',
        title: 'Welcome to Our Fashion Store',
        subtitle: 'Discover the Latest Trends in Ethnic Wear',
        description: 'Explore our premium collection of traditional and modern clothing',
        bannerType: 'home-main',
        category: 'general',
        desktopImage: 'https://picsum.photos/1920/800?random=1',
        mobileImage: 'https://picsum.photos/768/600?random=1',
        ctaText: 'Shop Now',
        ctaLink: '/products',
        isActive: true,
        displayOrder: 1,
        textColor: '#ffffff',
        backgroundColor: 'transparent',
        overlayOpacity: 0.5,
        textAlignment: 'center',
        clickCount: 0,
        impressionCount: 0,
        tags: ['fashion', 'ethnic', 'trending'],
        priority: 'high',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    'category-slider': [
      {
        _id: 'default-slider-1',
        title: 'Premium Collection',
        subtitle: 'Explore Our Latest Arrivals',
        bannerType: 'category-slider',
        category: 'clothing',
        desktopImage: 'https://picsum.photos/1200/600?random=2',
        mobileImage: 'https://picsum.photos/768/400?random=2',
        ctaText: 'Explore',
        ctaLink: '/category/clothing',
        isActive: true,
        displayOrder: 1,
        textColor: '#ffffff',
        backgroundColor: 'transparent',
        overlayOpacity: 0.5,
        textAlignment: 'center',
        clickCount: 0,
        impressionCount: 0,
        tags: ['clothing', 'premium'],
        priority: 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        _id: 'default-slider-2',
        title: 'Fabric Paradise',
        subtitle: 'Quality Fabrics for Every Need',
        bannerType: 'category-slider',
        category: 'fabrics',
        desktopImage: 'https://picsum.photos/1200/600?random=3',
        mobileImage: 'https://picsum.photos/768/400?random=3',
        ctaText: 'Shop Fabrics',
        ctaLink: '/category/fabrics',
        isActive: true,
        displayOrder: 2,
        textColor: '#ffffff',
        backgroundColor: 'transparent',
        overlayOpacity: 0.5,
        textAlignment: 'center',
        clickCount: 0,
        impressionCount: 0,
        tags: ['fabrics', 'quality'],
        priority: 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    'category-banner': [
      {
        _id: 'default-category-1',
        title: 'Explore Our Categories',
        subtitle: 'Discover our premium collection of ethnic wear',
        bannerType: 'category-banner',
        category: 'general',
        desktopImage: 'https://picsum.photos/1920/900?random=4',
        mobileImage: 'https://picsum.photos/768/600?random=4',
        ctaText: 'Shop Now',
        ctaLink: '/category/all',
        isActive: true,
        displayOrder: 1,
        textColor: '#ffffff',
        backgroundColor: 'transparent',
        overlayOpacity: 0.5,
        textAlignment: 'center',
        clickCount: 0,
        impressionCount: 0,
        tags: ['categories', 'explore'],
        priority: 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  };

  return defaultBanners[bannerType] || [];
};

// Hook for home banners
export const useHomeBanners = () => {
  const dispatch = useAppDispatch();
  const banners = useAppSelector(selectHomeBanners);
  const loading = useAppSelector(selectBannerLoading('home-main'));
  const error = useAppSelector(selectBannerError('home-main'));

  useEffect(() => {
    dispatch(fetchBannersByType({ bannerType: 'home-main' }));
  }, [dispatch]);

  const handleBannerClick = (bannerId: string) => {
    dispatch(recordBannerClick(bannerId));
  };

  return {
    banners: banners.length > 0 ? banners : getDefaultBanners('home-main'),
    loading,
    error,
    handleBannerClick,
    usingFallback: banners.length === 0
  };
};

// Hook for category slider banners
export const useCategorySliderBanners = (category?: string) => {
  const dispatch = useAppDispatch();
  const allBanners = useAppSelector(selectCategorySliderBanners);
  const loading = useAppSelector(selectBannerLoading('category-slider'));
  const error = useAppSelector(selectBannerError('category-slider'));

  useEffect(() => {
    dispatch(fetchBannersByType({ bannerType: 'category-slider', category }));
  }, [dispatch, category]);

  const handleBannerClick = (bannerId: string) => {
    dispatch(recordBannerClick(bannerId));
  };

  // Filter by category if specified
  const banners = category 
    ? allBanners.filter(banner => banner.category === category)
    : allBanners;

  return {
    banners: banners.length > 0 ? banners : getDefaultBanners('category-slider'),
    loading,
    error,
    handleBannerClick,
    usingFallback: banners.length === 0
  };
};



// Hook for promotional banners
export const usePromotionalBanners = () => {
  const dispatch = useAppDispatch();
  const banners = useAppSelector(selectPromotionalBanners);
  const loading = useAppSelector(selectBannerLoading('promotional'));
  const error = useAppSelector(selectBannerError('promotional'));

  useEffect(() => {
    dispatch(fetchBannersByType({ bannerType: 'promotional' }));
  }, [dispatch]);

  const handleBannerClick = (bannerId: string) => {
    dispatch(recordBannerClick(bannerId));
  };

  return {
    banners,
    loading,
    error,
    handleBannerClick,
    usingFallback: false
  };
};

// Hook for seasonal banners
export const useSeasonalBanners = () => {
  const dispatch = useAppDispatch();
  const banners = useAppSelector(selectSeasonalBanners);
  const loading = useAppSelector(selectBannerLoading('seasonal'));
  const error = useAppSelector(selectBannerError('seasonal'));

  useEffect(() => {
    dispatch(fetchBannersByType({ bannerType: 'seasonal' }));
  }, [dispatch]);

  const handleBannerClick = (bannerId: string) => {
    dispatch(recordBannerClick(bannerId));
  };

  return {
    banners,
    loading,
    error,
    handleBannerClick,
    usingFallback: false
  };
};

// Hook for collection banners
export const useCollectionBanners = () => {
  const dispatch = useAppDispatch();
  const banners = useAppSelector(selectCollectionBanners);
  const loading = useAppSelector(selectBannerLoading('collection'));
  const error = useAppSelector(selectBannerError('collection'));

  useEffect(() => {
    dispatch(fetchBannersByType({ bannerType: 'collection' }));
  }, [dispatch]);

  const handleBannerClick = (bannerId: string) => {
    dispatch(recordBannerClick(bannerId));
  };

  return {
    banners,
    loading,
    error,
    handleBannerClick,
    usingFallback: false
  };
};

// Generic hook for any banner type
export const useBanners = (bannerType: string, category?: string) => {
  const dispatch = useAppDispatch();
  const loading = useAppSelector(selectBannerLoading(bannerType));
  const error = useAppSelector(selectBannerError(bannerType));

  useEffect(() => {
    dispatch(fetchBannersByType({ bannerType, category }));
  }, [dispatch, bannerType, category]);

  const handleBannerClick = (bannerId: string) => {
    dispatch(recordBannerClick(bannerId));
  };

  return {
    loading,
    error,
    handleBannerClick
  };
};
