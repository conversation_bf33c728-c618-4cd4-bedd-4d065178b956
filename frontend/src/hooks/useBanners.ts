import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/hooks';
import {
  fetchKurtiBanners,
  fetchLehengaBanners,
  selectKurtiBanners,
  selectLehengaBanners,
  selectKurtiBannersLoading,
  selectLehengaBannersLoading,
  selectKurtiBannersError,
  selectLehengaBannersError,
  type Banner
} from '@/store/slices/banners/bannerSlice';

// Default fallback banners for when server is unavailable
const getDefaultBanners = (bannerType: string): Banner[] => {
  const defaultBanners: Record<string, Banner[]> = {
    'home-main': [
      {
        _id: 'default-home-1',
        title: 'Welcome to Our Fashion Store',
        subtitle: 'Discover the Latest Trends in Ethnic Wear',
        description: 'Explore our premium collection of traditional and modern clothing',
        bannerType: 'home-main',
        category: 'general',
        desktopImage: 'https://picsum.photos/1920/800?random=1',
        mobileImage: 'https://picsum.photos/768/600?random=1',
        ctaText: 'Shop Now',
        ctaLink: '/products',
        isActive: true,
        displayOrder: 1,
        textColor: '#ffffff',
        backgroundColor: 'transparent',
        overlayOpacity: 0.5,
        textAlignment: 'center',
        clickCount: 0,
        impressionCount: 0,
        tags: ['fashion', 'ethnic', 'trending'],
        priority: 'high',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    'category-slider': [
      {
        _id: 'default-slider-1',
        title: 'Premium Collection',
        subtitle: 'Explore Our Latest Arrivals',
        bannerType: 'category-slider',
        category: 'clothing',
        desktopImage: 'https://picsum.photos/1200/600?random=2',
        mobileImage: 'https://picsum.photos/768/400?random=2',
        ctaText: 'Explore',
        ctaLink: '/category/clothing',
        isActive: true,
        displayOrder: 1,
        textColor: '#ffffff',
        backgroundColor: 'transparent',
        overlayOpacity: 0.5,
        textAlignment: 'center',
        clickCount: 0,
        impressionCount: 0,
        tags: ['clothing', 'premium'],
        priority: 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        _id: 'default-slider-2',
        title: 'Fabric Paradise',
        subtitle: 'Quality Fabrics for Every Need',
        bannerType: 'category-slider',
        category: 'fabrics',
        desktopImage: 'https://picsum.photos/1200/600?random=3',
        mobileImage: 'https://picsum.photos/768/400?random=3',
        ctaText: 'Shop Fabrics',
        ctaLink: '/category/fabrics',
        isActive: true,
        displayOrder: 2,
        textColor: '#ffffff',
        backgroundColor: 'transparent',
        overlayOpacity: 0.5,
        textAlignment: 'center',
        clickCount: 0,
        impressionCount: 0,
        tags: ['fabrics', 'quality'],
        priority: 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    'category-banner': [
      {
        _id: 'default-category-1',
        title: 'Explore Our Categories',
        subtitle: 'Discover our premium collection of ethnic wear',
        bannerType: 'category-banner',
        category: 'general',
        desktopImage: 'https://picsum.photos/1920/900?random=4',
        mobileImage: 'https://picsum.photos/768/600?random=4',
        ctaText: 'Shop Now',
        ctaLink: '/category/all',
        isActive: true,
        displayOrder: 1,
        textColor: '#ffffff',
        backgroundColor: 'transparent',
        overlayOpacity: 0.5,
        textAlignment: 'center',
        clickCount: 0,
        impressionCount: 0,
        tags: ['categories', 'explore'],
        priority: 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  };

  return defaultBanners[bannerType] || [];
};

// Hook for Kurti banners (can be used as home banners)
export const useKurtiBanners = () => {
  const dispatch = useAppDispatch();
  const banners = useAppSelector(selectKurtiBanners);
  const loading = useAppSelector(selectKurtiBannersLoading);
  const error = useAppSelector(selectKurtiBannersError);

  useEffect(() => {
    dispatch(fetchKurtiBanners());
  }, [dispatch]);

  return {
    banners: banners.length > 0 ? banners : getDefaultBanners('home-main'),
    loading,
    error,
    usingFallback: banners.length === 0
  };
};

// Hook for Lehenga banners
export const useLehengaBanners = () => {
  const dispatch = useAppDispatch();
  const banners = useAppSelector(selectLehengaBanners);
  const loading = useAppSelector(selectLehengaBannersLoading);
  const error = useAppSelector(selectLehengaBannersError);

  useEffect(() => {
    dispatch(fetchLehengaBanners());
  }, [dispatch]);

  return {
    banners: banners.length > 0 ? banners : getDefaultBanners('category-slider'),
    loading,
    error,
    usingFallback: banners.length === 0
  };
};

// Hook for category slider banners (uses Lehenga banners)
export const useCategorySliderBanners = (category?: string) => {
  const dispatch = useAppDispatch();
  const allBanners = useAppSelector(selectLehengaBanners);
  const loading = useAppSelector(selectLehengaBannersLoading);
  const error = useAppSelector(selectLehengaBannersError);

  useEffect(() => {
    dispatch(fetchLehengaBanners());
  }, [dispatch]);

  // Filter by category if specified
  const banners = category
    ? allBanners.filter(banner => banner.category === category)
    : allBanners;

  return {
    banners: banners.length > 0 ? banners : getDefaultBanners('category-slider'),
    loading,
    error,
    usingFallback: banners.length === 0
  };
};



// Backward compatibility hooks - all use either Kurti or Lehenga banners

// Hook for promotional banners (uses Kurti banners)
export const usePromotionalBanners = () => {
  return useKurtiBanners();
};

// Hook for seasonal banners (uses Lehenga banners)
export const useSeasonalBanners = () => {
  return useLehengaBanners();
};

// Hook for collection banners (uses Kurti banners)
export const useCollectionBanners = () => {
  return useKurtiBanners();
};

// Hook for home banners (uses Kurti banners)
export const useHomeBanners = () => {
  return useKurtiBanners();
};

// Generic hook for any banner type
export const useBanners = (bannerType: string, category?: string) => {
  // Route to appropriate banner type based on bannerType
  if (bannerType.includes('lehenga') || bannerType.includes('category') || bannerType.includes('seasonal')) {
    return useLehengaBanners();
  } else {
    return useKurtiBanners();
  }
};
