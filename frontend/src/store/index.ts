import { configureStore } from "@reduxjs/toolkit";
import type { TypedUseSelectorHook } from "react-redux";
import { useDispatch, useSelector } from "react-redux";
import textReducer from "@/store/slices/textSlice";
import cartReducer from "@/store/slices/cart/cartSlice";
import searchReducer from "@/store/slices/search/searchSlice";
import authReducer from "@/store/slices/auth/authSlice";
// Content slices
import videoReducer from "@/store/slices/videos/videoSlice";
import bannerReducer from "@/store/slices/banners/bannerSlice";
import productReducer from "@/store/slices/products/productSlice";

import featureCategoryReducer from "@/store/slices/content/featureCategorySlice";
import subscriptionReducer from "@/store/slices/subscription/subscriptionSlice";
import customerReviewReducer from "@/store/slices/reviews/customerReviewSlice";
// Shopping slices
import wishlistReducer from "@/store/slices/wishlist/wishlistSlice";
import topHeaderReducer from "@/store/slices/topHeaderSlice";
import menuConfigReducer from "@/store/slices/menuConfigSlice";
import categoryReducer from "@/store/slices/categories/categorySlice";
import orderReducer from "@/store/slices/orders/orderSlice";
import { cartPersistenceMiddleware, cartAnalyticsMiddleware } from "@/store/slices/cart/cartMiddleware";
import { searchPersistenceMiddleware, searchAnalyticsMiddleware } from "@/store/slices/search/searchMiddleware";
import {
  authPersistenceMiddleware,
  cookieSessionMiddleware,
  sessionActivityMiddleware,
  authErrorHandlingMiddleware,
  authAnalyticsMiddleware
} from "@/store/slices/auth/authMiddleware";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    text: textReducer,
    cart: cartReducer,
    search: searchReducer,
    // Content reducers
    videos: videoReducer,
    banners: bannerReducer,
    products: productReducer,

    featureCategories: featureCategoryReducer,
    subscription: subscriptionReducer,
    customerReviews: customerReviewReducer,
    // Shopping reducers
    wishlist: wishlistReducer,
    topHeader: topHeaderReducer,
    menuConfig: menuConfigReducer,
    categories: categoryReducer,
    orders: orderReducer,
  },
  // Production-ready middleware configuration
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      // Configure serializable check based on environment
      serializableCheck: import.meta.env.DEV ? {
        // In development, ignore these action types
        ignoredActions: ["user/setUser", "auth/setTokens"],
        // Ignore these field paths in all actions
        ignoredActionPaths: ["payload.timestamp", "payload.avatarFile", "payload.tokenExpiry"],
        // Ignore these paths in the state
        ignoredPaths: [
          "user.userData",
          "cart.lastUpdated",
          "search.lastSearchTime",
          "auth.lastActivity",
          "auth.tokenExpiry",
          "auth.sessionExpiry"
        ],
      } : false, // Disable in production for performance
      // Disable immutability checks in production for performance
      immutableCheck: import.meta.env.DEV,
    }).concat(
      cartPersistenceMiddleware,
      cartAnalyticsMiddleware,
      searchPersistenceMiddleware,
      searchAnalyticsMiddleware,
      authPersistenceMiddleware,
      cookieSessionMiddleware,
      sessionActivityMiddleware,
      authErrorHandlingMiddleware,
      authAnalyticsMiddleware
    ),
  // Enable Redux DevTools only in development
  devTools: import.meta.env.DEV,
});

// Define the root state type
export type RootState = ReturnType<typeof store.getState>;
// Export the store state type directly as well
export type StoreState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
