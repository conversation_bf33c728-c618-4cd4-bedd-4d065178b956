import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { RootState } from '@/store';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Types
export interface Video {
  _id: string;
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  isFeatured: boolean;
  isActive: boolean;
  isPublished: boolean;
  category: string;
  tags: string[];
  viewCount: number;
  likeCount: number;
  shareCount: number;
  displayOrder: number;
  thumbnailUrl?: string;
  duration?: string;
}

export interface VideoState {
  videos: Video[];
  loading: boolean;
  error: string | null;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
}

// Initial state
const initialState: VideoState = {
  videos: [],
  loading: false,
  error: null,
  status: 'idle',
};

// Single async thunk for fetching all videos
export const fetchAllVideos = createAsyncThunk(
  'videos/fetchAllVideos',
  async (params?: { page?: number; limit?: number }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    console.log('Fetching videos with params:', params);

    const response = await fetch(`${API_BASE_URL}/videos?${queryParams.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch videos');
    }

    const data = await response.json();
    console.log('Videos response:', data);

    // Your backend returns: { success: true, data: { videos: [...], pagination: {...} } }
    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch videos');
    }

    return data.data.videos || [];
  }
);

// Slice
const videoSlice = createSlice({
  name: 'videos',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    resetVideos: (state) => {
      state.videos = [];
      state.status = 'idle';
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch all videos
      .addCase(fetchAllVideos.pending, (state) => {
        state.loading = true;
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchAllVideos.fulfilled, (state, action) => {
        state.loading = false;
        state.status = 'succeeded';
        state.videos = action.payload;
      })
      .addCase(fetchAllVideos.rejected, (state, action) => {
        state.loading = false;
        state.status = 'failed';
        state.error = action.error.message || 'Failed to fetch videos';
      });
  },
});

// Actions
export const { clearError, resetVideos } = videoSlice.actions;

// Selectors
export const selectAllVideos = (state: RootState) => state.videos.videos;
export const selectFeaturedVideos = (state: RootState) => 
  state.videos.videos.filter(video => video.isFeatured);
export const selectVideosLoading = (state: RootState) => state.videos.loading;
export const selectVideosError = (state: RootState) => state.videos.error;
export const selectVideosStatus = (state: RootState) => state.videos.status;

// Export reducer
export default videoSlice.reducer;
