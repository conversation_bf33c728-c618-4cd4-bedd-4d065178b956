import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { RootState } from '@/store';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Banner interface
export interface Banner {
  _id: string;
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  bannerType: string;
  category: string;
  desktopImage: string;
  mobileImage?: string;
  thumbnailImage?: string;
  ctaText: string;
  ctaLink: string;
  isActive: boolean;
  displayOrder: number;
  textColor?: string;
  backgroundColor?: string;
  overlayOpacity?: number;
  textAlignment?: 'left' | 'center' | 'right';
  startDate?: string;
  endDate?: string;
  altText?: string;
  seoTitle?: string;
  seoDescription?: string;
  clickCount?: number;
  impressionCount?: number;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  createdAt?: string;
  updatedAt?: string;
}

export interface BannerState {
  kurtiBanners: Banner[];
  lehengaBanners: Banner[];
  loading: {
    kurti: boolean;
    lehenga: boolean;
  };
  error: {
    kurti: string | null;
    lehenga: string | null;
  };
  status: {
    kurti: 'idle' | 'loading' | 'succeeded' | 'failed';
    lehenga: 'idle' | 'loading' | 'succeeded' | 'failed';
  };
}

// Initial state
const initialState: BannerState = {
  kurtiBanners: [],
  lehengaBanners: [],
  loading: {
    kurti: false,
    lehenga: false,
  },
  error: {
    kurti: null,
    lehenga: null,
  },
  status: {
    kurti: 'idle',
    lehenga: 'idle',
  },
};

// Async thunks for fetching banners
export const fetchKurtiBanners = createAsyncThunk(
  'banners/fetchKurtiBanners',
  async () => {
    const response = await fetch(`${API_BASE_URL}/banners/type/kurti`);
    if (!response.ok) {
      throw new Error('Failed to fetch Kurti banners');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch Kurti banners');
    }

    return data.data || [];
  }
);

export const fetchLehengaBanners = createAsyncThunk(
  'banners/fetchLehengaBanners',
  async () => {
    const response = await fetch(`${API_BASE_URL}/banners/type/lehenga`);
    if (!response.ok) {
      throw new Error('Failed to fetch Lehenga banners');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch Lehenga banners');
    }

    return data.data || [];
  }
);

// Slice
const bannerSlice = createSlice({
  name: 'banners',
  initialState,
  reducers: {
    clearKurtiError: (state) => {
      state.error.kurti = null;
    },
    clearLehengaError: (state) => {
      state.error.lehenga = null;
    },
    resetBanners: (state) => {
      state.kurtiBanners = [];
      state.lehengaBanners = [];
      state.status.kurti = 'idle';
      state.status.lehenga = 'idle';
      state.error.kurti = null;
      state.error.lehenga = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Kurti banners
      .addCase(fetchKurtiBanners.pending, (state) => {
        state.loading.kurti = true;
        state.status.kurti = 'loading';
        state.error.kurti = null;
      })
      .addCase(fetchKurtiBanners.fulfilled, (state, action) => {
        state.loading.kurti = false;
        state.status.kurti = 'succeeded';
        state.kurtiBanners = action.payload;
      })
      .addCase(fetchKurtiBanners.rejected, (state, action) => {
        state.loading.kurti = false;
        state.status.kurti = 'failed';
        state.error.kurti = action.error.message || 'Failed to fetch Kurti banners';
      })
      // Fetch Lehenga banners
      .addCase(fetchLehengaBanners.pending, (state) => {
        state.loading.lehenga = true;
        state.status.lehenga = 'loading';
        state.error.lehenga = null;
      })
      .addCase(fetchLehengaBanners.fulfilled, (state, action) => {
        state.loading.lehenga = false;
        state.status.lehenga = 'succeeded';
        state.lehengaBanners = action.payload;
      })
      .addCase(fetchLehengaBanners.rejected, (state, action) => {
        state.loading.lehenga = false;
        state.status.lehenga = 'failed';
        state.error.lehenga = action.error.message || 'Failed to fetch Lehenga banners';
      });
  },
});

// Actions
export const { clearKurtiError, clearLehengaError, resetBanners } = bannerSlice.actions;

// Selectors
export const selectKurtiBanners = (state: RootState) => state.banners.kurtiBanners;
export const selectLehengaBanners = (state: RootState) => state.banners.lehengaBanners;
export const selectKurtiBannersLoading = (state: RootState) => state.banners.loading.kurti;
export const selectLehengaBannersLoading = (state: RootState) => state.banners.loading.lehenga;
export const selectKurtiBannersError = (state: RootState) => state.banners.error.kurti;
export const selectLehengaBannersError = (state: RootState) => state.banners.error.lehenga;
export const selectKurtiBannersStatus = (state: RootState) => state.banners.status.kurti;
export const selectLehengaBannersStatus = (state: RootState) => state.banners.status.lehenga;

// Export reducer
export default bannerSlice.reducer;
