import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { RootState } from '@/store';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Simplified Product interfaces
export interface ProductPrice {
  original: number;
  current: number;
  currency: string;
  discount_percentage: number;
  savings: number;
}

export interface ProductImage {
  image_url: string;
  alt_text: string;
  is_primary: boolean;
}

export interface ProductRating {
  average_rating: number;
  reviews_count: number;
}

export interface Product {
  _id: string;
  product_id: string;
  name: string;
  slug: string;
  brand: string;
  category: string | { _id: string; name: string; slug: string };
  subcategory: string;
  description: string;
  short_description?: string;
  price: ProductPrice;
  material: string;
  tags: string[];
  rating: ProductRating;
  availability: string;
  stock_status: string;
  product_images: ProductImage[];
  product_url: string;
  created_at: string;
  updated_at: string;
  is_featured: boolean;
  is_bestseller: boolean;
  is_new_arrival: boolean;
}

// Simplified Product State
export interface ProductState {
  // Kurti products
  kurtiProducts: Product[];
  kurtiLoading: boolean;
  kurtiError: string | null;

  // Lehenga products
  lehengaProducts: Product[];
  lehengaLoading: boolean;
  lehengaError: string | null;

  // Status tracking
  status: {
    kurti: 'idle' | 'loading' | 'succeeded' | 'failed';
    lehenga: 'idle' | 'loading' | 'succeeded' | 'failed';
  };
}

const initialState: ProductState = {
  kurtiProducts: [],
  kurtiLoading: false,
  kurtiError: null,

  lehengaProducts: [],
  lehengaLoading: false,
  lehengaError: null,

  status: {
    kurti: 'idle',
    lehenga: 'idle',
  },
};

// Async thunks for fetching products
export const fetchKurtiProducts = createAsyncThunk(
  'products/fetchKurtiProducts',
  async (limit: number = 8) => {
    const response = await fetch(`${API_BASE_URL}/products?category=kurti&limit=${limit}`);
    if (!response.ok) {
      throw new Error('Failed to fetch Kurti products');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch Kurti products');
    }

    return data.data.products || [];
  }
);

export const fetchLehengaProducts = createAsyncThunk(
  'products/fetchLehengaProducts',
  async (limit: number = 8) => {
    const response = await fetch(`${API_BASE_URL}/products?category=lehenga&limit=${limit}`);
    if (!response.ok) {
      throw new Error('Failed to fetch Lehenga products');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch Lehenga products');
    }

    return data.data.products || [];
  }
);

// Slice
const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearKurtiError: (state) => {
      state.kurtiError = null;
    },
    clearLehengaError: (state) => {
      state.lehengaError = null;
    },
    resetProducts: (state) => {
      state.kurtiProducts = [];
      state.lehengaProducts = [];
      state.status.kurti = 'idle';
      state.status.lehenga = 'idle';
      state.kurtiError = null;
      state.lehengaError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Kurti products
      .addCase(fetchKurtiProducts.pending, (state) => {
        state.kurtiLoading = true;
        state.status.kurti = 'loading';
        state.kurtiError = null;
      })
      .addCase(fetchKurtiProducts.fulfilled, (state, action) => {
        state.kurtiLoading = false;
        state.status.kurti = 'succeeded';
        state.kurtiProducts = action.payload;
      })
      .addCase(fetchKurtiProducts.rejected, (state, action) => {
        state.kurtiLoading = false;
        state.status.kurti = 'failed';
        state.kurtiError = action.error.message || 'Failed to fetch Kurti products';
      })
      // Fetch Lehenga products
      .addCase(fetchLehengaProducts.pending, (state) => {
        state.lehengaLoading = true;
        state.status.lehenga = 'loading';
        state.lehengaError = null;
      })
      .addCase(fetchLehengaProducts.fulfilled, (state, action) => {
        state.lehengaLoading = false;
        state.status.lehenga = 'succeeded';
        state.lehengaProducts = action.payload;
      })
      .addCase(fetchLehengaProducts.rejected, (state, action) => {
        state.lehengaLoading = false;
        state.status.lehenga = 'failed';
        state.lehengaError = action.error.message || 'Failed to fetch Lehenga products';
      });
  },
});

// Actions
export const { clearKurtiError, clearLehengaError, resetProducts } = productSlice.actions;

// Selectors
export const selectKurtiProducts = (state: RootState) => state.products.kurtiProducts;
export const selectLehengaProducts = (state: RootState) => state.products.lehengaProducts;
export const selectKurtiProductsLoading = (state: RootState) => state.products.kurtiLoading;
export const selectLehengaProductsLoading = (state: RootState) => state.products.lehengaLoading;
export const selectKurtiProductsError = (state: RootState) => state.products.kurtiError;
export const selectLehengaProductsError = (state: RootState) => state.products.lehengaError;
export const selectKurtiProductsStatus = (state: RootState) => state.products.status.kurti;
export const selectLehengaProductsStatus = (state: RootState) => state.products.status.lehenga;

// Export reducer
export default productSlice.reducer;
