import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { RootState } from '@/store';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Simplified Product interfaces
export interface ProductPrice {
  original: number;
  current: number;
  currency: string;
  discount_percentage: number;
  savings: number;
}

export interface ProductImage {
  image_url: string;
  alt_text: string;
  is_primary: boolean;
}

export interface ProductRating {
  average_rating: number;
  reviews_count: number;
}

export interface Product {
  _id: string;
  product_id: string;
  name: string;
  slug: string;
  brand: string;
  category: string | { _id: string; name: string; slug: string };
  subcategory: string;
  description: string;
  short_description?: string;
  price: ProductPrice;
  material: string;
  tags: string[];
  rating: ProductRating;
  availability: string;
  stock_status: string;
  product_images: ProductImage[];
  product_url: string;
  created_at: string;
  updated_at: string;
  is_featured: boolean;
  is_bestseller: boolean;
  is_new_arrival: boolean;
}

// Product filters interface
export interface ProductFilters {
  category?: string;
  subcategory?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  minRating?: number;
  search?: string;
  sortBy?: string;
  page?: number;
  limit?: number;
  is_featured?: boolean;
  is_bestseller?: boolean;
  is_new_arrival?: boolean;
}

// Simplified Product State
export interface ProductState {
  // All products (mixed)
  allProducts: Product[];
  allProductsLoading: boolean;
  allProductsError: string | null;

  // Featured products
  featuredProducts: Product[];
  featuredLoading: boolean;
  featuredError: string | null;

  // New arrivals
  newArrivals: Product[];
  newArrivalsLoading: boolean;
  newArrivalsError: string | null;

  // Bestsellers
  bestsellers: Product[];
  bestsellersLoading: boolean;
  bestsellersError: string | null;

  // Kurti products
  kurtiProducts: Product[];
  kurtiLoading: boolean;
  kurtiError: string | null;

  // Lehenga products
  lehengaProducts: Product[];
  lehengaLoading: boolean;
  lehengaError: string | null;

  // Status tracking
  status: {
    all: 'idle' | 'loading' | 'succeeded' | 'failed';
    featured: 'idle' | 'loading' | 'succeeded' | 'failed';
    newArrivals: 'idle' | 'loading' | 'succeeded' | 'failed';
    bestsellers: 'idle' | 'loading' | 'succeeded' | 'failed';
    kurti: 'idle' | 'loading' | 'succeeded' | 'failed';
    lehenga: 'idle' | 'loading' | 'succeeded' | 'failed';
  };
}

const initialState: ProductState = {
  allProducts: [],
  allProductsLoading: false,
  allProductsError: null,

  featuredProducts: [],
  featuredLoading: false,
  featuredError: null,

  newArrivals: [],
  newArrivalsLoading: false,
  newArrivalsError: null,

  bestsellers: [],
  bestsellersLoading: false,
  bestsellersError: null,

  kurtiProducts: [],
  kurtiLoading: false,
  kurtiError: null,

  lehengaProducts: [],
  lehengaLoading: false,
  lehengaError: null,

  status: {
    all: 'idle',
    featured: 'idle',
    newArrivals: 'idle',
    bestsellers: 'idle',
    kurti: 'idle',
    lehenga: 'idle',
  },
};

// Dynamic product fetching thunk
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (filters: ProductFilters = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/products?${queryParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch products');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch products');
    }

    return data.data.products || [];
  }
);

// Specific thunks for different product types
export const fetchFeaturedProducts = createAsyncThunk(
  'products/fetchFeaturedProducts',
  async (limit: number = 8) => {
    const queryParams = new URLSearchParams();
    queryParams.append('is_featured', 'true');
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${API_BASE_URL}/products?${queryParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch featured products');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch featured products');
    }

    return data.data.products || [];
  }
);

export const fetchNewArrivalProducts = createAsyncThunk(
  'products/fetchNewArrivalProducts',
  async (limit: number = 8) => {
    const queryParams = new URLSearchParams();
    queryParams.append('is_new_arrival', 'true');
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${API_BASE_URL}/products?${queryParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch new arrival products');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch new arrival products');
    }

    return data.data.products || [];
  }
);

export const fetchBestsellerProducts = createAsyncThunk(
  'products/fetchBestsellerProducts',
  async (limit: number = 8) => {
    const queryParams = new URLSearchParams();
    queryParams.append('is_bestseller', 'true');
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${API_BASE_URL}/products?${queryParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch bestseller products');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch bestseller products');
    }

    return data.data.products || [];
  }
);

export const fetchKurtiProducts = createAsyncThunk(
  'products/fetchKurtiProducts',
  async (limit: number = 8) => {
    const queryParams = new URLSearchParams();
    queryParams.append('category', 'kurti');
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${API_BASE_URL}/products?${queryParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch Kurti products');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch Kurti products');
    }

    return data.data.products || [];
  }
);

export const fetchLehengaProducts = createAsyncThunk(
  'products/fetchLehengaProducts',
  async (limit: number = 8) => {
    const queryParams = new URLSearchParams();
    queryParams.append('category', 'lehenga');
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${API_BASE_URL}/products?${queryParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch Lehenga products');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch Lehenga products');
    }

    return data.data.products || [];
  }
);

// Slice
const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearAllProductsError: (state) => {
      state.allProductsError = null;
    },
    clearFeaturedError: (state) => {
      state.featuredError = null;
    },
    clearNewArrivalsError: (state) => {
      state.newArrivalsError = null;
    },
    clearBestsellersError: (state) => {
      state.bestsellersError = null;
    },
    clearKurtiError: (state) => {
      state.kurtiError = null;
    },
    clearLehengaError: (state) => {
      state.lehengaError = null;
    },
    resetProducts: (state) => {
      state.allProducts = [];
      state.featuredProducts = [];
      state.newArrivals = [];
      state.bestsellers = [];
      state.kurtiProducts = [];
      state.lehengaProducts = [];
      state.status.all = 'idle';
      state.status.featured = 'idle';
      state.status.newArrivals = 'idle';
      state.status.bestsellers = 'idle';
      state.status.kurti = 'idle';
      state.status.lehenga = 'idle';
      state.allProductsError = null;
      state.featuredError = null;
      state.newArrivalsError = null;
      state.bestsellersError = null;
      state.kurtiError = null;
      state.lehengaError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch all products
      .addCase(fetchProducts.pending, (state) => {
        state.allProductsLoading = true;
        state.status.all = 'loading';
        state.allProductsError = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.allProductsLoading = false;
        state.status.all = 'succeeded';
        state.allProducts = action.payload;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.allProductsLoading = false;
        state.status.all = 'failed';
        state.allProductsError = action.error.message || 'Failed to fetch products';
      })
      // Fetch featured products
      .addCase(fetchFeaturedProducts.pending, (state) => {
        state.featuredLoading = true;
        state.status.featured = 'loading';
        state.featuredError = null;
      })
      .addCase(fetchFeaturedProducts.fulfilled, (state, action) => {
        state.featuredLoading = false;
        state.status.featured = 'succeeded';
        state.featuredProducts = action.payload;
      })
      .addCase(fetchFeaturedProducts.rejected, (state, action) => {
        state.featuredLoading = false;
        state.status.featured = 'failed';
        state.featuredError = action.error.message || 'Failed to fetch featured products';
      })
      // Fetch new arrivals
      .addCase(fetchNewArrivalProducts.pending, (state) => {
        state.newArrivalsLoading = true;
        state.status.newArrivals = 'loading';
        state.newArrivalsError = null;
      })
      .addCase(fetchNewArrivalProducts.fulfilled, (state, action) => {
        state.newArrivalsLoading = false;
        state.status.newArrivals = 'succeeded';
        state.newArrivals = action.payload;
      })
      .addCase(fetchNewArrivalProducts.rejected, (state, action) => {
        state.newArrivalsLoading = false;
        state.status.newArrivals = 'failed';
        state.newArrivalsError = action.error.message || 'Failed to fetch new arrival products';
      })
      // Fetch bestsellers
      .addCase(fetchBestsellerProducts.pending, (state) => {
        state.bestsellersLoading = true;
        state.status.bestsellers = 'loading';
        state.bestsellersError = null;
      })
      .addCase(fetchBestsellerProducts.fulfilled, (state, action) => {
        state.bestsellersLoading = false;
        state.status.bestsellers = 'succeeded';
        state.bestsellers = action.payload;
      })
      .addCase(fetchBestsellerProducts.rejected, (state, action) => {
        state.bestsellersLoading = false;
        state.status.bestsellers = 'failed';
        state.bestsellersError = action.error.message || 'Failed to fetch bestseller products';
      })
      // Fetch Kurti products
      .addCase(fetchKurtiProducts.pending, (state) => {
        state.kurtiLoading = true;
        state.status.kurti = 'loading';
        state.kurtiError = null;
      })
      .addCase(fetchKurtiProducts.fulfilled, (state, action) => {
        state.kurtiLoading = false;
        state.status.kurti = 'succeeded';
        state.kurtiProducts = action.payload;
      })
      .addCase(fetchKurtiProducts.rejected, (state, action) => {
        state.kurtiLoading = false;
        state.status.kurti = 'failed';
        state.kurtiError = action.error.message || 'Failed to fetch Kurti products';
      })
      // Fetch Lehenga products
      .addCase(fetchLehengaProducts.pending, (state) => {
        state.lehengaLoading = true;
        state.status.lehenga = 'loading';
        state.lehengaError = null;
      })
      .addCase(fetchLehengaProducts.fulfilled, (state, action) => {
        state.lehengaLoading = false;
        state.status.lehenga = 'succeeded';
        state.lehengaProducts = action.payload;
      })
      .addCase(fetchLehengaProducts.rejected, (state, action) => {
        state.lehengaLoading = false;
        state.status.lehenga = 'failed';
        state.lehengaError = action.error.message || 'Failed to fetch Lehenga products';
      });
  },
});

// Actions
export const {
  clearAllProductsError,
  clearFeaturedError,
  clearNewArrivalsError,
  clearBestsellersError,
  clearKurtiError,
  clearLehengaError,
  resetProducts
} = productSlice.actions;

// Selectors
export const selectAllProducts = (state: RootState) => state.products.allProducts;
export const selectAllProductsLoading = (state: RootState) => state.products.allProductsLoading;
export const selectAllProductsError = (state: RootState) => state.products.allProductsError;

export const selectFeaturedProducts = (state: RootState) => state.products.featuredProducts;
export const selectFeaturedLoading = (state: RootState) => state.products.featuredLoading;
export const selectFeaturedError = (state: RootState) => state.products.featuredError;

export const selectNewArrivalProducts = (state: RootState) => state.products.newArrivals;
export const selectNewArrivalLoading = (state: RootState) => state.products.newArrivalsLoading;
export const selectNewArrivalError = (state: RootState) => state.products.newArrivalsError;

export const selectBestsellerProducts = (state: RootState) => state.products.bestsellers;
export const selectBestsellerLoading = (state: RootState) => state.products.bestsellersLoading;
export const selectBestsellerError = (state: RootState) => state.products.bestsellersError;

export const selectKurtiProducts = (state: RootState) => state.products.kurtiProducts;
export const selectKurtiProductsLoading = (state: RootState) => state.products.kurtiLoading;
export const selectKurtiProductsError = (state: RootState) => state.products.kurtiError;

export const selectLehengaProducts = (state: RootState) => state.products.lehengaProducts;
export const selectLehengaProductsLoading = (state: RootState) => state.products.lehengaLoading;
export const selectLehengaProductsError = (state: RootState) => state.products.lehengaError;

// Status selectors
export const selectAllProductsStatus = (state: RootState) => state.products.status.all;
export const selectFeaturedStatus = (state: RootState) => state.products.status.featured;
export const selectNewArrivalStatus = (state: RootState) => state.products.status.newArrivals;
export const selectBestsellerStatus = (state: RootState) => state.products.status.bestsellers;
export const selectKurtiProductsStatus = (state: RootState) => state.products.status.kurti;
export const selectLehengaProductsStatus = (state: RootState) => state.products.status.lehenga;

// Export reducer
export default productSlice.reducer;
