#!/bin/bash

# <PERSON><PERSON><PERSON>ree Admin Panel Startup Script
# This script helps you start the admin panel development server

echo "🚀 Starting <PERSON><PERSON><PERSON> Saree Admin Panel..."
echo "========================================"

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found!"
    echo "Please run this script from the adminPanel/admin directory"
    echo ""
    echo "Usage:"
    echo "  cd adminPanel/admin"
    echo "  ./start-admin.sh"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is not installed!"
    echo "Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "⚠️  Warning: Node.js version is $NODE_VERSION, but 18+ is recommended"
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ Error: npm is not installed!"
    echo "Please install npm or use yarn"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"
echo "✅ npm version: $(npm -v)"
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Error: Failed to install dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed successfully"
    echo ""
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚙️  Creating environment configuration..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ Created .env file from .env.example"
        echo "📝 Please update the .env file with your configuration"
    else
        echo "⚠️  No .env.example found, creating basic .env file..."
        cat > .env << EOL
# Admin Panel Environment Configuration
VITE_API_URL=http://localhost:5000/api
VITE_ADMIN_API_URL=http://localhost:5000/api/admin
VITE_NODE_ENV=development
VITE_APP_NAME=Sajawat Saree Admin
EOL
        echo "✅ Created basic .env file"
    fi
    echo ""
fi

# Display configuration
echo "🔧 Configuration:"
echo "  - API URL: $(grep VITE_API_URL .env | cut -d'=' -f2)"
echo "  - Admin API URL: $(grep VITE_ADMIN_API_URL .env | cut -d'=' -f2)"
echo "  - Environment: $(grep VITE_NODE_ENV .env | cut -d'=' -f2)"
echo ""

# Check if backend is running (optional)
echo "🔍 Checking backend connection..."
BACKEND_URL=$(grep VITE_API_URL .env | cut -d'=' -f2 | sed 's|/api||')
if curl -s "$BACKEND_URL/health" > /dev/null 2>&1; then
    echo "✅ Backend is running at $BACKEND_URL"
else
    echo "⚠️  Backend might not be running at $BACKEND_URL"
    echo "   Make sure to start your backend server first"
fi
echo ""

# Start the development server
echo "🌟 Starting admin panel development server..."
echo "   Admin Panel will be available at: http://localhost:5174"
echo "   Press Ctrl+C to stop the server"
echo ""
echo "🎉 Happy coding!"
echo "========================================"

# Start with host flag to allow external access
npm run dev -- --host

echo ""
echo "👋 Admin panel stopped. Thank you for using Sajawat Saree Admin!"
