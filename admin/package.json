{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.6", "@tremor/react": "^3.18.7", "@types/react-redux": "^7.1.34", "apexcharts": "^4.7.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.11.3", "lucide-react": "^0.510.0", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.23"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.2.9", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}