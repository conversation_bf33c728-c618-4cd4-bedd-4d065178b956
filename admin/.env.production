# Admin Panel Production Environment Configuration
# Admin Panel deployed on separate server from Frontend

# Backend Server Configuration (Replace with your actual backend server URL)
VITE_API_URL=https://your-backend-domain.com/api
VITE_ADMIN_API_URL=https://your-backend-domain.com/api/admin
VITE_BACKEND_URL=https://your-backend-domain.com

# Admin Panel Configuration
VITE_NODE_ENV=production
VITE_APP_NAME=Sajawat Saree Admin Panel
VITE_APP_VERSION=1.0.0

# Admin Server Configuration
VITE_ADMIN_PORT=80
VITE_ADMIN_HOST=0.0.0.0

# Security Configuration
VITE_JWT_SECRET=your-production-admin-jwt-secret-key-change-this
VITE_SESSION_TIMEOUT=3600000

# Features
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_DARK_MODE=true

# File Upload Configuration
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,image/gif

# Pagination
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# Admin Panel Specific Settings
VITE_ADMIN_TITLE=Sajawat Sarees Admin Dashboard
VITE_COMPANY_NAME=Sajawat Sarees
VITE_SUPPORT_EMAIL=<EMAIL>

# Deployment Configuration
VITE_BUILD_PATH=dist
VITE_PUBLIC_PATH=/
