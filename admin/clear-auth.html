<!DOCTYPE html>
<html>
<head>
    <title>Clear Admin Auth Data</title>
</head>
<body>
    <h1>Clear Admin Authentication Data</h1>
    <p>This will clear any stored authentication data that might be causing login loops.</p>
    <button onclick="clearAuth()">Clear Auth Data</button>
    <div id="result"></div>

    <script>
        function clearAuth() {
            try {
                // Clear all admin-related localStorage items
                localStorage.removeItem('sajawat_admin_token');
                localStorage.removeItem('sajawat_admin_refresh_token');
                localStorage.removeItem('sajawat_admin_user');
                
                // Clear any other potential auth items
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.includes('admin') || key.includes('auth')) {
                        localStorage.removeItem(key);
                    }
                });
                
                document.getElementById('result').innerHTML = '<p style="color: green;">✅ Auth data cleared successfully! You can now close this tab and try logging in again.</p>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<p style="color: red;">❌ Error clearing auth data: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
