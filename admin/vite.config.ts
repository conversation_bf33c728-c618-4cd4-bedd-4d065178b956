import path from "path";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 5174,
    host: true,
    // Admin Panel ONLY communicates with Backend Server
    proxy: {
      '/api': {
        target: 'http://localhost:5000', // Backend Server ONLY
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path, // Keep API paths as-is
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
});
