import { Route, Routes, Navigate, useLocation } from "react-router-dom";
import { Layout } from "@/components/layout";
import { Dashboard } from "@/pages/dashboard";
import { Products } from "@/pages/products";
import { ProductsListPage } from "@/pages/products/ProductsListPage";
import { ProductFormPage } from "@/pages/products/ProductFormPage";
import { BulkUploadPage } from "@/pages/products/BulkUploadPage";
import { CategoriesPage } from "@/pages/products/CategoriesPage";
import { AddProduct } from "@/pages/products/add";
import { EditProduct } from "@/pages/products/edit";
import { ProductView } from "@/pages/products/view";
import { ProductCategories } from "@/pages/products/categories";
import { ProductInventory } from "@/pages/products/inventory";
import { BulkProductUpload } from "@/pages/products/bulk-upload";
import { Orders } from "@/pages/orders";
import { OrderView } from "@/pages/orders/view";
import { OrdersPending } from "@/pages/orders/pending";
import { OrdersProcessing } from "@/pages/orders/processing";
import { OrdersCompleted } from "@/pages/orders/completed";
import { Customers } from "@/pages/customers";
import { CustomerView } from "@/pages/customers/view";
import { Analytics } from "@/pages/analytics";
import { SalesReport } from "@/pages/analytics/sales";
import { ProductsReport } from "@/pages/analytics/products";
import { CustomersReport } from "@/pages/analytics/customers";
import { Settings } from "@/pages/settings";
import { ProfileSettings } from "@/pages/settings/profile";
import { StoreSettings } from "@/pages/settings/store";
import { PaymentSettings } from "@/pages/settings/payment";
import { ShippingSettings } from "@/pages/settings/shipping";
import { TaxSettings } from "@/pages/settings/tax";
import { NotificationsSettings } from "@/pages/settings/notifications";
import { UsersSettings } from "@/pages/settings/users";
import { SecuritySettings } from "@/pages/settings/security";
import { Login } from "@/pages/auth/login";
import { NotFound } from "@/pages/not-found";
import TopHeadersPage from "@/pages/content/top-headers";
import MenuConfigPage from "@/pages/content/menu-config";
import BannersPage from "@/pages/content/banners";
import VideoManagement from "@/pages/VideoManagement";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { useTokenExpiryWarning } from "@/components/TokenExpiryWarning";
import * as React from "react";

// Test utility available but not auto-loaded
// To test token expiry: open console and run tokenExpiryTest.start()

// Protected route component
function RequireAuth({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  if (!isAuthenticated) {
    // Redirect to login page but save the current location
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

function AppContent() {
  const { isAuthenticated, loading } = useAuth();
  const { TokenExpiryWarning } = useTokenExpiryWarning();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-muted/30">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Token Expiry Warning Dialog */}
      {TokenExpiryWarning}

      <Routes>
        {/* Auth Routes */}
        <Route
          path="/login"
          element={isAuthenticated ? <Navigate to="/" /> : <Login />}
        />

        {/* Protected Routes */}
        <Route element={
          <RequireAuth>
            <Layout />
          </RequireAuth>
        }>
          <Route path="/" element={<Dashboard />} />

          {/* Products Routes */}
          <Route path="/products" element={<ProductsListPage />} />
          <Route path="/products/add" element={<ProductFormPage />} />
          <Route path="/products/edit/:id" element={<ProductFormPage />} />
          <Route path="/products/bulk-upload" element={<BulkUploadPage />} />
          <Route path="/products/categories" element={<CategoriesPage />} />
          <Route path="/products/:id" element={<ProductView />} />
          <Route path="/products/inventory" element={<ProductInventory />} />

          {/* Orders Routes */}
          <Route path="/orders" element={<Orders />} />
          <Route path="/orders/:id" element={<OrderView />} />
          <Route path="/orders/pending" element={<OrdersPending />} />
          <Route path="/orders/processing" element={<OrdersProcessing />} />
          <Route path="/orders/completed" element={<OrdersCompleted />} />

          {/* Customers Routes */}
          <Route path="/customers" element={<Customers />} />
          <Route path="/customers/:id" element={<CustomerView />} />

          {/* Content Routes */}
          <Route path="/content/top-headers" element={<TopHeadersPage />} />
          <Route path="/content/menu-config" element={<MenuConfigPage />} />
          <Route path="/content/banners" element={<BannersPage />} />

          {/* Video Routes */}
          <Route path="/videos" element={<VideoManagement />} />

          {/* Analytics Routes */}
          <Route path="/analytics" element={<Analytics />} />
          <Route path="/analytics/sales" element={<SalesReport />} />
          <Route path="/analytics/products" element={<ProductsReport />} />
          <Route path="/analytics/customers" element={<CustomersReport />} />

          {/* Settings Routes */}
          <Route path="/settings" element={<Settings />} />
          <Route path="/settings/profile" element={<ProfileSettings />} />
          <Route path="/settings/store" element={<StoreSettings />} />
          <Route path="/settings/payment" element={<PaymentSettings />} />
          <Route path="/settings/shipping" element={<ShippingSettings />} />
          <Route path="/settings/tax" element={<TaxSettings />} />
          <Route
            path="/settings/notifications"
            element={<NotificationsSettings />}
          />
          <Route path="/settings/users" element={<UsersSettings />} />
          <Route path="/settings/security" element={<SecuritySettings />} />
        </Route>

        {/* 404 Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </>
  );
}

export default App;
