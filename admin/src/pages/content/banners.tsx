import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, EyeOff, Image, BarChart3, Calendar, Tag } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/utils/toast';
import BannerModal from '@/components/content/BannerModal';

// Banner interfaces
interface Banner {
  _id: string;
  title: string;
  subtitle?: string;
  description?: string;
  bannerType: 'home-main' | 'category-slider' | 'promotional' | 'seasonal' | 'collection';
  category: string;
  desktopImage: string;
  mobileImage?: string;
  ctaText: string;
  ctaLink: string;
  isActive: boolean;
  displayOrder: number;
  textColor: string;
  backgroundColor: string;
  overlayOpacity: number;
  textAlignment: 'left' | 'center' | 'right';
  startDate?: string;
  endDate?: string;
  clickCount: number;
  impressionCount: number;
  tags: string[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
}

// Simple admin service
const API_BASE_URL = 'http://localhost:3000/api';

const bannerService = {
  async getAllBanners(filters = {}): Promise<{ banners: Banner[]; pagination: any }> {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    const response = await fetch(`${API_BASE_URL}/banners/admin/all?${queryParams}`);
    if (!response.ok) throw new Error('Failed to fetch banners');
    const data = await response.json();
    return { banners: data.data, pagination: data.pagination };
  },

  async createBanner(data: any): Promise<Banner> {
    const response = await fetch(`${API_BASE_URL}/banners/admin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to create banner');
    const result = await response.json();
    return result.data;
  },

  async updateBanner(id: string, data: any): Promise<Banner> {
    const response = await fetch(`${API_BASE_URL}/banners/admin/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to update banner');
    const result = await response.json();
    return result.data;
  },

  async deleteBanner(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/banners/admin/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('Failed to delete banner');
  },

  async toggleActiveStatus(id: string): Promise<Banner> {
    const response = await fetch(`${API_BASE_URL}/banners/admin/${id}/toggle`, {
      method: 'PATCH'
    });
    if (!response.ok) throw new Error('Failed to toggle banner status');
    const data = await response.json();
    return data.data;
  },

  async getBannerStatistics(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/banners/admin/statistics`);
    if (!response.ok) throw new Error('Failed to fetch banner statistics');
    const data = await response.json();
    return data.data;
  }
};

const BannersPage: React.FC = () => {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBanner, setEditingBanner] = useState<Banner | null>(null);
  const [filters, setFilters] = useState({
    bannerType: '',
    category: '',
    isActive: '',
    page: 1,
    limit: 20
  });

  // Fetch banners
  const fetchBanners = async () => {
    try {
      setLoading(true);
      const data = await bannerService.getAllBanners(filters);
      setBanners(data.banners);
    } catch (error) {
      toast.error('Failed to fetch banners');
      console.error('Error fetching banners:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const stats = await bannerService.getBannerStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  useEffect(() => {
    fetchBanners();
    fetchStatistics();
  }, [filters]);

  // Handle toggle active status
  const handleToggleActive = async (id: string) => {
    try {
      await bannerService.toggleActiveStatus(id);
      toast.success('Status updated successfully');
      fetchBanners();
    } catch (error) {
      toast.error('Failed to update status');
      console.error('Error toggling status:', error);
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this banner?')) return;

    try {
      await bannerService.deleteBanner(id);
      toast.success('Banner deleted successfully');
      fetchBanners();
      fetchStatistics();
    } catch (error) {
      toast.error('Failed to delete banner');
      console.error('Error deleting banner:', error);
    }
  };

  // Handle create new
  const handleCreate = () => {
    setEditingBanner(null);
    setIsModalOpen(true);
  };

  // Handle edit
  const handleEdit = (banner: Banner) => {
    setEditingBanner(banner);
    setIsModalOpen(true);
  };

  // Handle save (create or update)
  const handleSave = async (bannerData: Banner) => {
    try {
      if (editingBanner) {
        // Update existing
        await bannerService.updateBanner(editingBanner._id, bannerData);
        toast.success('Banner updated successfully');
      } else {
        // Create new
        await bannerService.createBanner(bannerData);
        toast.success('Banner created successfully');
      }

      setIsModalOpen(false);
      setEditingBanner(null);
      fetchBanners();
      fetchStatistics();
    } catch (error) {
      toast.error(editingBanner ? 'Failed to update banner' : 'Failed to create banner');
      console.error('Error saving banner:', error);
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingBanner(null);
  };

  // Get banner type color
  const getBannerTypeColor = (type: string) => {
    const colors = {
      'home-main': 'bg-blue-100 text-blue-800',
      'category-slider': 'bg-green-100 text-green-800',
      'category-banner': 'bg-purple-100 text-purple-800',
      'promotional': 'bg-red-100 text-red-800',
      'seasonal': 'bg-orange-100 text-orange-800',
      'collection': 'bg-pink-100 text-pink-800'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    const colors = {
      'low': 'bg-gray-100 text-gray-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-orange-100 text-orange-800',
      'urgent': 'bg-red-100 text-red-800'
    };
    return colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Banner Management</h1>
          <p className="text-gray-600 mt-1">
            Manage all banners across your website
          </p>
        </div>
        <Button onClick={handleCreate} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Banner
        </Button>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Banners</p>
                  <p className="text-2xl font-bold">{statistics.totalBanners}</p>
                </div>
                <Image className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active Banners</p>
                  <p className="text-2xl font-bold text-green-600">{statistics.activeBanners}</p>
                </div>
                <Eye className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Inactive Banners</p>
                  <p className="text-2xl font-bold text-red-600">{statistics.inactiveBanners}</p>
                </div>
                <EyeOff className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Banner Types</p>
                  <p className="text-2xl font-bold">{statistics.bannersByType?.length || 0}</p>
                </div>
                <Tag className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Banner Type</label>
              <select
                value={filters.bannerType}
                onChange={(e) => setFilters(prev => ({ ...prev, bannerType: e.target.value, page: 1 }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="">All Types</option>
                <option value="home-main">Home Main</option>
                <option value="category-slider">Category Slider</option>
                <option value="category-banner">Category Banner</option>
                <option value="promotional">Promotional</option>
                <option value="seasonal">Seasonal</option>
                <option value="collection">Collection</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Category</label>
              <input
                type="text"
                value={filters.category}
                onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value, page: 1 }))}
                placeholder="Filter by category"
                className="w-full p-2 border rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Status</label>
              <select
                value={filters.isActive}
                onChange={(e) => setFilters(prev => ({ ...prev, isActive: e.target.value, page: 1 }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="">All Status</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>
            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => setFilters({ bannerType: '', category: '', isActive: '', page: 1, limit: 20 })}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Banners List */}
      <div className="grid gap-6">
        {banners.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Image className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No banners found</p>
              <Button onClick={handleCreate}>Create your first banner</Button>
            </CardContent>
          </Card>
        ) : (
          banners.map((banner) => (
            <Card key={banner._id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="grid md:grid-cols-4 gap-0">
                  {/* Banner Image */}
                  <div className="relative h-48 md:h-auto">
                    <img
                      src={banner.desktopImage}
                      alt={banner.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 left-2 flex gap-1">
                      <Badge className={getPriorityColor(banner.priority)}>
                        {banner.priority}
                      </Badge>
                      <Badge className={getBannerTypeColor(banner.bannerType)}>
                        {banner.bannerType}
                      </Badge>
                    </div>
                  </div>

                  {/* Banner Details */}
                  <div className="md:col-span-3 p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-semibold mb-1">{banner.title}</h3>
                        {banner.subtitle && (
                          <p className="text-gray-600 mb-2">{banner.subtitle}</p>
                        )}
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Category: {banner.category}</span>
                          <span>Order: {banner.displayOrder}</span>
                          <span>Clicks: {banner.clickCount}</span>
                          <span>Views: {banner.impressionCount}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={banner.isActive}
                          onCheckedChange={() => handleToggleActive(banner._id)}
                        />
                        <Button variant="outline" size="sm" onClick={() => handleEdit(banner)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(banner._id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Banner Tags */}
                    {banner.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-4">
                        {banner.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Banner Actions */}
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <BarChart3 className="h-3 w-3" />
                        Analytics
                      </Button>
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Schedule
                      </Button>
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        Preview
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Modal */}
      <BannerModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSave={handleSave}
        editingBanner={editingBanner}
      />
    </div>
  );
};

export default BannersPage;
