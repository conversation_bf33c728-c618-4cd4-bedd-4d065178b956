import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, <PERSON>, EyeOff, GripVertical } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/utils/toast';
import TopHeaderModal from '@/components/content/TopHeaderModal';

// TopHeader interface
interface TopHeader {
  _id: string;
  text: string;
  isActive: boolean;
  order: number;
  backgroundColor: string;
  textColor: string;
  link?: string;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
}

// Simple admin service
const API_BASE_URL = 'http://localhost:3000/api';

const adminTopHeaderService = {
  async getAllTopHeaders(): Promise<TopHeader[]> {
    const response = await fetch(`${API_BASE_URL}/top-headers/admin/all`);
    if (!response.ok) throw new Error('Failed to fetch top headers');
    const data = await response.json();
    return data.data;
  },

  async createTopHeader(data: any): Promise<TopHeader> {
    const response = await fetch(`${API_BASE_URL}/top-headers/admin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to create top header');
    const result = await response.json();
    return result.data;
  },

  async updateTopHeader(id: string, data: any): Promise<TopHeader> {
    const response = await fetch(`${API_BASE_URL}/top-headers/admin/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to update top header');
    const result = await response.json();
    return result.data;
  },

  async deleteTopHeader(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/top-headers/admin/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('Failed to delete top header');
  },

  async toggleActiveStatus(id: string): Promise<TopHeader> {
    const response = await fetch(`${API_BASE_URL}/top-headers/admin/${id}/toggle`, {
      method: 'PATCH'
    });
    if (!response.ok) throw new Error('Failed to toggle top header status');
    const data = await response.json();
    return data.data;
  }
};

const TopHeadersPage: React.FC = () => {
  const [topHeaders, setTopHeaders] = useState<TopHeader[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingHeader, setEditingHeader] = useState<TopHeader | null>(null);

  // Fetch top headers
  const fetchTopHeaders = async () => {
    try {
      setLoading(true);
      const data = await adminTopHeaderService.getAllTopHeaders();
      setTopHeaders(data);
    } catch (error) {
      toast.error('Failed to fetch top headers');
      console.error('Error fetching top headers:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTopHeaders();
  }, []);

  // Handle create/edit
  const handleSave = async (data: any) => {
    try {
      if (editingHeader) {
        await adminTopHeaderService.updateTopHeader(editingHeader._id, data);
        toast.success('Top header updated successfully');
      } else {
        await adminTopHeaderService.createTopHeader(data);
        toast.success('Top header created successfully');
      }
      fetchTopHeaders();
      setIsModalOpen(false);
      setEditingHeader(null);
    } catch (error) {
      toast.error('Failed to save top header');
      console.error('Error saving top header:', error);
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this top header?')) return;
    
    try {
      await adminTopHeaderService.deleteTopHeader(id);
      toast.success('Top header deleted successfully');
      fetchTopHeaders();
    } catch (error) {
      toast.error('Failed to delete top header');
      console.error('Error deleting top header:', error);
    }
  };

  // Handle toggle active status
  const handleToggleActive = async (id: string) => {
    try {
      await adminTopHeaderService.toggleActiveStatus(id);
      toast.success('Status updated successfully');
      fetchTopHeaders();
    } catch (error) {
      toast.error('Failed to update status');
      console.error('Error toggling status:', error);
    }
  };

  // Handle edit
  const handleEdit = (header: TopHeader) => {
    setEditingHeader(header);
    setIsModalOpen(true);
  };

  // Handle create new
  const handleCreateNew = () => {
    setEditingHeader(null);
    setIsModalOpen(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Top Headers</h1>
          <p className="text-gray-600 mt-1">
            Manage promotional messages displayed at the top of your website
          </p>
        </div>
        <Button onClick={handleCreateNew} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Top Header
        </Button>
      </div>

      {/* Top Headers List */}
      <Card>
        <CardHeader>
          <CardTitle>All Top Headers</CardTitle>
        </CardHeader>
        <CardContent>
          {topHeaders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No top headers found</p>
              <Button onClick={handleCreateNew} className="mt-4">
                Create your first top header
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {topHeaders.map((header) => (
                <div
                  key={header._id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center gap-4 flex-1">
                    <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{header.text}</span>
                        <Badge variant={header.isActive ? 'default' : 'secondary'}>
                          {header.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>Order: {header.order}</span>
                        <div 
                          className="w-4 h-4 rounded border"
                          style={{ backgroundColor: header.backgroundColor }}
                          title={`Background: ${header.backgroundColor}`}
                        />
                        <div 
                          className="w-4 h-4 rounded border"
                          style={{ backgroundColor: header.textColor }}
                          title={`Text: ${header.textColor}`}
                        />
                        {header.link && (
                          <span className="text-blue-500">Has Link</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Switch
                      checked={header.isActive}
                      onCheckedChange={() => handleToggleActive(header._id)}
                    />
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(header)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(header._id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal */}
      <TopHeaderModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingHeader(null);
        }}
        onSave={handleSave}
        editingHeader={editingHeader}
      />
    </div>
  );
};

export default TopHeadersPage;
