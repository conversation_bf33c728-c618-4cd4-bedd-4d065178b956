import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, <PERSON>, <PERSON>Off, <PERSON>tings, Image, Menu } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/utils/toast';
import MenuConfigModal from '@/components/content/MenuConfigModal';

// Menu interfaces
interface MenuSubcategory {
  id: string;
  name: string;
  slug: string;
  path: string;
  isActive: boolean;
  order: number;
}

interface MenuCategory {
  id: string;
  name: string;
  slug: string;
  path: string;
  subcategories: MenuSubcategory[];
  featured: boolean;
  isActive: boolean;
  order: number;
  icon?: string;
  description?: string;
}

interface MenuImage {
  title: string;
  subtitle?: string;
  description?: string;
  imageUrl: string;
  linkUrl?: string;
  buttonText: string;
  isActive: boolean;
  position: 'mega-menu' | 'mobile-menu' | 'both';
}

interface MenuConfig {
  _id: string;
  name: string;
  type: 'mega-menu' | 'mobile-menu' | 'both';
  categories: MenuCategory[];
  menuImage: MenuImage;
  isActive: boolean;
  version: number;
  createdAt: string;
  updatedAt: string;
}

// Simple admin service
const API_BASE_URL = 'http://localhost:3000/api';

const menuConfigService = {
  async getAllMenuConfigs(): Promise<MenuConfig[]> {
    const response = await fetch(`${API_BASE_URL}/menu-config/admin/all`);
    if (!response.ok) throw new Error('Failed to fetch menu configurations');
    const data = await response.json();
    return data.data;
  },

  async createMenuConfig(data: any): Promise<MenuConfig> {
    const response = await fetch(`${API_BASE_URL}/menu-config/admin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to create menu configuration');
    const result = await response.json();
    return result.data;
  },

  async updateMenuConfig(id: string, data: any): Promise<MenuConfig> {
    const response = await fetch(`${API_BASE_URL}/menu-config/admin/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Failed to update menu configuration');
    const result = await response.json();
    return result.data;
  },

  async deleteMenuConfig(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/menu-config/admin/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('Failed to delete menu configuration');
  },

  async toggleActiveStatus(id: string): Promise<MenuConfig> {
    const response = await fetch(`${API_BASE_URL}/menu-config/admin/${id}/toggle`, {
      method: 'PATCH'
    });
    if (!response.ok) throw new Error('Failed to toggle menu configuration status');
    const data = await response.json();
    return data.data;
  }
};

const MenuConfigPage: React.FC = () => {
  const [menuConfigs, setMenuConfigs] = useState<MenuConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedConfig, setSelectedConfig] = useState<MenuConfig | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<MenuConfig | null>(null);

  // Fetch menu configurations
  const fetchMenuConfigs = async () => {
    try {
      setLoading(true);
      const data = await menuConfigService.getAllMenuConfigs();
      setMenuConfigs(data);
    } catch (error) {
      toast.error('Failed to fetch menu configurations');
      console.error('Error fetching menu configurations:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMenuConfigs();
  }, []);

  // Handle toggle active status
  const handleToggleActive = async (id: string) => {
    try {
      await menuConfigService.toggleActiveStatus(id);
      toast.success('Status updated successfully');
      fetchMenuConfigs();
    } catch (error) {
      toast.error('Failed to update status');
      console.error('Error toggling status:', error);
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this menu configuration?')) return;

    try {
      await menuConfigService.deleteMenuConfig(id);
      toast.success('Menu configuration deleted successfully');
      fetchMenuConfigs();
    } catch (error) {
      toast.error('Failed to delete menu configuration');
      console.error('Error deleting menu configuration:', error);
    }
  };

  // Handle create new
  const handleCreate = () => {
    setEditingConfig(null);
    setIsModalOpen(true);
  };

  // Handle edit
  const handleEdit = (config: MenuConfig) => {
    setEditingConfig(config);
    setIsModalOpen(true);
  };

  // Handle save (create or update)
  const handleSave = async (configData: MenuConfig) => {
    try {
      if (editingConfig) {
        // Update existing
        await menuConfigService.updateMenuConfig(editingConfig._id, configData);
        toast.success('Menu configuration updated successfully');
      } else {
        // Create new
        await menuConfigService.createMenuConfig(configData);
        toast.success('Menu configuration created successfully');
      }

      setIsModalOpen(false);
      setEditingConfig(null);
      fetchMenuConfigs();
    } catch (error) {
      toast.error(editingConfig ? 'Failed to update menu configuration' : 'Failed to create menu configuration');
      console.error('Error saving menu configuration:', error);
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingConfig(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Menu Configuration</h1>
          <p className="text-gray-600 mt-1">
            Manage navigation menus, categories, and menu images
          </p>
        </div>
        <Button onClick={handleCreate} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Menu Config
        </Button>
      </div>

      {/* Menu Configurations List */}
      <div className="grid gap-6">
        {menuConfigs.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Menu className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No menu configurations found</p>
              <Button onClick={handleCreate}>Create your first menu configuration</Button>
            </CardContent>
          </Card>
        ) : (
          menuConfigs.map((config) => (
            <Card key={config._id} className="overflow-hidden">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {config.name}
                      <Badge variant={config.isActive ? 'default' : 'secondary'}>
                        {config.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      <Badge variant="outline">{config.type}</Badge>
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      {config.categories.length} categories • Version {config.version}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={config.isActive}
                      onCheckedChange={() => handleToggleActive(config._id)}
                    />
                    <Button variant="outline" size="sm" onClick={() => handleEdit(config)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(config._id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Categories */}
                  <div>
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <Menu className="h-4 w-4" />
                      Categories ({config.categories.length})
                    </h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {config.categories.map((category) => (
                        <div
                          key={category.id}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <div>
                            <span className="font-medium">{category.name}</span>
                            <span className="text-sm text-gray-500 ml-2">
                              ({category.subcategories.length} subcategories)
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            {category.featured && (
                              <Badge variant="secondary" className="text-xs">Featured</Badge>
                            )}
                            <Badge variant={category.isActive ? 'default' : 'secondary'} className="text-xs">
                              {category.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Menu Image */}
                  <div>
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <Image className="h-4 w-4" />
                      Menu Image
                    </h4>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <img
                          src={config.menuImage.imageUrl}
                          alt={config.menuImage.title}
                          className="w-16 h-16 object-cover rounded"
                        />
                        <div className="flex-1 min-w-0">
                          <h5 className="font-medium truncate">{config.menuImage.title}</h5>
                          {config.menuImage.subtitle && (
                            <p className="text-sm text-gray-600 truncate">{config.menuImage.subtitle}</p>
                          )}
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className="text-xs">
                              {config.menuImage.position}
                            </Badge>
                            <Badge variant={config.menuImage.isActive ? 'default' : 'secondary'} className="text-xs">
                              {config.menuImage.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex gap-2 mt-4 pt-4 border-t">
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Settings className="h-3 w-3" />
                    Manage Categories
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Image className="h-3 w-3" />
                    Edit Image
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    Preview
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Modal */}
      <MenuConfigModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSave={handleSave}
        editingConfig={editingConfig}
      />
    </div>
  );
};

export default MenuConfigPage;
