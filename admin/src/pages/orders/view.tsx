import * as React from "react";
import { use<PERSON>ara<PERSON>, useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  ChevronLeftIcon,
  PrinterIcon,
  DownloadIcon,
  TruckIcon,
  CalendarIcon,
  CreditCardIcon,
  MapPinIcon,
  UserIcon,
  CheckCircleIcon,
  AlertTriangleIcon,
  ClockIcon,
  PackageIcon,
  XCircleIcon,
  RefreshCwIcon,
  ShoppingBagIcon,
  ReceiptIcon,
  PhoneIcon,
  MailIcon,
  EditIcon,
  SendIcon,
  BellIcon,
  ExternalLinkIcon,
  CopyIcon
} from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/hooks/use-redux";
import {
  fetchOrderById,
  updateOrderStatus,
  clearCurrentOrder,
  type OrderStatus
} from "@/store/slices/ordersSlice";
import {
  updateAdminOrderTracking,
  sendOrderNotification
} from "@/services/adminOrderService";
import { toast } from "@/utils/toast";

export function OrderView() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentOrder: order, loading, error } = useAppSelector((state) => state.orders);

  // State for tracking dialog
  const [isTrackingDialogOpen, setIsTrackingDialogOpen] = React.useState(false);
  const [isNotificationDialogOpen, setIsNotificationDialogOpen] = React.useState(false);
  const [trackingData, setTrackingData] = React.useState({
    trackingNumber: '',
    carrier: '',
    estimatedDelivery: '',
    trackingUrl: '',
    notes: ''
  });
  const [notificationData, setNotificationData] = React.useState({
    type: 'status_update' as 'status_update' | 'shipping' | 'delivery' | 'custom',
    message: ''
  });

  // Fetch order data on component mount
  React.useEffect(() => {
    if (id) {
      dispatch(fetchOrderById(id));
    }

    return () => {
      dispatch(clearCurrentOrder());
    };
  }, [dispatch, id]);

  // Handle errors
  React.useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  const handleUpdateStatus = async (newStatus: OrderStatus) => {
    if (!order) return;

    try {
      await dispatch(updateOrderStatus({
        orderId: order._id,
        status: newStatus,
        notes: `Status updated to ${newStatus} by admin`
      })).unwrap();

      toast.success(`Order status updated to ${newStatus}`);

      // Auto-send notification to customer
      try {
        await sendOrderNotification(order._id, 'status_update',
          `Your order #${order.orderNumber} status has been updated to ${newStatus}`);
        toast.success('Customer notification sent');
      } catch (notificationError) {
        console.warn('Failed to send notification:', notificationError);
      }
    } catch (error: any) {
      toast.error(error || 'Failed to update order status');
    }
  };

  // Handle tracking update
  const handleUpdateTracking = async () => {
    if (!order || !trackingData.trackingNumber || !trackingData.carrier) {
      toast.error('Tracking number and carrier are required');
      return;
    }

    try {
      await updateAdminOrderTracking(order._id, trackingData);
      toast.success('Tracking information updated successfully');
      setIsTrackingDialogOpen(false);

      // Refresh order data
      dispatch(fetchOrderById(order._id));

      // Auto-send shipping notification
      try {
        await sendOrderNotification(order._id, 'shipping',
          `Your order #${order.orderNumber} has been shipped. Tracking number: ${trackingData.trackingNumber}`);
        toast.success('Shipping notification sent to customer');
      } catch (notificationError) {
        console.warn('Failed to send shipping notification:', notificationError);
      }
    } catch (error: any) {
      toast.error(error || 'Failed to update tracking information');
    }
  };

  // Handle sending custom notification
  const handleSendNotification = async () => {
    if (!order || !notificationData.message) {
      toast.error('Message is required');
      return;
    }

    try {
      await sendOrderNotification(order._id, notificationData.type, notificationData.message);
      toast.success('Notification sent to customer');
      setIsNotificationDialogOpen(false);
      setNotificationData({ type: 'status_update', message: '' });
    } catch (error: any) {
      toast.error(error || 'Failed to send notification');
    }
  };

  // Copy tracking number to clipboard
  const copyTrackingNumber = (trackingNumber: string) => {
    navigator.clipboard.writeText(trackingNumber);
    toast.success('Tracking number copied to clipboard');
  };

  const [isPrinting, setIsPrinting] = React.useState(false);
  const [isDownloading, setIsDownloading] = React.useState(false);

  const handlePrint = () => {
    if (!order) return;

    setIsPrinting(true);

    // In a real app, you would format the order for printing
    // For now, we'll just use the browser's print functionality
    setTimeout(() => {
      window.print();
      setIsPrinting(false);
    }, 500);
  };

  const handleDownload = () => {
    if (!order) return;

    setIsDownloading(true);

    // Create order summary content
    const orderContent = `
ORDER SUMMARY
==============================
Order ID: ${order.id}
Date: ${order.date}
Customer: ${order.customer}
Status: ${order.status}
------------------------------
Items:
${orderItems.map(item => `${item.name} x${item.quantity} - ₹${item.price} each = ₹${item.total}`).join('\n')}
------------------------------
Subtotal: ₹${order.amount}
Tax: ₹0
Shipping: ₹0
Total: ₹${order.amount}
==============================
Thank you for shopping with Sajawat Sarees!
    `;

    // Create a blob and download
    const blob = new Blob([orderContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `order_${order.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // In a real app, you would generate a PDF

    setTimeout(() => {
      setIsDownloading(false);
    }, 1000);
  };

  const getStatusBadge = (status: string) => {
    const statusLower = status.toLowerCase();
    switch (statusLower) {
      case "delivered":
        return (
          <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800 shadow-sm shadow-green-800/5 transition-all duration-200 hover:shadow-md">
            <CheckCircleIcon className="mr-1.5 h-4 w-4 text-green-600 dark:text-green-400" />
            Delivered
          </span>
        );
      case "processing":
        return (
          <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800 shadow-sm shadow-yellow-800/5 transition-all duration-200 hover:shadow-md">
            <RefreshCwIcon className="mr-1.5 h-4 w-4 text-yellow-600 dark:text-yellow-400 animate-spin-slow" />
            Processing
          </span>
        );
      case "shipped":
        return (
          <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-800 shadow-sm shadow-blue-800/5 transition-all duration-200 hover:shadow-md">
            <TruckIcon className="mr-1.5 h-4 w-4 text-blue-600 dark:text-blue-400" />
            Shipped
          </span>
        );
      case "pending":
        return (
          <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 border border-orange-200 dark:border-orange-800 shadow-sm shadow-orange-800/5 transition-all duration-200 hover:shadow-md">
            <AlertTriangleIcon className="mr-1.5 h-4 w-4 text-orange-600 dark:text-orange-400" />
            Pending
          </span>
        );
      case "confirmed":
        return (
          <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-300 border border-cyan-200 dark:border-cyan-800 shadow-sm shadow-cyan-800/5 transition-all duration-200 hover:shadow-md">
            <CheckCircleIcon className="mr-1.5 h-4 w-4 text-cyan-600 dark:text-cyan-400" />
            Confirmed
          </span>
        );
      case "cancelled":
      case "returned":
        return (
          <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800 shadow-sm shadow-red-800/5 transition-all duration-200 hover:shadow-md">
            <XCircleIcon className="mr-1.5 h-4 w-4 text-red-600 dark:text-red-400" />
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        );
      case "refunded":
        return (
          <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border border-purple-200 dark:border-purple-800 shadow-sm shadow-purple-800/5 transition-all duration-200 hover:shadow-md">
            <RefreshCwIcon className="mr-1.5 h-4 w-4 text-purple-600 dark:text-purple-400" />
            Refunded
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300 border border-gray-200 dark:border-gray-800 shadow-sm shadow-gray-800/5 transition-all duration-200 hover:shadow-md">
            <PackageIcon className="mr-1.5 h-4 w-4 text-gray-600 dark:text-gray-400" />
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        );
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-96 animate-fade-in">
        <div className="bg-muted/30 p-8 rounded-lg border border-border/40 shadow-sm flex flex-col items-center max-w-md mx-auto animate-slide-up">
          <RefreshCwIcon className="h-12 w-12 text-primary animate-spin mb-4" />
          <h2 className="text-2xl font-bold mb-2">Loading Order</h2>
          <p className="text-muted-foreground text-center">
            Please wait while we fetch the order details...
          </p>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="flex flex-col items-center justify-center h-96 animate-fade-in">
        <div className="bg-muted/30 p-8 rounded-lg border border-border/40 shadow-sm flex flex-col items-center max-w-md mx-auto animate-slide-up">
          <div className="bg-red-100 dark:bg-red-900/20 p-4 rounded-full mb-4 shadow-sm">
            <PackageIcon className="h-12 w-12 text-red-500 dark:text-red-400" />
          </div>
          <h2 className="text-2xl font-bold mb-2">Order Not Found</h2>
          <p className="text-muted-foreground text-center mb-6">
            The order you're looking for doesn't exist or may have been removed.
          </p>
          <div className="flex gap-3">
            <Button asChild variant="outline">
              <Link to="/">
                <ShoppingBagIcon className="mr-2 h-4 w-4" />
                Dashboard
              </Link>
            </Button>
            <Button asChild>
              <Link to="/orders">
                <ChevronLeftIcon className="mr-2 h-4 w-4" />
                Back to Orders
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/orders">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <ShoppingBagIcon className="h-6 w-6 text-primary/80" />
              Order #{order.orderNumber}
            </h1>
            <div className="ml-2">{getStatusBadge(order.status)}</div>
          </div>
          <p className="text-muted-foreground flex items-center gap-1.5">
            <CalendarIcon className="h-4 w-4" />
            Placed on {new Date(order.placedAt).toLocaleDateString()}
          </p>
        </div>
        <div className="flex gap-2">
          {/* Tracking Dialog */}
          <Dialog open={isTrackingDialogOpen} onOpenChange={setIsTrackingDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="hover:bg-primary/5 transition-colors">
                <TruckIcon className="mr-2 h-4 w-4" />
                Update Tracking
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Update Tracking Information</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="trackingNumber">Tracking Number *</Label>
                  <Input
                    id="trackingNumber"
                    value={trackingData.trackingNumber}
                    onChange={(e) => setTrackingData(prev => ({ ...prev, trackingNumber: e.target.value }))}
                    placeholder="Enter tracking number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="carrier">Carrier *</Label>
                  <Select value={trackingData.carrier} onValueChange={(value) => setTrackingData(prev => ({ ...prev, carrier: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select carrier" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fedex">FedEx</SelectItem>
                      <SelectItem value="ups">UPS</SelectItem>
                      <SelectItem value="dhl">DHL</SelectItem>
                      <SelectItem value="bluedart">Blue Dart</SelectItem>
                      <SelectItem value="dtdc">DTDC</SelectItem>
                      <SelectItem value="indiapost">India Post</SelectItem>
                      <SelectItem value="ecom">Ecom Express</SelectItem>
                      <SelectItem value="delhivery">Delhivery</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="estimatedDelivery">Estimated Delivery</Label>
                  <Input
                    id="estimatedDelivery"
                    type="date"
                    value={trackingData.estimatedDelivery}
                    onChange={(e) => setTrackingData(prev => ({ ...prev, estimatedDelivery: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="trackingUrl">Tracking URL</Label>
                  <Input
                    id="trackingUrl"
                    value={trackingData.trackingUrl}
                    onChange={(e) => setTrackingData(prev => ({ ...prev, trackingUrl: e.target.value }))}
                    placeholder="https://..."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={trackingData.notes}
                    onChange={(e) => setTrackingData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Additional notes..."
                    rows={3}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsTrackingDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleUpdateTracking}>
                    Update Tracking
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* Notification Dialog */}
          <Dialog open={isNotificationDialogOpen} onOpenChange={setIsNotificationDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="hover:bg-primary/5 transition-colors">
                <BellIcon className="mr-2 h-4 w-4" />
                Notify Customer
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Send Customer Notification</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="notificationType">Notification Type</Label>
                  <Select value={notificationData.type} onValueChange={(value: any) => setNotificationData(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="status_update">Status Update</SelectItem>
                      <SelectItem value="shipping">Shipping Update</SelectItem>
                      <SelectItem value="delivery">Delivery Update</SelectItem>
                      <SelectItem value="custom">Custom Message</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Message *</Label>
                  <Textarea
                    id="message"
                    value={notificationData.message}
                    onChange={(e) => setNotificationData(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Enter your message..."
                    rows={4}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsNotificationDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSendNotification}>
                    <SendIcon className="mr-2 h-4 w-4" />
                    Send Notification
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handlePrint}
            disabled={isPrinting}
          >
            <PrinterIcon className={`mr-2 h-4 w-4 ${isPrinting ? 'animate-pulse' : ''}`} />
            {isPrinting ? 'Printing...' : 'Print'}
          </Button>
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handleDownload}
            disabled={isDownloading}
          >
            {isDownloading ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <DownloadIcon className="mr-2 h-4 w-4" />
            )}
            {isDownloading ? 'Downloading...' : 'Download'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <ShoppingBagIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Order Items</h2>
            </div>
            <div className="space-y-4">
              {order.items.map((item, index) => (
                <div
                  key={item._id || index}
                  className="flex items-center gap-4 p-4 border border-border/40 rounded-lg hover:bg-muted/30 transition-all duration-200"
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animation: 'fade-in 0.5s ease-out forwards',
                    opacity: 0
                  }}
                >
                  <div className="w-20 h-20 rounded-md overflow-hidden border border-border/60 shadow-sm bg-muted/30">
                    <img
                      src={item.productDetails?.image || `http://localhost:3000/assets/imagesVidoes/images/products/${item.productDetails?.sku || 'placeholder'}.jpg`}
                      alt={item.productDetails?.name || 'Product'}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder-product.jpg';
                      }}
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-lg">{item.productDetails?.name || 'Product'}</h3>
                    {item.productDetails?.sku && (
                      <div className="text-xs text-muted-foreground mt-0.5">
                        SKU: {item.productDetails.sku}
                      </div>
                    )}
                    {item.productDetails?.brand && (
                      <div className="text-xs text-muted-foreground">
                        Brand: {item.productDetails.brand}
                      </div>
                    )}
                    <div className="text-sm text-muted-foreground flex items-center gap-1.5 mt-2">
                      <ReceiptIcon className="h-3.5 w-3.5" />
                      ₹{(item.unitPrice || item.price || 0).toLocaleString()} × {item.quantity}
                    </div>
                    {(item.variant || item.selectedVariant) && (
                      <div className="text-xs text-muted-foreground mt-1 flex flex-wrap gap-2">
                        {(item.variant?.color || item.selectedVariant?.color) && (
                          <span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                            Color: {item.variant?.color || item.selectedVariant?.color}
                          </span>
                        )}
                        {(item.variant?.size || item.selectedVariant?.size) && (
                          <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                            Size: {item.variant?.size || item.selectedVariant?.size}
                          </span>
                        )}
                        {(item.variant?.style || item.selectedVariant?.style) && (
                          <span className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">
                            Style: {item.variant?.style || item.selectedVariant?.style}
                          </span>
                        )}
                      </div>
                    )}
                    {item.tracking?.trackingNumber && (
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          Tracking: {item.tracking.trackingNumber}
                        </span>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0"
                          onClick={() => copyTrackingNumber(item.tracking!.trackingNumber)}
                        >
                          <CopyIcon className="h-3 w-3" />
                        </Button>
                        {item.tracking.trackingUrl && (
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0"
                            asChild
                          >
                            <a href={item.tracking.trackingUrl} target="_blank" rel="noopener noreferrer">
                              <ExternalLinkIcon className="h-3 w-3" />
                            </a>
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-lg">₹{(item.totalPrice || (item.unitPrice || item.price || 0) * item.quantity).toLocaleString()}</div>
                    {item.discountPrice && item.discountPrice > 0 && (
                      <div className="text-xs text-green-600 mt-0.5">
                        Discount: ₹{item.discountPrice.toLocaleString()}
                      </div>
                    )}
                    {item.status && item.status !== order.status && (
                      <div className="text-xs mt-1">
                        {getStatusBadge(item.status)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 border-t pt-4">
              <div className="flex justify-between py-1.5">
                <span className="text-muted-foreground">Subtotal</span>
                <span>₹{order.totals.subtotal.toLocaleString()}</span>
              </div>
              <div className="flex justify-between py-1.5">
                <span className="text-muted-foreground">Shipping</span>
                <span>₹{order.totals.shipping.toLocaleString()}</span>
              </div>
              <div className="flex justify-between py-1.5">
                <span className="text-muted-foreground">Tax</span>
                <span>₹{order.totals.tax.toLocaleString()}</span>
              </div>
              {order.totals.discount > 0 && (
                <div className="flex justify-between py-1.5 text-green-600">
                  <span>Discount</span>
                  <span>-₹{order.totals.discount.toLocaleString()}</span>
                </div>
              )}
              <div className="flex justify-between py-3 font-medium text-lg border-t mt-2">
                <span>Total</span>
                <span className="text-primary">₹{order.totals.total.toLocaleString()}</span>
              </div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <ClockIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Order Timeline</h2>
            </div>
            <div className="space-y-6">
              {order.timeline && order.timeline.length > 0 ? (
                order.timeline.map((entry, index) => {
                  const isLast = index === order.timeline.length - 1;
                  const getTimelineIcon = (status: string) => {
                    switch (status.toLowerCase()) {
                      case 'pending':
                        return <AlertTriangleIcon className="h-4 w-4 text-orange-500" />;
                      case 'confirmed':
                        return <CheckCircleIcon className="h-4 w-4 text-blue-500" />;
                      case 'processing':
                        return <RefreshCwIcon className="h-4 w-4 text-yellow-500 animate-spin-slow" />;
                      case 'shipped':
                        return <TruckIcon className="h-4 w-4 text-blue-600" />;
                      case 'delivered':
                        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
                      case 'cancelled':
                      case 'returned':
                        return <XCircleIcon className="h-4 w-4 text-red-500" />;
                      case 'refunded':
                        return <RefreshCwIcon className="h-4 w-4 text-purple-500" />;
                      default:
                        return <PackageIcon className="h-4 w-4 text-gray-500" />;
                    }
                  };

                  const getStatusColor = (status: string) => {
                    switch (status.toLowerCase()) {
                      case 'delivered':
                        return 'bg-green-500';
                      case 'cancelled':
                      case 'returned':
                        return 'bg-red-500';
                      case 'refunded':
                        return 'bg-purple-500';
                      default:
                        return 'bg-primary';
                    }
                  };

                  return (
                    <div key={index} className={`relative pl-8 ${!isLast ? 'pb-6 border-l-2 border-primary' : 'pb-0'}`}>
                      <div className={`absolute left-[-8px] top-0 w-4 h-4 rounded-full ${getStatusColor(entry.status)} shadow-sm shadow-primary/30 animate-pulse-slow`}></div>
                      <div className="font-medium flex items-center gap-2">
                        {getTimelineIcon(entry.status)}
                        {entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(entry.timestamp).toLocaleString()}
                      </div>
                      <div className="mt-1 text-sm">
                        {entry.message}
                      </div>
                      {entry.updatedBy && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Updated by: {entry.updatedBy}
                        </div>
                      )}
                    </div>
                  );
                })
              ) : (
                <div className="relative pl-8 pb-0">
                  <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-primary shadow-sm shadow-primary/30 animate-pulse-slow"></div>
                  <div className="font-medium flex items-center gap-2">
                    <ShoppingBagIcon className="h-4 w-4 text-primary/70" />
                    Order Placed
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(order.placedAt).toLocaleString()}
                  </div>
                  <div className="mt-1 text-sm">
                    Order #{order.orderNumber} was placed
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <UserIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Customer</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border border-border/40">
                <div className="bg-primary/10 p-3 rounded-full">
                  <UserIcon className="h-6 w-6 text-primary" />
                </div>
                <div className="flex-1">
                  <div className="font-semibold text-lg">
                    {order.userId?.profile?.firstName && order.userId?.profile?.lastName
                      ? `${order.userId.profile.firstName} ${order.userId.profile.lastName}`
                      : order.shippingAddress?.fullName || 'Customer'
                    }
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {order.userId ? 'Registered Customer' : 'Guest Customer'}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-3">
                {(order.userId?.email || order.customerInfo?.email) && (
                  <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                    <MailIcon className="h-4 w-4 text-primary" />
                    <div>
                      <div className="text-sm font-medium">Email</div>
                      <div className="text-sm text-muted-foreground">
                        {order.userId?.email || order.customerInfo?.email}
                      </div>
                    </div>
                  </div>
                )}

                {(order.userId?.phone || order.customerInfo?.phone || order.shippingAddress?.phone) && (
                  <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                    <PhoneIcon className="h-4 w-4 text-primary" />
                    <div>
                      <div className="text-sm font-medium">Phone Number</div>
                      <div className="text-sm text-muted-foreground">
                        {order.userId?.phone || order.customerInfo?.phone || order.shippingAddress?.phone}
                      </div>
                    </div>
                  </div>
                )}

                {order.userId?._id && (
                  <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                    <UserIcon className="h-4 w-4 text-primary" />
                    <div>
                      <div className="text-sm font-medium">Customer ID</div>
                      <div className="text-sm text-muted-foreground font-mono">
                        {order.userId._id}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            {order.userId && (
              <Button asChild variant="outline" className="w-full hover:bg-primary/5 transition-colors">
                <Link to={`/customers/${order.userId}`}>
                  <UserIcon className="mr-2 h-4 w-4" />
                  View Customer Profile
                </Link>
              </Button>
            )}
          </div>

          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <MapPinIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Shipping & Tracking</h2>
            </div>
            <div className="space-y-4">
              <div className="p-4 bg-muted/30 rounded-lg border border-border/40">
                <div className="flex items-center gap-2 mb-3">
                  <MapPinIcon className="h-5 w-5 text-primary" />
                  <h3 className="font-semibold">Shipping Address</h3>
                </div>
                <div className="space-y-2">
                  <div className="font-medium text-lg">{order.shippingAddress?.fullName}</div>
                  {order.shippingAddress?.phone && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <PhoneIcon className="h-3.5 w-3.5" />
                      {order.shippingAddress.phone}
                    </div>
                  )}
                  <div className="text-sm text-muted-foreground leading-relaxed">
                    {order.shippingAddress?.addressLine1}<br />
                    {order.shippingAddress?.addressLine2 && (
                      <>{order.shippingAddress.addressLine2}<br /></>
                    )}
                    {order.shippingAddress?.landmark && (
                      <>Near {order.shippingAddress.landmark}<br /></>
                    )}
                    {order.shippingAddress?.city}, {order.shippingAddress?.state}<br />
                    {order.shippingAddress?.pincode || order.shippingAddress?.postalCode}<br />
                    {order.shippingAddress?.country || 'India'}
                  </div>
                </div>
              </div>

              {/* Billing Address if different */}
              {order.billingAddress && !order.billingAddress?.sameAsShipping && (
                <div className="p-4 bg-muted/30 rounded-lg border border-border/40">
                  <div className="flex items-center gap-2 mb-3">
                    <CreditCardIcon className="h-5 w-5 text-primary" />
                    <h3 className="font-semibold">Billing Address</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="font-medium text-lg">{order.billingAddress?.fullName}</div>
                    {order.billingAddress?.phone && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <PhoneIcon className="h-3.5 w-3.5" />
                        {order.billingAddress.phone}
                      </div>
                    )}
                    <div className="text-sm text-muted-foreground leading-relaxed">
                      {order.billingAddress?.addressLine1}<br />
                      {order.billingAddress?.addressLine2 && (
                        <>{order.billingAddress.addressLine2}<br /></>
                      )}
                      {order.billingAddress?.landmark && (
                        <>Near {order.billingAddress.landmark}<br /></>
                      )}
                      {order.billingAddress?.city}, {order.billingAddress?.state}<br />
                      {order.billingAddress?.pincode || order.billingAddress?.postalCode}<br />
                      {order.billingAddress?.country || 'India'}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <TruckIcon className="h-5 w-5 text-primary" />
                <div>
                  <div className="font-medium">{order.delivery?.type || 'Standard'} Delivery</div>
                  <div className="text-sm text-muted-foreground">
                    {order.delivery?.estimatedDate
                      ? `Estimated delivery: ${new Date(order.delivery.estimatedDate).toLocaleDateString()}`
                      : 'Estimated delivery: 3-5 days'
                    }
                  </div>
                  {order.delivery?.actualDate && (
                    <div className="text-sm text-green-600 font-medium">
                      Delivered on: {new Date(order.delivery.actualDate).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>

              {/* Tracking Information */}
              {order.items.some(item => item.tracking?.trackingNumber) && (
                <div className="space-y-3">
                  <h3 className="font-medium text-sm">Tracking Information</h3>
                  {order.items.map((item, index) =>
                    item.tracking?.trackingNumber && (
                      <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                        <PackageIcon className="h-5 w-5 text-primary" />
                        <div className="flex-1">
                          <div className="font-medium">{item.productDetails?.name}</div>
                          <div className="text-sm text-muted-foreground flex items-center gap-2 mt-1">
                            <span>Carrier: {item.tracking.carrier}</span>
                            <span className="font-mono bg-background px-2 py-0.5 rounded border border-border/60">
                              {item.tracking.trackingNumber}
                            </span>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                              onClick={() => copyTrackingNumber(item.tracking!.trackingNumber)}
                            >
                              <CopyIcon className="h-3 w-3" />
                            </Button>
                            {item.tracking.trackingUrl && (
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-6 w-6 p-0"
                                asChild
                              >
                                <a href={item.tracking.trackingUrl} target="_blank" rel="noopener noreferrer">
                                  <ExternalLinkIcon className="h-3 w-3" />
                                </a>
                              </Button>
                            )}
                          </div>
                          {item.tracking.estimatedDelivery && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Est. delivery: {new Date(item.tracking.estimatedDelivery).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  )}
                </div>
              )}

              {/* Delivery Attempts */}
              {order.delivery?.attempts && order.delivery.attempts.length > 0 && (
                <div className="space-y-3">
                  <h3 className="font-medium text-sm">Delivery Attempts</h3>
                  {order.delivery.attempts.map((attempt, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                      <CalendarIcon className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">
                          Attempt #{index + 1} - {attempt.status}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(attempt.date).toLocaleDateString()}
                        </div>
                        {attempt.reason && (
                          <div className="text-sm text-muted-foreground">
                            Reason: {attempt.reason}
                          </div>
                        )}
                        {attempt.deliveredTo && (
                          <div className="text-sm text-green-600">
                            Delivered to: {attempt.deliveredTo}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <CreditCardIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Payment</h2>
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <CreditCardIcon className="h-5 w-5 text-primary" />
                <div>
                  <div className="font-medium">{order.payment?.method || 'Payment Method'}</div>
                  {order.payment?.transactionId && (
                    <div className="text-sm text-muted-foreground">
                      Transaction ID: <span className="font-mono">{order.payment.transactionId}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <CalendarIcon className="h-5 w-5 text-primary" />
                <div>
                  <div className="font-medium">Payment Date</div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(order.placedAt).toLocaleDateString()}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <CheckCircleIcon className="h-5 w-5 text-green-500" />
                <div>
                  <div className="font-medium">Payment Status</div>
                  <div className={`text-sm font-medium ${
                    order.payment?.status === 'completed' ? 'text-green-600' :
                    order.payment?.status === 'pending' ? 'text-yellow-600' :
                    order.payment?.status === 'failed' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {order.payment?.status ?
                      order.payment.status.charAt(0).toUpperCase() + order.payment.status.slice(1) :
                      'Paid'
                    }
                  </div>
                </div>
              </div>

              {order.payment?.amount && (
                <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                  <ReceiptIcon className="h-5 w-5 text-primary" />
                  <div>
                    <div className="font-medium">Amount Paid</div>
                    <div className="text-sm text-muted-foreground">
                      ₹{order.payment.amount.toLocaleString()}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Order Notes Section */}
          {(order.notes || order.internalNotes || order.delivery?.instructions || order.giftMessage) && (
            <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
              <div className="flex items-center gap-2 mb-4">
                <div className="bg-primary/10 p-1.5 rounded-full">
                  <EditIcon className="h-5 w-5 text-primary/80" />
                </div>
                <h2 className="text-xl font-semibold">Order Notes & Instructions</h2>
              </div>
              <div className="space-y-3">
                {order.notes && (
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-1">Customer Notes</div>
                    <div className="text-sm text-blue-700 dark:text-blue-400">{order.notes}</div>
                  </div>
                )}
                {order.delivery?.instructions && (
                  <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div className="text-sm font-medium text-green-800 dark:text-green-300 mb-1">Delivery Instructions</div>
                    <div className="text-sm text-green-700 dark:text-green-400">{order.delivery.instructions}</div>
                  </div>
                )}
                {order.giftMessage && (
                  <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                    <div className="text-sm font-medium text-purple-800 dark:text-purple-300 mb-1">Gift Message</div>
                    <div className="text-sm text-purple-700 dark:text-purple-400">{order.giftMessage}</div>
                  </div>
                )}
                {order.internalNotes && (
                  <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                    <div className="text-sm font-medium text-orange-800 dark:text-orange-300 mb-1">Internal Notes (Admin Only)</div>
                    <div className="text-sm text-orange-700 dark:text-orange-400">{order.internalNotes}</div>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <RefreshCwIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Update Status</h2>
            </div>
            <div className="space-y-3">
              <Button
                variant={order.status === "pending" ? "default" : "outline"}
                className={`w-full justify-start transition-all duration-200 ${order.status === "pending" ? "" : "hover:bg-orange-50 hover:text-orange-600 hover:border-orange-200 dark:hover:bg-orange-900/20 dark:hover:text-orange-400 dark:hover:border-orange-800"}`}
                onClick={() => handleUpdateStatus("pending")}
                disabled={order.status === "pending"}
              >
                <AlertTriangleIcon className="mr-2 h-4 w-4 text-orange-500" />
                Mark as Pending
              </Button>

              <Button
                variant={order.status === "confirmed" ? "default" : "outline"}
                className={`w-full justify-start transition-all duration-200 ${order.status === "confirmed" ? "" : "hover:bg-cyan-50 hover:text-cyan-600 hover:border-cyan-200 dark:hover:bg-cyan-900/20 dark:hover:text-cyan-400 dark:hover:border-cyan-800"}`}
                onClick={() => handleUpdateStatus("confirmed")}
                disabled={order.status === "confirmed"}
              >
                <CheckCircleIcon className="mr-2 h-4 w-4 text-cyan-500" />
                Mark as Confirmed
              </Button>

              <Button
                variant={order.status === "processing" ? "default" : "outline"}
                className={`w-full justify-start transition-all duration-200 ${order.status === "processing" ? "" : "hover:bg-yellow-50 hover:text-yellow-600 hover:border-yellow-200 dark:hover:bg-yellow-900/20 dark:hover:text-yellow-400 dark:hover:border-yellow-800"}`}
                onClick={() => handleUpdateStatus("processing")}
                disabled={order.status === "processing"}
              >
                <RefreshCwIcon className="mr-2 h-4 w-4 text-yellow-500 animate-spin-slow" />
                Mark as Processing
              </Button>

              <Button
                variant={order.status === "shipped" ? "default" : "outline"}
                className={`w-full justify-start transition-all duration-200 ${order.status === "shipped" ? "" : "hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 dark:hover:bg-blue-900/20 dark:hover:text-blue-400 dark:hover:border-blue-800"}`}
                onClick={() => handleUpdateStatus("shipped")}
                disabled={order.status === "shipped"}
              >
                <TruckIcon className="mr-2 h-4 w-4 text-blue-500" />
                Mark as Shipped
              </Button>

              <Button
                variant={order.status === "delivered" ? "default" : "outline"}
                className={`w-full justify-start transition-all duration-200 ${order.status === "delivered" ? "" : "hover:bg-green-50 hover:text-green-600 hover:border-green-200 dark:hover:bg-green-900/20 dark:hover:text-green-400 dark:hover:border-green-800"}`}
                onClick={() => handleUpdateStatus("delivered")}
                disabled={order.status === "delivered"}
              >
                <CheckCircleIcon className="mr-2 h-4 w-4 text-green-500" />
                Mark as Delivered
              </Button>

              <Button
                variant={order.status === "cancelled" ? "destructive" : "outline"}
                className={`w-full justify-start transition-all duration-200 ${order.status === "cancelled" ? "" : "hover:bg-red-50 hover:text-red-600 hover:border-red-200 dark:hover:bg-red-900/20 dark:hover:text-red-400 dark:hover:border-red-800"}`}
                onClick={() => handleUpdateStatus("cancelled")}
                disabled={order.status === "cancelled"}
              >
                <XCircleIcon className="mr-2 h-4 w-4 text-red-500" />
                Mark as Cancelled
              </Button>

              <Button
                variant={order.status === "returned" ? "destructive" : "outline"}
                className={`w-full justify-start transition-all duration-200 ${order.status === "returned" ? "" : "hover:bg-red-50 hover:text-red-600 hover:border-red-200 dark:hover:bg-red-900/20 dark:hover:text-red-400 dark:hover:border-red-800"}`}
                onClick={() => handleUpdateStatus("returned")}
                disabled={order.status === "returned"}
              >
                <RefreshCwIcon className="mr-2 h-4 w-4 text-red-500" />
                Mark as Returned
              </Button>

              <Button
                variant={order.status === "refunded" ? "default" : "outline"}
                className={`w-full justify-start transition-all duration-200 ${order.status === "refunded" ? "" : "hover:bg-purple-50 hover:text-purple-600 hover:border-purple-200 dark:hover:bg-purple-900/20 dark:hover:text-purple-400 dark:hover:border-purple-800"}`}
                onClick={() => handleUpdateStatus("refunded")}
                disabled={order.status === "refunded"}
              >
                <RefreshCwIcon className="mr-2 h-4 w-4 text-purple-500" />
                Mark as Refunded
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
