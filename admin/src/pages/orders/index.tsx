import * as React from "react";
import { Link } from "react-router-dom";
import {
  SearchIcon,
  FilterIcon,
  ArrowUpDownIcon,
  PackageIcon,
  CheckCircleIcon,
  ClockIcon,
  TruckIcon,
  XCircleIcon,
  RefreshCwIcon,
  ShoppingBagIcon,
  AlertCircleIcon
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useAppSelector, useAppDispatch } from "@/hooks/use-redux";
import {
  fetchOrders,
  updateOrderStatus,
  clearError,
  type OrderStatus
} from "@/store/slices/ordersSlice";
import { toast } from "@/utils/toast";

export function Orders() {
  const dispatch = useAppDispatch();
  const { items: orders, loading, error, pagination } = useAppSelector((state) => state.orders);
  const [searchTerm, setSearchTerm] = React.useState("");
  const [statusFilter, setStatusFilter] = React.useState("");
  const [sortOption, setSortOption] = React.useState("");
  const [currentPage, setCurrentPage] = React.useState(1);
  const itemsPerPage = 10; // Show 10 orders per page
  const [selectedOrders, setSelectedOrders] = React.useState<string[]>([]);
  const [bulkActionStatus, setBulkActionStatus] = React.useState<OrderStatus | "">("");

  // Fetch orders on component mount and when filters change
  React.useEffect(() => {
    const filters = {
      page: currentPage,
      limit: itemsPerPage,
      ...(searchTerm && { search: searchTerm }),
      ...(statusFilter && { status: statusFilter }),
      ...(sortOption && {
        sort_by: sortOption.split('-')[0] as any,
        sort_order: sortOption.split('-')[1] as 'asc' | 'desc'
      })
    };

    dispatch(fetchOrders(filters));
  }, [dispatch, currentPage, searchTerm, statusFilter, sortOption]);

  // Handle errors
  React.useEffect(() => {
    if (error) {
      toast.error(error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  // Since filtering and sorting is now done on the backend, we use orders directly
  const filteredOrders = orders || [];
  const paginatedOrders = orders || []; // Backend handles pagination
  const totalPages = pagination?.totalPages || 1;

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800 shadow-sm shadow-green-800/5";
      case "processing":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800 shadow-sm shadow-yellow-800/5";
      case "shipped":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-800 shadow-sm shadow-blue-800/5";
      case "pending":
        return "bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 border border-orange-200 dark:border-orange-800 shadow-sm shadow-orange-800/5";
      case "confirmed":
        return "bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-300 border border-cyan-200 dark:border-cyan-800 shadow-sm shadow-cyan-800/5";
      case "cancelled":
      case "returned":
        return "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800 shadow-sm shadow-red-800/5";
      case "refunded":
        return "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border border-purple-200 dark:border-purple-800 shadow-sm shadow-purple-800/5";
      default:
        return "bg-gray-100 dark:bg-gray-800/50 text-gray-800 dark:text-gray-300 border border-gray-200 dark:border-gray-700 shadow-sm";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return <CheckCircleIcon className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />;
      case "processing":
        return <RefreshCwIcon className="h-3.5 w-3.5 text-yellow-600 dark:text-yellow-400 animate-spin-slow" />;
      case "shipped":
        return <TruckIcon className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />;
      case "pending":
        return <AlertCircleIcon className="h-3.5 w-3.5 text-orange-600 dark:text-orange-400" />;
      case "confirmed":
        return <CheckCircleIcon className="h-3.5 w-3.5 text-cyan-600 dark:text-cyan-400" />;
      case "cancelled":
      case "returned":
        return <XCircleIcon className="h-3.5 w-3.5 text-red-600 dark:text-red-400" />;
      case "refunded":
        return <RefreshCwIcon className="h-3.5 w-3.5 text-purple-600 dark:text-purple-400" />;
      default:
        return <PackageIcon className="h-3.5 w-3.5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const handleUpdateStatus = async (orderId: string, newStatus: OrderStatus) => {
    try {
      await dispatch(updateOrderStatus({ orderId, status: newStatus })).unwrap();
      toast.success(`Order status updated to ${newStatus}`);
    } catch (error: any) {
      toast.error(error || 'Failed to update order status');
    }
  };

  const handleSelectOrder = (id: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedOrders([...selectedOrders, id]);
    } else {
      setSelectedOrders(selectedOrders.filter(orderId => orderId !== id));
    }
  };

  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected && paginatedOrders.length > 0) {
      setSelectedOrders(paginatedOrders.map(order => order._id));
    } else {
      setSelectedOrders([]);
    }
  };

  const handleBulkAction = async () => {
    if (!bulkActionStatus || selectedOrders.length === 0) return;

    try {
      // Update status for all selected orders
      await Promise.all(
        selectedOrders.map(orderId =>
          dispatch(updateOrderStatus({ orderId, status: bulkActionStatus as OrderStatus })).unwrap()
        )
      );

      // Reset selection and bulk action
      setSelectedOrders([]);
      setBulkActionStatus("");

      // Show success message
      toast.success(`${selectedOrders.length} orders updated to ${bulkActionStatus} status`);
    } catch (error: any) {
      toast.error(error || 'Failed to update orders');
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="animate-slide-up">
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <ShoppingBagIcon className="h-8 w-8 text-primary/80" />
            Orders
          </h1>
          <p className="text-muted-foreground mt-1">Manage and track all customer orders</p>
        </div>
        <div className="flex gap-2 animate-slide-up">
          <Button asChild variant="outline">
            <Link to="/orders/processing">
              <RefreshCwIcon className="mr-2 h-4 w-4 text-yellow-500" />
              Processing
            </Link>
          </Button>
          <Button asChild>
            <Link to="/orders/pending">
              <AlertCircleIcon className="mr-2 h-4 w-4" />
              Pending Orders
            </Link>
          </Button>
        </div>
      </div>

      <Separator className="animate-fade-in" />

      <div className="bg-card p-5 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search by order ID or customer name..."
              className="w-full pl-10 transition-all duration-200 focus:border-primary/30"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative min-w-[180px]">
              <select
                className="w-full h-9 px-3 py-2 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
                <option value="returned">Returned</option>
                <option value="refunded">Refunded</option>
              </select>
              <FilterIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
            </div>
            <div className="relative min-w-[180px]">
              <select
                className="w-full h-9 px-3 py-2 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value)}
              >
                <option value="">Sort By</option>
                <option value="placedAt-desc">Date (Newest)</option>
                <option value="placedAt-asc">Date (Oldest)</option>
                <option value="total-desc">Amount (High to Low)</option>
                <option value="total-asc">Amount (Low to High)</option>
                <option value="orderNumber-asc">Order Number</option>
                <option value="status-asc">Status</option>
              </select>
              <ArrowUpDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
            </div>
          </div>
        </div>

        {selectedOrders.length > 0 && (
          <div className="mt-4 p-3 bg-muted/30 rounded-lg border border-border/40 flex flex-wrap items-center gap-3">
            <span className="text-sm font-medium">{selectedOrders.length} orders selected</span>
            <div className="flex-1"></div>
            <div className="flex items-center gap-2">
              <select
                className="h-9 px-3 py-2 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
                value={bulkActionStatus}
                onChange={(e) => setBulkActionStatus(e.target.value as OrderStatus | "")}
              >
                <option value="">Bulk Action</option>
                <option value="pending">Mark as Pending</option>
                <option value="confirmed">Mark as Confirmed</option>
                <option value="processing">Mark as Processing</option>
                <option value="shipped">Mark as Shipped</option>
                <option value="delivered">Mark as Delivered</option>
                <option value="cancelled">Mark as Cancelled</option>
              </select>
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkAction}
                disabled={!bulkActionStatus}
                className="h-9 hover:bg-primary/5 transition-colors"
              >
                Apply
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedOrders([])}
                className="h-9 hover:bg-primary/5 transition-colors"
              >
                Clear Selection
              </Button>
            </div>
          </div>
        )}
      </div>

      <div className="bg-card rounded-lg shadow-sm border border-border/40 overflow-hidden transition-all duration-200 hover:shadow-md animate-slide-up">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-primary focus:ring-primary/30"
                    checked={selectedOrders.length === paginatedOrders.length && paginatedOrders.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    disabled={paginatedOrders.length === 0}
                  />
                </th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Order ID</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Customer</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Date</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Amount</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border/60">
              {loading ? (
                <tr>
                  <td colSpan={7} className="p-8 text-center">
                    <div className="flex flex-col items-center justify-center space-y-3 animate-pulse-slow">
                      <RefreshCwIcon className="h-8 w-8 text-primary/70 animate-spin" />
                      <p className="text-muted-foreground">Loading orders...</p>
                    </div>
                  </td>
                </tr>
              ) : filteredOrders.length === 0 ? (
                <tr>
                  <td colSpan={7} className="p-8 text-center">
                    <div className="flex flex-col items-center justify-center space-y-3 animate-fade-in">
                      <div className="bg-muted/30 p-4 rounded-full">
                        <PackageIcon className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <p className="text-muted-foreground">No orders found</p>
                      {(searchTerm || statusFilter || sortOption) && (
                        <p className="text-xs text-muted-foreground">Try adjusting your filters</p>
                      )}
                    </div>
                  </td>
                </tr>
              ) : (
                paginatedOrders.map((order, index) => (
                  <tr
                    key={order._id}
                    className="bg-card hover:bg-muted/30 transition-colors"
                    style={{
                      animationDelay: `${index * 50}ms`,
                      animation: 'fade-in 0.5s ease-out forwards',
                      opacity: 0
                    }}
                  >
                    <td className="px-4 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-primary focus:ring-primary/30"
                        checked={selectedOrders.includes(order._id)}
                        onChange={(e) => handleSelectOrder(order._id, e.target.checked)}
                      />
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="font-medium text-foreground flex items-center gap-1.5">
                        <span className="bg-primary/10 p-1 rounded">
                          <ShoppingBagIcon className="h-3.5 w-3.5 text-primary/70" />
                        </span>
                        #{order.orderNumber}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="font-medium">
                        {order.userId?.profile?.firstName && order.userId?.profile?.lastName
                          ? `${order.userId.profile.firstName} ${order.userId.profile.lastName}`
                          : order.shippingAddress?.fullName || 'Guest User'
                        }
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {order.userId?.email || order.userId?.phone || ''}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      {new Date(order.placedAt).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="relative group">
                        <span className={`${getStatusBadgeClass(order.status)} inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium cursor-pointer transition-all duration-200 hover:shadow-md`}>
                          {getStatusIcon(order.status)}
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                        <div className="absolute left-0 mt-2 w-48 bg-popover border border-border shadow-lg rounded-md p-2 hidden group-hover:block z-10 animate-slide-up">
                          <div className="text-xs font-medium mb-2 text-muted-foreground">Update Status:</div>
                          <div className="space-y-1">
                            {["pending", "confirmed", "processing", "shipped", "delivered", "cancelled"].map((status) => (
                              <div
                                key={status}
                                className={`flex items-center gap-1.5 px-2.5 py-1.5 rounded text-xs cursor-pointer hover:bg-muted transition-colors ${
                                  status === order.status ? 'bg-muted font-medium' : ''
                                }`}
                                onClick={() => handleUpdateStatus(order._id, status as OrderStatus)}
                              >
                                {getStatusIcon(status)}
                                {status.charAt(0).toUpperCase() + status.slice(1)}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="font-medium">₹{order.totals?.total?.toLocaleString() || 0}</div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" asChild className="h-8 px-3 text-xs hover:bg-primary/5 transition-colors">
                          <Link to={`/orders/${order._id}`}>View Details</Link>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 bg-muted/30 p-4 rounded-lg border border-border/40 shadow-sm animate-slide-up">
        <div className="text-sm text-muted-foreground">
          {pagination && pagination.total > 0
            ? `Showing ${((pagination.page - 1) * pagination.limit) + 1}-${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} orders`
            : "No orders found"}
        </div>
        <div className="flex items-center gap-4">
          {pagination && pagination.total > 0 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreviousPage}
                disabled={!pagination.hasPrev}
                className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              >
                &lt;
              </Button>
              <span className="text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextPage}
                disabled={!pagination.hasNext}
                className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              >
                &gt;
              </Button>
            </div>
          )}
          <Button
            variant="outline"
            size="sm"
            disabled={!pagination || pagination.total === 0 || (searchTerm === "" && statusFilter === "" && sortOption === "")}
            onClick={() => {
              // Reset filters
              setSearchTerm("");
              setStatusFilter("");
              setSortOption("");
              setCurrentPage(1);
            }}
            className="h-8 gap-1.5 hover:bg-primary/5 transition-colors"
          >
            <RefreshCwIcon className="h-3.5 w-3.5" />
            Reset Filters
          </Button>
        </div>
      </div>
    </div>
  );
}
