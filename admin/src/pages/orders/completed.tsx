import * as React from "react";
import { Link } from "react-router-dom";
import {
  SearchIcon,
  ChevronLeftIcon,
  CheckCircleIcon,
  DownloadIcon,
  PackageIcon,
  ReceiptIcon,
  RefreshCwIcon,
  UserIcon,
  PhoneIcon,
  MailIcon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAppSelector, useAppDispatch } from "@/hooks/use-redux";
import { fetchOrders } from "@/store/slices/ordersSlice";
import { toast } from "@/utils/toast";

export function OrdersCompleted() {
  const dispatch = useAppDispatch();
  const { items: orders, loading, error } = useAppSelector((state) => state.orders);
  const [searchTerm, setSearchTerm] = React.useState("");
  const [generatingInvoice, setGeneratingInvoice] = React.useState<string | null>(null);

  // Fetch completed orders on component mount
  React.useEffect(() => {
    dispatch(fetchOrders({ status: 'delivered', page: 1, limit: 50 }));
  }, [dispatch]);

  const completedOrders = React.useMemo(() => {
    if (!orders || !Array.isArray(orders)) return [];

    let result = orders.filter(order => order.status === "delivered");

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter(order =>
        order.orderNumber?.toLowerCase().includes(searchLower) ||
        order.userId?.profile?.firstName?.toLowerCase().includes(searchLower) ||
        order.userId?.profile?.lastName?.toLowerCase().includes(searchLower) ||
        order.shippingAddress?.fullName?.toLowerCase().includes(searchLower) ||
        order.userId?.email?.toLowerCase().includes(searchLower) ||
        order.shippingAddress?.phone?.toLowerCase().includes(searchLower)
      );
    }

    // Sort by date (newest first)
    return result.sort((a, b) => new Date(b.placedAt).getTime() - new Date(a.placedAt).getTime());
  }, [orders, searchTerm]);

  const generateInvoice = (orderId: string) => {
    setGeneratingInvoice(orderId);

    // Get the order
    const order = orders?.find(o => o._id === orderId);
    if (!order) {
      setGeneratingInvoice(null);
      toast.error("Order not found");
      return;
    }

    // Create invoice content with real order data
    const customerName = order.userId?.profile?.firstName && order.userId?.profile?.lastName
      ? `${order.userId.profile.firstName} ${order.userId.profile.lastName}`
      : order.shippingAddress?.fullName || 'Guest Customer';

    const invoiceContent = `
INVOICE - SAJAWAT SAREES
==============================
Order Number: ${order.orderNumber}
Order ID: ${order._id}
Date: ${new Date(order.placedAt).toLocaleDateString()}
Customer: ${customerName}
Email: ${order.userId?.email || 'N/A'}
Phone: ${order.userId?.phone || order.shippingAddress?.phone || 'N/A'}
Status: ${order.status.toUpperCase()}

SHIPPING ADDRESS:
${order.shippingAddress?.fullName || ''}
${order.shippingAddress?.addressLine1 || ''}
${order.shippingAddress?.addressLine2 ? order.shippingAddress.addressLine2 + '\n' : ''}${order.shippingAddress?.city || ''}, ${order.shippingAddress?.state || ''} ${order.shippingAddress?.pincode || order.shippingAddress?.postalCode || ''}
${order.shippingAddress?.country || 'India'}

------------------------------
ITEMS ORDERED:
${order.items?.map(item =>
  `${item.productDetails?.name || 'Product'} ${item.variant?.color ? `(${item.variant.color}` : ''}${item.variant?.size ? `, ${item.variant.size})` : item.variant?.color ? ')' : ''}
  Qty: ${item.quantity} x ₹${(item.unitPrice || item.price || 0).toLocaleString()} = ₹${(item.totalPrice || (item.unitPrice || item.price || 0) * item.quantity).toLocaleString()}`
).join('\n') || 'No items found'}
------------------------------
Subtotal: ₹${order.totals?.subtotal?.toLocaleString() || '0'}
${order.totals?.discount ? `Discount: -₹${order.totals.discount.toLocaleString()}` : ''}
${order.totals?.tax ? `Tax: ₹${order.totals.tax.toLocaleString()}` : ''}
${order.totals?.shipping ? `Shipping: ₹${order.totals.shipping.toLocaleString()}` : ''}
TOTAL: ₹${order.totals?.total?.toLocaleString() || '0'}

Payment Method: ${order.paymentMethod || 'N/A'}
Payment Status: ${order.paymentStatus || 'N/A'}
==============================
Thank you for shopping with Sajawat Sarees!
Visit us at: www.sajawatsarees.com
    `;

    // Create a blob and download
    const blob = new Blob([invoiceContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `invoice_${order.orderNumber || order._id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Invoice downloaded successfully");

    setTimeout(() => {
      setGeneratingInvoice(null);
    }, 1000);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/orders">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <CheckCircleIcon className="h-6 w-6 text-green-500" />
              Completed Orders
            </h1>
          </div>
          <p className="text-muted-foreground">
            View orders that have been completed and delivered.
          </p>
        </div>
      </div>

      <div className="bg-card p-4 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search completed orders..."
            className="w-full pl-10 transition-all duration-200 focus:border-primary/30"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="bg-card rounded-lg shadow-sm border border-border/40 overflow-hidden transition-all duration-200 hover:shadow-md animate-slide-up">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Order ID</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Customer</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Products</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Date</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Amount</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border/60">
              {loading ? (
                <tr>
                  <td colSpan={6} className="p-8 text-center">
                    <div className="flex flex-col items-center justify-center space-y-3 animate-pulse-slow">
                      <RefreshCwIcon className="h-8 w-8 text-primary/70 animate-spin" />
                      <p className="text-muted-foreground">Loading completed orders...</p>
                    </div>
                  </td>
                </tr>
              ) : completedOrders.length === 0 ? (
                <tr>
                  <td colSpan={6} className="p-8 text-center">
                    <div className="flex flex-col items-center justify-center space-y-3 animate-fade-in">
                      <div className="bg-muted/30 p-4 rounded-full">
                        <PackageIcon className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <p className="text-muted-foreground">No completed orders found</p>
                      {searchTerm && (
                        <p className="text-xs text-muted-foreground">Try adjusting your search</p>
                      )}
                    </div>
                  </td>
                </tr>
              ) : (
                completedOrders.map((order, index) => (
                  <tr
                    key={order._id}
                    className="bg-card hover:bg-muted/30 transition-colors"
                    style={{
                      animationDelay: `${index * 50}ms`,
                      animation: 'fade-in 0.5s ease-out forwards',
                      opacity: 0
                    }}
                  >
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="font-medium text-foreground flex items-center gap-1.5">
                        <span className="bg-green-100 dark:bg-green-900/30 p-1 rounded text-green-600 dark:text-green-400">
                          <CheckCircleIcon className="h-3.5 w-3.5" />
                        </span>
                        #{order.orderNumber}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-3">
                        <div className="bg-primary/10 p-2 rounded-full">
                          <UserIcon className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {order.userId?.profile?.firstName && order.userId?.profile?.lastName
                              ? `${order.userId.profile.firstName} ${order.userId.profile.lastName}`
                              : order.shippingAddress?.fullName || 'Guest Customer'
                            }
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            {order.userId?.email && (
                              <>
                                <MailIcon className="h-3 w-3" />
                                {order.userId.email}
                              </>
                            )}
                            {(order.userId?.phone || order.shippingAddress?.phone) && (
                              <>
                                <PhoneIcon className="h-3 w-3 ml-2" />
                                {order.userId?.phone || order.shippingAddress?.phone}
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        {order.items?.slice(0, 3).map((item, itemIndex) => (
                          <div key={itemIndex} className="relative">
                            <div className="w-10 h-10 rounded-md overflow-hidden border border-border/60 shadow-sm bg-muted/30">
                              <img
                                src={item.productDetails?.image || `http://localhost:3000/assets/imagesVidoes/images/products/${item.productDetails?.sku || 'placeholder'}.jpg`}
                                alt={item.productDetails?.name || 'Product'}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = '/placeholder-product.jpg';
                                }}
                              />
                            </div>
                            {item.quantity > 1 && (
                              <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                                {item.quantity}
                              </span>
                            )}
                          </div>
                        ))}
                        {order.items && order.items.length > 3 && (
                          <div className="w-10 h-10 rounded-md border border-border/60 bg-muted/50 flex items-center justify-center">
                            <span className="text-xs font-medium text-muted-foreground">
                              +{order.items.length - 3}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {order.items?.length || 0} item{(order.items?.length || 0) !== 1 ? 's' : ''}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      {new Date(order.placedAt).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="font-medium">₹{order.totals?.total?.toLocaleString() || 0}</div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" asChild className="h-8 px-3 text-xs hover:bg-primary/5 transition-colors">
                          <Link to={`/orders/${order._id}`}>View Details</Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 px-3 text-xs hover:bg-green-50 hover:text-green-600 hover:border-green-200 dark:hover:bg-green-900/20 dark:hover:text-green-400 dark:hover:border-green-800 transition-colors"
                          onClick={() => generateInvoice(order._id)}
                          disabled={generatingInvoice === order._id}
                        >
                          {generatingInvoice === order._id ? (
                            <RefreshCwIcon className="mr-1.5 h-3.5 w-3.5 text-green-500 animate-spin" />
                          ) : (
                            <ReceiptIcon className="mr-1.5 h-3.5 w-3.5 text-green-500" />
                          )}
                          {generatingInvoice === order._id ? 'Generating...' : 'Invoice'}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
