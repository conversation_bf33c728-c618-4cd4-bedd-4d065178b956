import * as React from "react";
import { Link, useLocation } from "react-router-dom";
import {
  UserIcon,
  StoreIcon,
  CreditCardIcon,
  TruckIcon,
  PercentIcon,
  BellIcon,
  UsersIcon,
  SettingsIcon,
  ChevronRightIcon,
  ShieldIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";

interface SettingsNavItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
  active?: boolean;
}

function SettingsNavItem({
  icon,
  title,
  description,
  href,
  active,
}: SettingsNavItemProps) {
  return (
    <Link
      to={href}
      className={cn(
        "flex items-start space-x-4 rounded-lg border border-border/40 p-5 transition-all duration-200 hover:bg-muted/30 hover:shadow-md group relative overflow-hidden",
        active ? "bg-muted/50 shadow-sm border-primary/20" : "bg-card shadow-sm"
      )}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div className="bg-primary/10 p-2.5 rounded-full z-10">
        {icon}
      </div>
      <div className="space-y-1.5 z-10 flex-1">
        <h3 className="font-medium text-lg flex items-center justify-between">
          {title}
          <ChevronRightIcon className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-200 transform translate-x-0 group-hover:translate-x-1 transition-transform" />
        </h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </Link>
  );
}

export function Settings() {
  const location = useLocation();
  const currentPath = location.pathname;

  const settingsNavItems = [
    {
      icon: <UserIcon className="h-5 w-5 text-primary/80" />,
      title: "Profile",
      description: "Manage your account settings and preferences",
      href: "/settings/profile",
    },
    {
      icon: <StoreIcon className="h-5 w-5 text-primary/80" />,
      title: "Store",
      description: "Manage your store information and settings",
      href: "/settings/store",
    },
    {
      icon: <CreditCardIcon className="h-5 w-5 text-primary/80" />,
      title: "Payment",
      description: "Manage payment methods and transaction settings",
      href: "/settings/payment",
    },
    {
      icon: <TruckIcon className="h-5 w-5 text-primary/80" />,
      title: "Shipping",
      description: "Configure shipping zones, methods, and rates",
      href: "/settings/shipping",
    },
    {
      icon: <PercentIcon className="h-5 w-5 text-primary/80" />,
      title: "Tax",
      description: "Configure tax rates and settings",
      href: "/settings/tax",
    },
    {
      icon: <BellIcon className="h-5 w-5 text-primary/80" />,
      title: "Notifications",
      description: "Configure email and system notifications",
      href: "/settings/notifications",
    },
    {
      icon: <UsersIcon className="h-5 w-5 text-primary/80" />,
      title: "Users",
      description: "Manage staff accounts and permissions",
      href: "/settings/users",
    },
    {
      icon: <ShieldIcon className="h-5 w-5 text-primary/80" />,
      title: "Security",
      description: "Manage security settings and access controls",
      href: "/settings/security",
    },
  ];

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center gap-3 animate-slide-up">
        <div className="bg-primary/10 p-2.5 rounded-full">
          <SettingsIcon className="h-6 w-6 text-primary/80" />
        </div>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>
      </div>

      <Separator className="animate-fade-in" />

      <div className="grid gap-5 md:grid-cols-2 lg:grid-cols-3">
        {settingsNavItems.map((item, index) => (
          <div
            key={item.href}
            className="animate-slide-up"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <SettingsNavItem
              icon={item.icon}
              title={item.title}
              description={item.description}
              href={item.href}
              active={currentPath === item.href}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
