import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  ChevronLeftIcon,
  SaveIcon,
  ShieldIcon,
  KeyIcon,
  LockIcon,
  EyeIcon,
  EyeOffIcon,
  UserIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  RefreshCwIcon,
  GlobeIcon,
  LogInIcon,
  ClockIcon,
  HistoryIcon,
  XIcon
} from "lucide-react";

export function SecuritySettings() {
  const [passwordSettings, setPasswordSettings] = React.useState({
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    minLength: 8,
    passwordExpiry: 90,
  });

  const [securitySettings, setSecuritySettings] = React.useState({
    twoFactorAuth: false,
    loginAttempts: 5,
    lockoutDuration: 30,
    sessionTimeout: 60,
    ipRestriction: false,
    allowedIPs: "",
  });

  const [showPassword, setShowPassword] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const [loginHistory, setLoginHistory] = React.useState([
    {
      id: 1,
      user: "Admin User",
      ip: "***********",
      device: "Chrome / Windows",
      timestamp: "2023-06-15 10:30:45",
      status: "Success",
    },
    {
      id: 2,
      user: "Admin User",
      ip: "***********",
      device: "Chrome / Windows",
      timestamp: "2023-06-14 15:22:18",
      status: "Success",
    },
    {
      id: 3,
      user: "Inventory Manager",
      ip: "***********",
      device: "Safari / macOS",
      timestamp: "2023-06-14 12:15:33",
      status: "Success",
    },
    {
      id: 4,
      user: "Unknown",
      ip: "************",
      device: "Firefox / Linux",
      timestamp: "2023-06-13 08:45:12",
      status: "Failed",
    },
  ]);

  const handlePasswordSettingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setPasswordSettings((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : type === "number" ? parseInt(value) : value,
    }));
  };

  const handleSecuritySettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    setSecuritySettings((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      console.log("Security settings saved:", { passwordSettings, securitySettings });
      alert("Security settings updated successfully!");
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/settings">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <div className="flex items-center gap-2">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <ShieldIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h1 className="text-2xl font-bold tracking-tight">Security Settings</h1>
            </div>
          </div>
          <p className="text-muted-foreground ml-10">
            Manage security settings and access controls.
          </p>
        </div>
      </div>

      <Separator className="animate-fade-in" />

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4 animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <LockIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Password Policy</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <KeyIcon className="h-4 w-4 text-primary/70" />
                    Require Uppercase Letters
                  </h3>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer" 
                    name="requireUppercase"
                    checked={passwordSettings.requireUppercase}
                    onChange={handlePasswordSettingsChange}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <KeyIcon className="h-4 w-4 text-primary/70" />
                    Require Lowercase Letters
                  </h3>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer" 
                    name="requireLowercase"
                    checked={passwordSettings.requireLowercase}
                    onChange={handlePasswordSettingsChange}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <KeyIcon className="h-4 w-4 text-primary/70" />
                    Require Numbers
                  </h3>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer" 
                    name="requireNumbers"
                    checked={passwordSettings.requireNumbers}
                    onChange={handlePasswordSettingsChange}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <KeyIcon className="h-4 w-4 text-primary/70" />
                    Require Special Characters
                  </h3>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer" 
                    name="requireSpecialChars"
                    checked={passwordSettings.requireSpecialChars}
                    onChange={handlePasswordSettingsChange}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div>
                <label htmlFor="minLength" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <KeyIcon className="h-3.5 w-3.5 text-primary/70" />
                  Minimum Password Length
                </label>
                <Input
                  id="minLength"
                  name="minLength"
                  type="number"
                  min={6}
                  max={32}
                  value={passwordSettings.minLength}
                  onChange={handlePasswordSettingsChange}
                  className="w-full transition-all duration-200 focus:border-primary/30"
                />
              </div>

              <div>
                <label htmlFor="passwordExpiry" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <ClockIcon className="h-3.5 w-3.5 text-primary/70" />
                  Password Expiry (days)
                </label>
                <Input
                  id="passwordExpiry"
                  name="passwordExpiry"
                  type="number"
                  min={0}
                  max={365}
                  value={passwordSettings.passwordExpiry}
                  onChange={handlePasswordSettingsChange}
                  className="w-full transition-all duration-200 focus:border-primary/30"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Set to 0 for no expiry
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4 animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <ShieldIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Access Controls</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <UserIcon className="h-4 w-4 text-primary/70" />
                    Two-Factor Authentication
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Require 2FA for all admin users
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer" 
                    name="twoFactorAuth"
                    checked={securitySettings.twoFactorAuth}
                    onChange={handleSecuritySettingsChange}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div>
                <label htmlFor="loginAttempts" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <LogInIcon className="h-3.5 w-3.5 text-primary/70" />
                  Maximum Login Attempts
                </label>
                <Input
                  id="loginAttempts"
                  name="loginAttempts"
                  type="number"
                  min={1}
                  max={10}
                  value={securitySettings.loginAttempts}
                  onChange={handleSecuritySettingsChange}
                  className="w-full transition-all duration-200 focus:border-primary/30"
                />
              </div>

              <div>
                <label htmlFor="lockoutDuration" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <ClockIcon className="h-3.5 w-3.5 text-primary/70" />
                  Account Lockout Duration (minutes)
                </label>
                <Input
                  id="lockoutDuration"
                  name="lockoutDuration"
                  type="number"
                  min={5}
                  max={1440}
                  value={securitySettings.lockoutDuration}
                  onChange={handleSecuritySettingsChange}
                  className="w-full transition-all duration-200 focus:border-primary/30"
                />
              </div>

              <div>
                <label htmlFor="sessionTimeout" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <ClockIcon className="h-3.5 w-3.5 text-primary/70" />
                  Session Timeout (minutes)
                </label>
                <Input
                  id="sessionTimeout"
                  name="sessionTimeout"
                  type="number"
                  min={5}
                  max={1440}
                  value={securitySettings.sessionTimeout}
                  onChange={handleSecuritySettingsChange}
                  className="w-full transition-all duration-200 focus:border-primary/30"
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <GlobeIcon className="h-4 w-4 text-primary/70" />
                    IP Restriction
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Restrict admin access to specific IP addresses
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer" 
                    name="ipRestriction"
                    checked={securitySettings.ipRestriction}
                    onChange={handleSecuritySettingsChange}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              {securitySettings.ipRestriction && (
                <div>
                  <label htmlFor="allowedIPs" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                    <GlobeIcon className="h-3.5 w-3.5 text-primary/70" />
                    Allowed IP Addresses
                  </label>
                  <textarea
                    id="allowedIPs"
                    name="allowedIPs"
                    rows={3}
                    value={securitySettings.allowedIPs}
                    onChange={handleSecuritySettingsChange}
                    placeholder="Enter IP addresses, one per line"
                    className="w-full p-3 border border-input rounded-md shadow-xs transition-all duration-200 focus:border-primary/30 focus:outline-none focus:ring-2 focus:ring-ring/50"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Enter one IP address per line (e.g., ***********)
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="animate-slide-up" style={{ animationDelay: '150ms' }}>
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <HistoryIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Login History</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted/50">
                  <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">User</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">IP Address</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Device</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Time</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border/60">
                {loginHistory.map((entry) => (
                  <tr key={entry.id} className="hover:bg-muted/30 transition-colors">
                    <td className="px-4 py-3 whitespace-nowrap font-medium">{entry.user}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{entry.ip}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{entry.device}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{entry.timestamp}</td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      {entry.status === "Success" ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                          <CheckCircleIcon className="h-3 w-3 mr-1" />
                          Success
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                          <XIcon className="h-3 w-3 mr-1" />
                          Failed
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div className="flex justify-end animate-slide-up" style={{ animationDelay: '200ms' }}>
        <Button 
          type="button" 
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="transition-all duration-200 hover:bg-primary/90"
        >
          {isSubmitting ? (
            <>
              <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <SaveIcon className="h-4 w-4 mr-2" />
              Save Security Settings
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
