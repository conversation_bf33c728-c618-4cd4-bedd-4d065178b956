import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeftIcon, SaveIcon, TruckIcon, PlusIcon, TrashIcon } from "lucide-react";

export function ShippingSettings() {
  const [shippingZones, setShippingZones] = React.useState([
    {
      id: 1,
      name: "India",
      regions: ["All States"],
      methods: [
        {
          id: 1,
          name: "Standard Shipping",
          cost: 100,
          freeAbove: 1999,
          estimatedDays: "3-5",
        },
        {
          id: 2,
          name: "Express Shipping",
          cost: 250,
          freeAbove: 4999,
          estimatedDays: "1-2",
        },
      ],
    },
    {
      id: 2,
      name: "International",
      regions: ["All Countries (except India)"],
      methods: [
        {
          id: 3,
          name: "International Standard",
          cost: 1500,
          freeAbove: 15000,
          estimatedDays: "7-14",
        },
      ],
    },
  ]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the shipping settings here
    console.log("Shipping settings saved:", shippingZones);
    alert("Shipping settings updated successfully!");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              asChild
            >
              <Link to="/settings">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">Shipping Settings</h1>
          </div>
          <p className="text-muted-foreground">
            Configure shipping zones, methods, and rates.
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full">
                <TruckIcon className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Shipping Zones</h2>
                <p className="text-muted-foreground">Configure shipping by region</p>
              </div>
            </div>

            <div className="space-y-4">
              {shippingZones.map((zone) => (
                <div key={zone.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h3 className="font-medium">{zone.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {zone.regions.join(", ")}
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <TrashIcon className="h-4 w-4 text-destructive" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>

                  <div className="mt-4 space-y-3 pt-3 border-t">
                    <h4 className="font-medium text-sm">Shipping Methods</h4>
                    {zone.methods.map((method) => (
                      <div key={method.id} className="bg-muted/50 p-3 rounded-md">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium">{method.name}</h5>
                            <p className="text-xs text-muted-foreground">
                              ₹{method.cost} (Free above ₹{method.freeAbove}) • {method.estimatedDays} days
                            </p>
                          </div>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <TrashIcon className="h-4 w-4 text-destructive" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Button variant="outline" size="sm" className="w-full">
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add Shipping Method
                    </Button>
                  </div>
                </div>
              ))}

              <Button variant="outline" className="w-full">
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Shipping Zone
              </Button>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Add Shipping Method</h2>
            <form className="space-y-4">
              <div>
                <label htmlFor="zone" className="block text-sm font-medium mb-1">
                  Shipping Zone
                </label>
                <select
                  id="zone"
                  className="w-full p-2 border rounded-md"
                  defaultValue=""
                >
                  <option value="" disabled>Select a zone</option>
                  {shippingZones.map((zone) => (
                    <option key={zone.id} value={zone.id}>
                      {zone.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="methodName" className="block text-sm font-medium mb-1">
                  Method Name
                </label>
                <input
                  id="methodName"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g. Standard Shipping"
                />
              </div>

              <div>
                <label htmlFor="cost" className="block text-sm font-medium mb-1">
                  Shipping Cost
                </label>
                <div className="flex">
                  <span className="inline-flex items-center px-3 border border-r-0 rounded-l-md bg-muted">
                    ₹
                  </span>
                  <input
                    id="cost"
                    type="number"
                    className="w-full p-2 border rounded-r-md"
                    placeholder="0"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="freeAbove" className="block text-sm font-medium mb-1">
                  Free Shipping Above
                </label>
                <div className="flex">
                  <span className="inline-flex items-center px-3 border border-r-0 rounded-l-md bg-muted">
                    ₹
                  </span>
                  <input
                    id="freeAbove"
                    type="number"
                    className="w-full p-2 border rounded-r-md"
                    placeholder="0"
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Set to 0 for no free shipping
                </p>
              </div>

              <div>
                <label htmlFor="estimatedDays" className="block text-sm font-medium mb-1">
                  Estimated Delivery Time
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <input
                      id="minDays"
                      type="number"
                      className="w-full p-2 border rounded-md"
                      placeholder="Min days"
                    />
                  </div>
                  <div>
                    <input
                      id="maxDays"
                      type="number"
                      className="w-full p-2 border rounded-md"
                      placeholder="Max days"
                    />
                  </div>
                </div>
              </div>

              <Button type="submit" className="w-full">
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Method
              </Button>
            </form>
          </div>

          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Shipping Options</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Calculate Shipping by Weight</h3>
                  <p className="text-sm text-muted-foreground">
                    Use product weight to calculate shipping costs
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Show Shipping Calculator</h3>
                  <p className="text-sm text-muted-foreground">
                    Allow customers to calculate shipping before checkout
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <Button type="submit" className="w-full" onClick={handleSubmit}>
              <SaveIcon className="h-4 w-4 mr-2" />
              Save Shipping Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
