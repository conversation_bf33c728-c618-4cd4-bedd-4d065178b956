import * as React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeftIcon, SaveIcon, BellIcon, MailIcon, MessageSquareIcon } from "lucide-react";

export function NotificationsSettings() {
  const [emailSettings, setEmailSettings] = React.useState({
    smtpHost: "smtp.gmail.com",
    smtpPort: "587",
    smtpUsername: "<EMAIL>",
    smtpPassword: "••••••••••••••••",
    fromEmail: "<EMAIL>",
    fromName: "Sajawat Sarees",
  });

  const [isTestingEmail, setIsTestingEmail] = React.useState(false);
  const [isTestingSMS, setIsTestingSMS] = React.useState(false);
  const [editingTemplate, setEditingTemplate] = React.useState<number | null>(null);
  const [templateContent, setTemplateContent] = React.useState("");

  const [notificationTemplates, setNotificationTemplates] = React.useState([
    {
      id: 1,
      type: "New Order",
      emailEnabled: true,
      smsEnabled: true,
      adminEnabled: true,
      emailSubject: "Thank you for your order #{{order_number}}",
    },
    {
      id: 2,
      type: "Order Processing",
      emailEnabled: true,
      smsEnabled: true,
      adminEnabled: true,
      emailSubject: "Your order #{{order_number}} is being processed",
    },
    {
      id: 3,
      type: "Order Shipped",
      emailEnabled: true,
      smsEnabled: true,
      adminEnabled: true,
      emailSubject: "Your order #{{order_number}} has been shipped",
    },
    {
      id: 4,
      type: "Order Delivered",
      emailEnabled: true,
      smsEnabled: false,
      adminEnabled: true,
      emailSubject: "Your order #{{order_number}} has been delivered",
    },
    {
      id: 5,
      type: "Order Cancelled",
      emailEnabled: true,
      smsEnabled: true,
      adminEnabled: true,
      emailSubject: "Your order #{{order_number}} has been cancelled",
    },
    {
      id: 6,
      type: "Low Stock Alert",
      emailEnabled: true,
      smsEnabled: false,
      adminEnabled: true,
      emailSubject: "Low stock alert for {{product_name}}",
    },
  ]);

  const handleToggleNotification = (id: number, field: "emailEnabled" | "smsEnabled" | "adminEnabled") => {
    setNotificationTemplates(
      notificationTemplates.map((template) =>
        template.id === id ? { ...template, [field]: !template[field] } : template
      )
    );
  };

  const handleEmailSettingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEmailSettings((prev) => ({ ...prev, [name]: value }));
  };

  const handleTestEmailConnection = () => {
    setIsTestingEmail(true);

    // Simulate API call to test email connection
    setTimeout(() => {
      // Check if required fields are filled
      if (!emailSettings.smtpHost || !emailSettings.smtpPort || !emailSettings.smtpUsername ||
          !emailSettings.smtpPassword || !emailSettings.fromEmail || !emailSettings.fromName) {
        alert("Please fill in all email settings fields before testing the connection.");
        setIsTestingEmail(false);
        return;
      }

      // In a real app, you would make an API call to test the email connection
      const success = Math.random() > 0.2; // 80% success rate for demo

      if (success) {
        alert("Email connection test successful! A test email has been sent to your inbox.");
      } else {
        alert("Email connection test failed. Please check your settings and try again.");
      }

      setIsTestingEmail(false);
    }, 2000);
  };

  const handleTestSMSConnection = () => {
    setIsTestingSMS(true);

    // Simulate API call to test SMS connection
    setTimeout(() => {
      // In a real app, you would make an API call to test the SMS connection
      const success = Math.random() > 0.2; // 80% success rate for demo

      if (success) {
        alert("SMS connection test successful! A test SMS has been sent to your phone.");
      } else {
        alert("SMS connection test failed. Please check your settings and try again.");
      }

      setIsTestingSMS(false);
    }, 2000);
  };

  const handleEditTemplate = (templateId: number) => {
    const template = notificationTemplates.find(t => t.id === templateId);
    if (!template) return;

    // Set the template content based on the template type
    // In a real app, you would fetch the actual template content from the server
    let content = "";

    switch (template.type) {
      case "Order Confirmation":
        content = `Dear {{customer_name}},

Thank you for your order #{{order_id}}. We're processing it now and will send you an update when it ships.

Order Details:
{{order_items}}

Total: {{order_total}}

If you have any questions, please contact us.

Best regards,
Sajawat Sarees Team`;
        break;

      case "Order Shipped":
        content = `Dear {{customer_name}},

Great news! Your order #{{order_id}} has been shipped.

Tracking Number: {{tracking_number}}
Estimated Delivery: {{delivery_date}}

You can track your package here: {{tracking_link}}

Thank you for shopping with us!

Best regards,
Sajawat Sarees Team`;
        break;

      case "Order Delivered":
        content = `Dear {{customer_name}},

Your order #{{order_id}} has been delivered.

We hope you love your new sarees! If you have a moment, we'd appreciate if you could leave a review.

Thank you for choosing Sajawat Sarees.

Best regards,
Sajawat Sarees Team`;
        break;

      case "Password Reset":
        content = `Dear {{customer_name}},

We received a request to reset your password. Click the link below to set a new password:

{{reset_link}}

If you didn't request this, please ignore this email.

Best regards,
Sajawat Sarees Team`;
        break;

      default:
        content = "Template content not available.";
    }

    setTemplateContent(content);
    setEditingTemplate(templateId);
  };

  const handleSaveTemplate = () => {
    // In a real app, you would save the template content to the server
    alert("Template saved successfully!");
    setEditingTemplate(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the notification settings here
    console.log("Notification settings saved:", { emailSettings, notificationTemplates });
    alert("Notification settings updated successfully!");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              asChild
            >
              <Link to="/settings">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">Notification Settings</h1>
          </div>
          <p className="text-muted-foreground">
            Configure email and system notifications.
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full">
                <MailIcon className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Email Settings</h2>
                <p className="text-muted-foreground">Configure email server settings</p>
              </div>
            </div>

            <form className="space-y-4">
              <div>
                <label htmlFor="smtpHost" className="block text-sm font-medium mb-1">
                  SMTP Host
                </label>
                <input
                  id="smtpHost"
                  name="smtpHost"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  value={emailSettings.smtpHost}
                  onChange={handleEmailSettingsChange}
                />
              </div>

              <div>
                <label htmlFor="smtpPort" className="block text-sm font-medium mb-1">
                  SMTP Port
                </label>
                <input
                  id="smtpPort"
                  name="smtpPort"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  value={emailSettings.smtpPort}
                  onChange={handleEmailSettingsChange}
                />
              </div>

              <div>
                <label htmlFor="smtpUsername" className="block text-sm font-medium mb-1">
                  SMTP Username
                </label>
                <input
                  id="smtpUsername"
                  name="smtpUsername"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  value={emailSettings.smtpUsername}
                  onChange={handleEmailSettingsChange}
                />
              </div>

              <div>
                <label htmlFor="smtpPassword" className="block text-sm font-medium mb-1">
                  SMTP Password
                </label>
                <input
                  id="smtpPassword"
                  name="smtpPassword"
                  type="password"
                  className="w-full p-2 border rounded-md"
                  value={emailSettings.smtpPassword}
                  onChange={handleEmailSettingsChange}
                />
              </div>

              <div>
                <label htmlFor="fromEmail" className="block text-sm font-medium mb-1">
                  From Email
                </label>
                <input
                  id="fromEmail"
                  name="fromEmail"
                  type="email"
                  className="w-full p-2 border rounded-md"
                  value={emailSettings.fromEmail}
                  onChange={handleEmailSettingsChange}
                />
              </div>

              <div>
                <label htmlFor="fromName" className="block text-sm font-medium mb-1">
                  From Name
                </label>
                <input
                  id="fromName"
                  name="fromName"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  value={emailSettings.fromName}
                  onChange={handleEmailSettingsChange}
                />
              </div>

              <Button
                variant="outline"
                className="w-full"
                onClick={handleTestEmailConnection}
                disabled={isTestingEmail}
              >
                {isTestingEmail ? 'Testing...' : 'Test Email Connection'}
              </Button>
            </form>
          </div>

          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full">
                <MessageSquareIcon className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">SMS Settings</h2>
                <p className="text-muted-foreground">Configure SMS gateway settings</p>
              </div>
            </div>

            <form className="space-y-4">
              <div>
                <label htmlFor="smsProvider" className="block text-sm font-medium mb-1">
                  SMS Provider
                </label>
                <select
                  id="smsProvider"
                  className="w-full p-2 border rounded-md"
                  defaultValue="twilio"
                >
                  <option value="twilio">Twilio</option>
                  <option value="msg91">MSG91</option>
                  <option value="textlocal">Textlocal</option>
                </select>
              </div>

              <div>
                <label htmlFor="smsApiKey" className="block text-sm font-medium mb-1">
                  API Key
                </label>
                <input
                  id="smsApiKey"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  placeholder="Enter API key"
                />
              </div>

              <div>
                <label htmlFor="smsSenderId" className="block text-sm font-medium mb-1">
                  Sender ID
                </label>
                <input
                  id="smsSenderId"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g. SJWSAT"
                />
              </div>

              <Button
                variant="outline"
                className="w-full"
                onClick={handleTestSMSConnection}
                disabled={isTestingSMS}
              >
                {isTestingSMS ? 'Testing...' : 'Test SMS Connection'}
              </Button>
            </form>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full">
                <BellIcon className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Notification Templates</h2>
                <p className="text-muted-foreground">Configure notification settings for events</p>
              </div>
            </div>

            <div className="space-y-4">
              {notificationTemplates.map((template) => (
                <div key={template.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">{template.type}</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditTemplate(template.id)}
                    >
                      Edit Template
                    </Button>
                  </div>
                  <div className="grid grid-cols-3 gap-2 mt-3">
                    <div className="flex items-center gap-2">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={template.emailEnabled}
                          onChange={() => handleToggleNotification(template.id, "emailEnabled")}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                      <span className="text-sm">Email</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={template.smsEnabled}
                          onChange={() => handleToggleNotification(template.id, "smsEnabled")}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                      <span className="text-sm">SMS</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={template.adminEnabled}
                          onChange={() => handleToggleNotification(template.id, "adminEnabled")}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                      <span className="text-sm">Admin</span>
                    </div>
                  </div>
                  <div className="mt-2 text-xs text-muted-foreground">
                    <p>Subject: {template.emailSubject}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Admin Notification Preferences</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">New Order Notifications</h3>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications for new orders
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Low Stock Alerts</h3>
                  <p className="text-sm text-muted-foreground">
                    Receive alerts when product stock is low
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Customer Reviews</h3>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications for new customer reviews
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <Button type="submit" className="w-full" onClick={handleSubmit}>
              <SaveIcon className="h-4 w-4 mr-2" />
              Save Notification Settings
            </Button>
          </div>
        </div>
      </div>

      {/* Template Editor Modal */}
      {editingTemplate !== null && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto">
            <h2 className="text-xl font-semibold mb-4">
              Edit Template: {notificationTemplates.find(t => t.id === editingTemplate)?.type}
            </h2>

            <div className="mb-4">
              <p className="text-sm text-muted-foreground mb-2">
                Use variables like {`{{customer_name}}`} to personalize your template.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">{`{{customer_name}}`}</span>
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">{`{{order_id}}`}</span>
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">{`{{order_items}}`}</span>
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">{`{{order_total}}`}</span>
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">{`{{tracking_number}}`}</span>
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">{`{{delivery_date}}`}</span>
              </div>

              <textarea
                className="w-full h-64 p-3 border rounded-md font-mono text-sm"
                value={templateContent}
                onChange={(e) => setTemplateContent(e.target.value)}
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setEditingTemplate(null)}
              >
                Cancel
              </Button>
              <Button onClick={handleSaveTemplate}>
                Save Template
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
