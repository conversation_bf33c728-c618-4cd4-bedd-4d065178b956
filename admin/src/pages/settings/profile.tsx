import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  ChevronLeftIcon,
  SaveIcon,
  UserIcon,
  MailIcon,
  PhoneIcon,
  KeyIcon,
  BellIcon,
  ShieldIcon,
  LockIcon,
  CheckIcon,
  AlertCircleIcon
} from "lucide-react";

export function ProfileSettings() {
  const [formData, setFormData] = React.useState({
    name: "Admin User",
    email: "<EMAIL>",
    phone: "+91 9876543210",
    role: "Administrator",
    bio: "Experienced e-commerce professional with expertise in saree retail.",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the profile data here
    console.log("Profile data saved:", formData);
    alert("Profile updated successfully!");
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/settings">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <div className="flex items-center gap-2">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <UserIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h1 className="text-2xl font-bold tracking-tight">Profile Settings</h1>
            </div>
          </div>
          <p className="text-muted-foreground ml-10">
            Manage your personal information and preferences.
          </p>
        </div>
      </div>

      <Separator className="animate-fade-in" />

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4 animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full shadow-sm">
                <UserIcon className="h-8 w-8 text-primary/80" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">{formData.name}</h2>
                <p className="text-muted-foreground flex items-center gap-1.5">
                  <ShieldIcon className="h-3.5 w-3.5" />
                  {formData.role}
                </p>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <UserIcon className="h-3.5 w-3.5 text-primary/70" />
                  Full Name
                </label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <MailIcon className="h-3.5 w-3.5 text-primary/70" />
                  Email Address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <PhoneIcon className="h-3.5 w-3.5 text-primary/70" />
                  Phone Number
                </label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  value={formData.phone}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="bio" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <UserIcon className="h-3.5 w-3.5 text-primary/70" />
                  Bio
                </label>
                <textarea
                  id="bio"
                  name="bio"
                  rows={4}
                  className="w-full p-3 border border-input rounded-md shadow-xs transition-all duration-200 focus:border-primary/30 focus:outline-none focus:ring-2 focus:ring-ring/50"
                  value={formData.bio}
                  onChange={handleChange}
                />
              </div>

              <div className="pt-4">
                <Button type="submit" className="w-full transition-all duration-200 hover:bg-primary/90">
                  <SaveIcon className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </form>
          </div>
        </div>

        <div className="space-y-4 animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <LockIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Security</h2>
            </div>
            <div className="space-y-4">
              <div>
                <label htmlFor="current-password" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <KeyIcon className="h-3.5 w-3.5 text-primary/70" />
                  Current Password
                </label>
                <Input
                  id="current-password"
                  type="password"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  placeholder="Enter current password"
                />
              </div>

              <div>
                <label htmlFor="new-password" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <KeyIcon className="h-3.5 w-3.5 text-primary/70" />
                  New Password
                </label>
                <Input
                  id="new-password"
                  type="password"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  placeholder="Enter new password"
                />
              </div>

              <div>
                <label htmlFor="confirm-password" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <CheckIcon className="h-3.5 w-3.5 text-primary/70" />
                  Confirm New Password
                </label>
                <Input
                  id="confirm-password"
                  type="password"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  placeholder="Confirm new password"
                />
              </div>

              <div className="pt-4">
                <Button variant="outline" className="w-full hover:bg-primary/5 transition-colors">
                  <KeyIcon className="h-4 w-4 mr-2" />
                  Change Password
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <BellIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Preferences</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <MailIcon className="h-4 w-4 text-primary/70" />
                    Email Notifications
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Receive email notifications for important updates
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary shadow-sm"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <ShieldIcon className="h-4 w-4 text-primary/70" />
                    Two-Factor Authentication
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Add an extra layer of security to your account
                  </p>
                </div>
                <Button variant="outline" size="sm" className="hover:bg-primary/5 transition-colors">
                  <ShieldIcon className="h-3.5 w-3.5 mr-1.5" />
                  Enable
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <AlertCircleIcon className="h-4 w-4 text-primary/70" />
                    Login Alerts
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Get notified about new login attempts
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary shadow-sm"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
