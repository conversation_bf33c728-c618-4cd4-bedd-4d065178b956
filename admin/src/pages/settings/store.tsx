import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  ChevronLeftIcon,
  SaveIcon,
  StoreIcon,
  MailIcon,
  PhoneIcon,
  MapPinIcon,
  FileTextIcon,
  GlobeIcon,
  ClockIcon,
  ImageIcon,
  SearchIcon,
  TagIcon,
  UploadIcon,
  DollarSignIcon
} from "lucide-react";

export function StoreSettings() {
  const [formData, setFormData] = React.useState({
    storeName: "Sajawat Sarees",
    storeEmail: "<EMAIL>",
    storePhone: "+91 9876543210",
    storeAddress: "123 Fashion Street, Textile Market, Surat, Gujarat, India",
    storeDescription: "Premium collection of authentic Indian sarees for every occasion.",
    currency: "INR",
    timezone: "Asia/Kolkata",
    logo: "/logo.png",
  });

  const [isChangingLogo, setIsChangingLogo] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleLogoClick = () => {
    // Trigger the hidden file input
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsChangingLogo(true);

    // Check file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file.');
      setIsChangingLogo(false);
      return;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      alert('Image size should be less than 2MB.');
      setIsChangingLogo(false);
      return;
    }

    // Create a URL for the file
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setFormData(prev => ({
          ...prev,
          logo: event.target.result as string
        }));
        setIsChangingLogo(false);
      }
    };
    reader.onerror = () => {
      alert('Error reading file.');
      setIsChangingLogo(false);
    };
    reader.readAsDataURL(file);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the store data here
    console.log("Store data saved:", formData);
    alert("Store settings updated successfully!");
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/settings">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <div className="flex items-center gap-2">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <StoreIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h1 className="text-2xl font-bold tracking-tight">Store Settings</h1>
            </div>
          </div>
          <p className="text-muted-foreground ml-10">
            Manage your store information and settings.
          </p>
        </div>
      </div>

      <Separator className="animate-fade-in" />

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4 animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full shadow-sm">
                <StoreIcon className="h-8 w-8 text-primary/80" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">{formData.storeName}</h2>
                <p className="text-muted-foreground flex items-center gap-1.5">
                  <GlobeIcon className="h-3.5 w-3.5" />
                  Store Information
                </p>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="storeName" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <StoreIcon className="h-3.5 w-3.5 text-primary/70" />
                  Store Name
                </label>
                <Input
                  id="storeName"
                  name="storeName"
                  type="text"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  value={formData.storeName}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <label htmlFor="storeEmail" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <MailIcon className="h-3.5 w-3.5 text-primary/70" />
                  Store Email
                </label>
                <Input
                  id="storeEmail"
                  name="storeEmail"
                  type="email"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  value={formData.storeEmail}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <label htmlFor="storePhone" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <PhoneIcon className="h-3.5 w-3.5 text-primary/70" />
                  Store Phone
                </label>
                <Input
                  id="storePhone"
                  name="storePhone"
                  type="tel"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  value={formData.storePhone}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="storeAddress" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <MapPinIcon className="h-3.5 w-3.5 text-primary/70" />
                  Store Address
                </label>
                <textarea
                  id="storeAddress"
                  name="storeAddress"
                  rows={3}
                  className="w-full p-3 border border-input rounded-md shadow-xs transition-all duration-200 focus:border-primary/30 focus:outline-none focus:ring-2 focus:ring-ring/50"
                  value={formData.storeAddress}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="storeDescription" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <FileTextIcon className="h-3.5 w-3.5 text-primary/70" />
                  Store Description
                </label>
                <textarea
                  id="storeDescription"
                  name="storeDescription"
                  rows={4}
                  className="w-full p-3 border border-input rounded-md shadow-xs transition-all duration-200 focus:border-primary/30 focus:outline-none focus:ring-2 focus:ring-ring/50"
                  value={formData.storeDescription}
                  onChange={handleChange}
                />
              </div>
            </form>
          </div>
        </div>

        <div className="space-y-4 animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <GlobeIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Store Preferences</h2>
            </div>
            <div className="space-y-4">
              <div>
                <label htmlFor="currency" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <DollarSignIcon className="h-3.5 w-3.5 text-primary/70" />
                  Currency
                </label>
                <select
                  id="currency"
                  name="currency"
                  className="w-full h-10 px-3 py-2 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
                  value={formData.currency}
                  onChange={handleChange}
                >
                  <option value="INR">Indian Rupee (₹)</option>
                  <option value="USD">US Dollar ($)</option>
                  <option value="EUR">Euro (€)</option>
                  <option value="GBP">British Pound (£)</option>
                </select>
              </div>

              <div>
                <label htmlFor="timezone" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <ClockIcon className="h-3.5 w-3.5 text-primary/70" />
                  Timezone
                </label>
                <select
                  id="timezone"
                  name="timezone"
                  className="w-full h-10 px-3 py-2 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
                  value={formData.timezone}
                  onChange={handleChange}
                >
                  <option value="Asia/Kolkata">India (GMT+5:30)</option>
                  <option value="America/New_York">Eastern Time (GMT-5:00)</option>
                  <option value="Europe/London">London (GMT+0:00)</option>
                  <option value="Asia/Dubai">Dubai (GMT+4:00)</option>
                </select>
              </div>

              <div>
                <label htmlFor="logo" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <ImageIcon className="h-3.5 w-3.5 text-primary/70" />
                  Store Logo
                </label>
                <div className="flex items-center gap-4 mt-2">
                  <div className="h-16 w-16 bg-muted/30 rounded-md flex items-center justify-center overflow-hidden border border-border/40 shadow-sm">
                    <img src={formData.logo} alt="Store logo" className="max-h-full max-w-full" />
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="hover:bg-primary/5 transition-colors"
                    onClick={handleLogoClick}
                    disabled={isChangingLogo}
                  >
                    <UploadIcon className="h-3.5 w-3.5 mr-1.5" />
                    {isChangingLogo ? 'Uploading...' : 'Change Logo'}
                  </Button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*"
                    onChange={handleLogoChange}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <SearchIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">SEO Settings</h2>
            </div>
            <div className="space-y-4">
              <div>
                <label htmlFor="metaTitle" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <TagIcon className="h-3.5 w-3.5 text-primary/70" />
                  Meta Title
                </label>
                <Input
                  id="metaTitle"
                  type="text"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  placeholder="Sajawat Sarees - Premium Indian Sarees"
                />
              </div>

              <div>
                <label htmlFor="metaDescription" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <FileTextIcon className="h-3.5 w-3.5 text-primary/70" />
                  Meta Description
                </label>
                <textarea
                  id="metaDescription"
                  rows={3}
                  className="w-full p-3 border border-input rounded-md shadow-xs transition-all duration-200 focus:border-primary/30 focus:outline-none focus:ring-2 focus:ring-ring/50"
                  placeholder="Discover our exclusive collection of premium Indian sarees for every occasion."
                />
              </div>
            </div>
          </div>

          <div className="pt-4">
            <Button type="submit" className="w-full transition-all duration-200 hover:bg-primary/90" onClick={handleSubmit}>
              <SaveIcon className="h-4 w-4 mr-2" />
              Save Store Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
