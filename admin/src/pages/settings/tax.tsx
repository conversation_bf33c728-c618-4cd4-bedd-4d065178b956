import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ChevronLeftIcon, SaveIcon, PercentIcon, PlusIcon, TrashIcon } from "lucide-react";

export function TaxSettings() {
  const [taxRates, setTaxRates] = React.useState([
    {
      id: 1,
      name: "GST 5%",
      rate: 5,
      country: "India",
      state: "All States",
      category: "Essential Textiles",
    },
    {
      id: 2,
      name: "GST 12%",
      rate: 12,
      country: "India",
      state: "All States",
      category: "Standard Textiles",
    },
    {
      id: 3,
      name: "GST 18%",
      rate: 18,
      country: "India",
      state: "All States",
      category: "Luxury Textiles",
    },
  ]);

  const [taxSettings, setTaxSettings] = React.useState({
    pricesIncludeTax: true,
    displayPricesInStore: "including_tax",
    taxCalculationBasis: "shipping",
    shippingTaxClass: "standard",
    roundTaxAtSubtotal: true,
    taxBasedOn: "shipping",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the tax settings here
    console.log("Tax settings saved:", { taxRates, taxSettings });
    alert("Tax settings updated successfully!");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              asChild
            >
              <Link to="/settings">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">Tax Settings</h1>
          </div>
          <p className="text-muted-foreground">
            Configure tax rates and settings.
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full">
                <PercentIcon className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Tax Rates</h2>
                <p className="text-muted-foreground">Configure tax rates by region</p>
              </div>
            </div>

            <div className="space-y-4">
              {taxRates.map((tax) => (
                <div key={tax.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h3 className="font-medium">{tax.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {tax.country} - {tax.state} • {tax.category}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">{tax.rate}%</span>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <TrashIcon className="h-4 w-4 text-destructive" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}

              <Button variant="outline" className="w-full">
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Tax Rate
              </Button>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Add Tax Rate</h2>
            <form className="space-y-4">
              <div>
                <label htmlFor="taxName" className="block text-sm font-medium mb-1">
                  Tax Name
                </label>
                <input
                  id="taxName"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g. GST 5%"
                />
              </div>

              <div>
                <label htmlFor="taxRate" className="block text-sm font-medium mb-1">
                  Tax Rate
                </label>
                <div className="flex">
                  <input
                    id="taxRate"
                    type="number"
                    className="w-full p-2 border rounded-l-md"
                    placeholder="0"
                  />
                  <span className="inline-flex items-center px-3 border border-l-0 rounded-r-md bg-muted">
                    %
                  </span>
                </div>
              </div>

              <div>
                <label htmlFor="country" className="block text-sm font-medium mb-1">
                  Country
                </label>
                <select
                  id="country"
                  className="w-full p-2 border rounded-md"
                  defaultValue="India"
                >
                  <option value="India">India</option>
                  <option value="United States">United States</option>
                  <option value="United Kingdom">United Kingdom</option>
                  <option value="Canada">Canada</option>
                  <option value="Australia">Australia</option>
                </select>
              </div>

              <div>
                <label htmlFor="state" className="block text-sm font-medium mb-1">
                  State/Region
                </label>
                <select
                  id="state"
                  className="w-full p-2 border rounded-md"
                  defaultValue="All States"
                >
                  <option value="All States">All States</option>
                  <option value="Gujarat">Gujarat</option>
                  <option value="Maharashtra">Maharashtra</option>
                  <option value="Delhi">Delhi</option>
                  <option value="Tamil Nadu">Tamil Nadu</option>
                </select>
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium mb-1">
                  Product Category
                </label>
                <select
                  id="category"
                  className="w-full p-2 border rounded-md"
                  defaultValue=""
                >
                  <option value="" disabled>Select a category</option>
                  <option value="Essential Textiles">Essential Textiles</option>
                  <option value="Standard Textiles">Standard Textiles</option>
                  <option value="Luxury Textiles">Luxury Textiles</option>
                  <option value="All Categories">All Categories</option>
                </select>
              </div>

              <Button type="submit" className="w-full">
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Tax Rate
              </Button>
            </form>
          </div>

          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Tax Options</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Prices Include Tax</h3>
                  <p className="text-sm text-muted-foreground">
                    Product prices include tax
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer" 
                    checked={taxSettings.pricesIncludeTax}
                    onChange={() => setTaxSettings({...taxSettings, pricesIncludeTax: !taxSettings.pricesIncludeTax})}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>

              <div>
                <label htmlFor="displayPrices" className="block text-sm font-medium mb-1">
                  Display Prices in Store
                </label>
                <select
                  id="displayPrices"
                  className="w-full p-2 border rounded-md"
                  value={taxSettings.displayPricesInStore}
                  onChange={(e) => setTaxSettings({...taxSettings, displayPricesInStore: e.target.value})}
                >
                  <option value="including_tax">Including tax</option>
                  <option value="excluding_tax">Excluding tax</option>
                  <option value="both">Both including and excluding tax</option>
                </select>
              </div>

              <div>
                <label htmlFor="taxBasedOn" className="block text-sm font-medium mb-1">
                  Calculate Tax Based On
                </label>
                <select
                  id="taxBasedOn"
                  className="w-full p-2 border rounded-md"
                  value={taxSettings.taxBasedOn}
                  onChange={(e) => setTaxSettings({...taxSettings, taxBasedOn: e.target.value})}
                >
                  <option value="shipping">Customer shipping address</option>
                  <option value="billing">Customer billing address</option>
                  <option value="store">Store address</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Round Tax at Subtotal</h3>
                  <p className="text-sm text-muted-foreground">
                    Calculate tax based on subtotal, not per item
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer" 
                    checked={taxSettings.roundTaxAtSubtotal}
                    onChange={() => setTaxSettings({...taxSettings, roundTaxAtSubtotal: !taxSettings.roundTaxAtSubtotal})}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <Button type="submit" className="w-full" onClick={handleSubmit}>
              <SaveIcon className="h-4 w-4 mr-2" />
              Save Tax Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
