import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  ChevronLeftIcon,
  SaveIcon,
  UsersIcon,
  PlusIcon,
  TrashIcon,
  EditIcon,
  UserPlusIcon,
  ShieldIcon,
  EyeIcon,
  EyeOffIcon
} from "lucide-react";

export function UsersSettings() {
  const [users, setUsers] = React.useState([
    {
      id: 1,
      name: "Admin User",
      email: "<EMAIL>",
      role: "Administrator",
      status: "Active",
      lastLogin: "2023-06-15 10:30 AM",
    },
    {
      id: 2,
      name: "Inventory Manager",
      email: "<EMAIL>",
      role: "Manager",
      status: "Active",
      lastLogin: "2023-06-14 02:15 PM",
    },
    {
      id: 3,
      name: "Order Processor",
      email: "<EMAIL>",
      role: "Staff",
      status: "Active",
      lastLogin: "2023-06-15 09:45 AM",
    },
    {
      id: 4,
      name: "Customer Support",
      email: "<EMAIL>",
      role: "Staff",
      status: "Inactive",
      lastLogin: "2023-05-30 11:20 AM",
    },
  ]);

  const [roles, setRoles] = React.useState([
    {
      id: 1,
      name: "Administrator",
      permissions: {
        dashboard: true,
        products: true,
        orders: true,
        customers: true,
        analytics: true,
        settings: true,
      },
    },
    {
      id: 2,
      name: "Manager",
      permissions: {
        dashboard: true,
        products: true,
        orders: true,
        customers: true,
        analytics: true,
        settings: false,
      },
    },
    {
      id: 3,
      name: "Staff",
      permissions: {
        dashboard: true,
        products: false,
        orders: true,
        customers: true,
        analytics: false,
        settings: false,
      },
    },
  ]);

  const [showAddUserForm, setShowAddUserForm] = React.useState(false);
  const [showAddRoleForm, setShowAddRoleForm] = React.useState(false);
  const [newUserData, setNewUserData] = React.useState({
    name: "",
    email: "",
    role: "Staff",
    password: "",
    confirmPassword: "",
  });

  const [newRoleData, setNewRoleData] = React.useState({
    name: "",
    permissions: {
      dashboard: true,
      products: true,
      orders: true,
      customers: true,
      analytics: false,
      settings: false,
    }
  });

  const handleNewUserChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewUserData((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddUser = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would add the user to the database
    if (newUserData.password !== newUserData.confirmPassword) {
      alert("Passwords do not match!");
      return;
    }

    const newUser = {
      id: users.length + 1,
      name: newUserData.name,
      email: newUserData.email,
      role: newUserData.role,
      status: "Active",
      lastLogin: "Never",
    };

    setUsers([...users, newUser]);
    setNewUserData({
      name: "",
      email: "",
      role: "Staff",
      password: "",
      confirmPassword: "",
    });
    setShowAddUserForm(false);
    alert("User added successfully!");
  };

  const handleNewRoleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    if (name === "roleName") {
      setNewRoleData(prev => ({ ...prev, name: value }));
    } else if (type === "checkbox") {
      // Handle permission toggles
      const permission = name.replace('permission_', '');
      setNewRoleData(prev => ({
        ...prev,
        permissions: {
          ...prev.permissions,
          [permission]: checked
        }
      }));
    }
  };

  const handleAddRole = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newRoleData.name.trim()) {
      alert("Role name is required!");
      return;
    }

    // Check if role name already exists
    if (roles.some(role => role.name.toLowerCase() === newRoleData.name.toLowerCase())) {
      alert("A role with this name already exists!");
      return;
    }

    const newRole = {
      id: roles.length + 1,
      name: newRoleData.name,
      permissions: { ...newRoleData.permissions }
    };

    setRoles([...roles, newRole]);
    setNewRoleData({
      name: "",
      permissions: {
        dashboard: true,
        products: true,
        orders: true,
        customers: true,
        analytics: false,
        settings: false,
      }
    });
    setShowAddRoleForm(false);
    alert("Role added successfully!");
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the users and roles settings here
    console.log("Users and roles saved:", { users, roles });
    alert("User settings updated successfully!");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              asChild
            >
              <Link to="/settings">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">User Settings</h1>
          </div>
          <p className="text-muted-foreground">
            Manage staff accounts and permissions.
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="bg-primary/10 p-4 rounded-full">
                  <UsersIcon className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold">Staff Accounts</h2>
                  <p className="text-muted-foreground">Manage staff users</p>
                </div>
              </div>
              <Button onClick={() => setShowAddUserForm(!showAddUserForm)}>
                <UserPlusIcon className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </div>

            {showAddUserForm && (
              <div className="mb-6 p-4 border rounded-lg">
                <h3 className="font-medium mb-3">Add New User</h3>
                <form onSubmit={handleAddUser} className="space-y-3">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium mb-1">
                      Full Name
                    </label>
                    <input
                      id="name"
                      name="name"
                      type="text"
                      className="w-full p-2 border rounded-md"
                      value={newUserData.name}
                      onChange={handleNewUserChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-1">
                      Email
                    </label>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      className="w-full p-2 border rounded-md"
                      value={newUserData.email}
                      onChange={handleNewUserChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="role" className="block text-sm font-medium mb-1">
                      Role
                    </label>
                    <select
                      id="role"
                      name="role"
                      className="w-full p-2 border rounded-md"
                      value={newUserData.role}
                      onChange={handleNewUserChange}
                    >
                      {roles.map((role) => (
                        <option key={role.id} value={role.name}>
                          {role.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium mb-1">
                      Password
                    </label>
                    <input
                      id="password"
                      name="password"
                      type="password"
                      className="w-full p-2 border rounded-md"
                      value={newUserData.password}
                      onChange={handleNewUserChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium mb-1">
                      Confirm Password
                    </label>
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      className="w-full p-2 border rounded-md"
                      value={newUserData.confirmPassword}
                      onChange={handleNewUserChange}
                      required
                    />
                  </div>
                  <div className="flex gap-2 pt-2">
                    <Button type="submit">
                      Add User
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowAddUserForm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </div>
            )}

            <div className="space-y-4">
              {users.map((user) => (
                <div key={user.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h3 className="font-medium">{user.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        user.status === "Active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                      }`}>
                        {user.status}
                      </span>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <EditIcon className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <TrashIcon className="h-4 w-4 text-destructive" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span className="text-muted-foreground">Role: {user.role}</span>
                    <span className="text-muted-foreground">Last login: {user.lastLogin}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="bg-primary/10 p-4 rounded-full">
                  <ShieldIcon className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold">User Roles</h2>
                  <p className="text-muted-foreground">Manage role permissions</p>
                </div>
              </div>
              <Button onClick={() => setShowAddRoleForm(!showAddRoleForm)}>
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Role
              </Button>
            </div>

            {showAddRoleForm && (
              <div className="mb-6 p-4 border rounded-lg">
                <h3 className="font-medium mb-3">Add New Role</h3>
                <form className="space-y-3" onSubmit={handleAddRole}>
                  <div>
                    <label htmlFor="roleName" className="block text-sm font-medium mb-1">
                      Role Name
                    </label>
                    <input
                      id="roleName"
                      name="roleName"
                      type="text"
                      className="w-full p-2 border rounded-md"
                      placeholder="e.g. Content Editor"
                      value={newRoleData.name}
                      onChange={handleNewRoleChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Permissions</h4>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Dashboard</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="permission_dashboard"
                          className="sr-only peer"
                          checked={newRoleData.permissions.dashboard}
                          onChange={handleNewRoleChange}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Products</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="permission_products"
                          className="sr-only peer"
                          checked={newRoleData.permissions.products}
                          onChange={handleNewRoleChange}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Orders</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="permission_orders"
                          className="sr-only peer"
                          checked={newRoleData.permissions.orders}
                          onChange={handleNewRoleChange}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Customers</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="permission_customers"
                          className="sr-only peer"
                          checked={newRoleData.permissions.customers}
                          onChange={handleNewRoleChange}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Analytics</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="permission_analytics"
                          className="sr-only peer"
                          checked={newRoleData.permissions.analytics}
                          onChange={handleNewRoleChange}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Settings</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="permission_settings"
                          className="sr-only peer"
                          checked={newRoleData.permissions.settings}
                          onChange={handleNewRoleChange}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
                      </label>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button type="submit">
                      Add Role
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowAddRoleForm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </div>
            )}

            <div className="space-y-4">
              {roles.map((role) => (
                <div key={role.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium">{role.name}</h3>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <EditIcon className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      {role.name !== "Administrator" && (
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <TrashIcon className="h-4 w-4 text-destructive" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="space-y-1 text-sm">
                    <div className="flex items-center justify-between">
                      <span>Dashboard</span>
                      {role.permissions.dashboard ? (
                        <EyeIcon className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOffIcon className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Products</span>
                      {role.permissions.products ? (
                        <EyeIcon className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOffIcon className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Orders</span>
                      {role.permissions.orders ? (
                        <EyeIcon className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOffIcon className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Customers</span>
                      {role.permissions.customers ? (
                        <EyeIcon className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOffIcon className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Analytics</span>
                      {role.permissions.analytics ? (
                        <EyeIcon className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOffIcon className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Settings</span>
                      {role.permissions.settings ? (
                        <EyeIcon className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOffIcon className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="pt-4">
            <Button type="submit" className="w-full" onClick={handleSubmit}>
              <SaveIcon className="h-4 w-4 mr-2" />
              Save User Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
