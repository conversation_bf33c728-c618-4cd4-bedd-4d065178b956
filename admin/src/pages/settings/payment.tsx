import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  ChevronLeftIcon,
  SaveIcon,
  CreditCardIcon,
  PlusIcon,
  DollarSignIcon,
  KeyIcon,
  ToggleLeftIcon,
  TruckIcon,
  FileTextIcon,
  ReceiptIcon,
  CheckIcon,
  BanknoteIcon,
  WalletIcon, // Replacing CashIcon
  CreditCardIcon as CreditCardAltIcon,
  GlobeIcon
} from "lucide-react";

export function PaymentSettings() {
  const [paymentMethods, setPaymentMethods] = React.useState([
    {
      id: 1,
      name: "Cash on Delivery",
      enabled: true,
      description: "Pay when you receive your order",
    },
    {
      id: 2,
      name: "Razorpay",
      enabled: true,
      description: "Accept credit/debit cards and UPI payments",
      apiKey: "rzp_test_1234567890",
      secretKey: "••••••••••••••••",
    },
    {
      id: 3,
      name: "PayPal",
      enabled: false,
      description: "Accept international payments",
      clientId: "",
      clientSecret: "",
    },
  ]);

  const [showAddMethodForm, setShowAddMethodForm] = React.useState(false);
  const [newPaymentMethod, setNewPaymentMethod] = React.useState({
    name: "",
    description: "",
    type: "gateway", // gateway, manual, etc.
  });

  const handleToggleMethod = (id: number) => {
    setPaymentMethods(
      paymentMethods.map((method) =>
        method.id === id ? { ...method, enabled: !method.enabled } : method
      )
    );
  };

  const handleNewMethodChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewPaymentMethod(prev => ({ ...prev, [name]: value }));
  };

  const handleAddMethod = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newPaymentMethod.name.trim()) {
      alert("Payment method name is required!");
      return;
    }

    // Check if method name already exists
    if (paymentMethods.some(method => method.name.toLowerCase() === newPaymentMethod.name.toLowerCase())) {
      alert("A payment method with this name already exists!");
      return;
    }

    const newMethod = {
      id: paymentMethods.length + 1,
      name: newPaymentMethod.name,
      description: newPaymentMethod.description,
      enabled: true,
      type: newPaymentMethod.type,
    };

    setPaymentMethods([...paymentMethods, newMethod]);
    setNewPaymentMethod({
      name: "",
      description: "",
      type: "gateway",
    });
    setShowAddMethodForm(false);
    alert("Payment method added successfully!");
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the payment settings here
    console.log("Payment settings saved:", paymentMethods);
    alert("Payment settings updated successfully!");
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/settings">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <div className="flex items-center gap-2">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <CreditCardIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h1 className="text-2xl font-bold tracking-tight">Payment Settings</h1>
            </div>
          </div>
          <p className="text-muted-foreground ml-10">
            Manage payment methods and transaction settings.
          </p>
        </div>
      </div>

      <Separator className="animate-fade-in" />

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4 animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full shadow-sm">
                <CreditCardIcon className="h-8 w-8 text-primary/80" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Payment Methods</h2>
                <p className="text-muted-foreground flex items-center gap-1.5">
                  <BanknoteIcon className="h-3.5 w-3.5" />
                  Configure how customers can pay
                </p>
              </div>
            </div>

            <div className="space-y-4">
              {paymentMethods.map((method, index) => {
                const getMethodIcon = (name: string) => {
                  switch (name) {
                    case "Cash on Delivery": return <WalletIcon className="h-5 w-5 text-primary/80" />;
                    case "Razorpay": return <CreditCardAltIcon className="h-5 w-5 text-primary/80" />;
                    case "PayPal": return <GlobeIcon className="h-5 w-5 text-primary/80" />;
                    default: return <CreditCardIcon className="h-5 w-5 text-primary/80" />;
                  }
                };

                return (
                  <div
                    key={method.id}
                    className={`border border-border/40 rounded-lg p-5 transition-all duration-200 ${method.enabled ? 'bg-card shadow-sm' : 'bg-muted/20'} hover:shadow-md animate-slide-up`}
                    style={{ animationDelay: `${index * 50 + 100}ms` }}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-primary/10 p-2 rounded-full">
                          {getMethodIcon(method.name)}
                        </div>
                        <div>
                          <h3 className="font-medium text-base">{method.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {method.description}
                          </p>
                        </div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={method.enabled}
                          onChange={() => handleToggleMethod(method.id)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary shadow-sm"></div>
                      </label>
                    </div>

                    {method.enabled && method.name === "Razorpay" && (
                      <div className="mt-4 space-y-3 pt-3 border-t border-border/40">
                        <div>
                          <label htmlFor="apiKey" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                            <KeyIcon className="h-3.5 w-3.5 text-primary/70" />
                            API Key
                          </label>
                          <Input
                            id="apiKey"
                            type="text"
                            className="w-full transition-all duration-200 focus:border-primary/30"
                            defaultValue={method.apiKey}
                          />
                        </div>
                        <div>
                          <label htmlFor="secretKey" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                            <KeyIcon className="h-3.5 w-3.5 text-primary/70" />
                            Secret Key
                          </label>
                          <Input
                            id="secretKey"
                            type="password"
                            className="w-full transition-all duration-200 focus:border-primary/30"
                            defaultValue={method.secretKey}
                          />
                        </div>
                      </div>
                    )}

                    {method.enabled && method.name === "PayPal" && (
                      <div className="mt-4 space-y-3 pt-3 border-t border-border/40">
                        <div>
                          <label htmlFor="clientId" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                            <KeyIcon className="h-3.5 w-3.5 text-primary/70" />
                            Client ID
                          </label>
                          <Input
                            id="clientId"
                            type="text"
                            className="w-full transition-all duration-200 focus:border-primary/30"
                            placeholder="Enter PayPal Client ID"
                          />
                        </div>
                        <div>
                          <label htmlFor="clientSecret" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                            <KeyIcon className="h-3.5 w-3.5 text-primary/70" />
                            Client Secret
                          </label>
                          <Input
                            id="clientSecret"
                            type="password"
                            className="w-full transition-all duration-200 focus:border-primary/30"
                            placeholder="Enter PayPal Client Secret"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}

              <Button
                variant="outline"
                className="w-full hover:bg-primary/5 transition-colors"
                onClick={() => setShowAddMethodForm(!showAddMethodForm)}
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                {showAddMethodForm ? 'Cancel' : 'Add Payment Method'}
              </Button>

              {showAddMethodForm && (
                <div className="mt-4 p-4 border border-border/40 rounded-lg bg-muted/10 animate-fade-in">
                  <h3 className="text-base font-medium mb-3">Add New Payment Method</h3>
                  <form onSubmit={handleAddMethod} className="space-y-3">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium mb-1.5">
                        Method Name
                      </label>
                      <Input
                        id="name"
                        name="name"
                        value={newPaymentMethod.name}
                        onChange={handleNewMethodChange}
                        className="w-full"
                        placeholder="e.g. Stripe, Bank Transfer"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="description" className="block text-sm font-medium mb-1.5">
                        Description
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        value={newPaymentMethod.description}
                        onChange={handleNewMethodChange}
                        className="w-full p-3 border border-input rounded-md shadow-xs transition-all duration-200 focus:border-primary/30 focus:outline-none focus:ring-2 focus:ring-ring/50"
                        placeholder="Short description of the payment method"
                        rows={2}
                      />
                    </div>

                    <div>
                      <label htmlFor="type" className="block text-sm font-medium mb-1.5">
                        Method Type
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={newPaymentMethod.type}
                        onChange={handleNewMethodChange}
                        className="w-full h-10 px-3 py-2 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
                      >
                        <option value="gateway">Payment Gateway</option>
                        <option value="manual">Manual Payment</option>
                        <option value="cod">Cash on Delivery</option>
                        <option value="wallet">Digital Wallet</option>
                      </select>
                    </div>

                    <div className="pt-2">
                      <Button type="submit" className="w-full">
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Method
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4 animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <DollarSignIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Transaction Settings</h2>
            </div>
            <div className="space-y-4">
              <div>
                <label htmlFor="currency" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <DollarSignIcon className="h-3.5 w-3.5 text-primary/70" />
                  Currency
                </label>
                <select
                  id="currency"
                  className="w-full h-10 px-3 py-2 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
                  defaultValue="INR"
                >
                  <option value="INR">Indian Rupee (₹)</option>
                  <option value="USD">US Dollar ($)</option>
                  <option value="EUR">Euro (€)</option>
                  <option value="GBP">British Pound (£)</option>
                </select>
              </div>

              <div>
                <label htmlFor="minOrderAmount" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <BanknoteIcon className="h-3.5 w-3.5 text-primary/70" />
                  Minimum Order Amount
                </label>
                <div className="flex">
                  <span className="inline-flex items-center px-3 border border-r-0 rounded-l-md bg-muted/30 text-muted-foreground">
                    ₹
                  </span>
                  <Input
                    id="minOrderAmount"
                    type="number"
                    className="rounded-l-none transition-all duration-200 focus:border-primary/30"
                    defaultValue="499"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <WalletIcon className="h-4 w-4 text-primary/70" />
                    Cash on Delivery Fee
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Charge extra for COD orders
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary shadow-sm"></div>
                </label>
              </div>

              <div>
                <label htmlFor="codFee" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <BanknoteIcon className="h-3.5 w-3.5 text-primary/70" />
                  COD Fee Amount
                </label>
                <div className="flex">
                  <span className="inline-flex items-center px-3 border border-r-0 rounded-l-md bg-muted/30 text-muted-foreground">
                    ₹
                  </span>
                  <Input
                    id="codFee"
                    type="number"
                    className="rounded-l-none transition-all duration-200 focus:border-primary/30"
                    defaultValue="50"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <ReceiptIcon className="h-5 w-5 text-primary/80" />
              </div>
              <h2 className="text-xl font-semibold">Invoice Settings</h2>
            </div>
            <div className="space-y-4">
              <div>
                <label htmlFor="invoicePrefix" className="block text-sm font-medium mb-1.5 flex items-center gap-1.5">
                  <FileTextIcon className="h-3.5 w-3.5 text-primary/70" />
                  Invoice Prefix
                </label>
                <Input
                  id="invoicePrefix"
                  type="text"
                  className="w-full transition-all duration-200 focus:border-primary/30"
                  defaultValue="SS-INV-"
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
                <div>
                  <h3 className="font-medium flex items-center gap-1.5">
                    <CheckIcon className="h-4 w-4 text-primary/70" />
                    Auto-generate Invoice
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Create invoice automatically when order is placed
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary shadow-sm"></div>
                </label>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <Button type="submit" className="w-full transition-all duration-200 hover:bg-primary/90" onClick={handleSubmit}>
              <SaveIcon className="h-4 w-4 mr-2" />
              Save Payment Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
