import * as React from "react";
import { <PERSON> } from "react-router-dom";
import {
  BarChart3Icon,
  TrendingUpIcon,
  UsersIcon,
  ShoppingBagIcon,
  CreditCardIcon,
  PieChartIcon,
  LineChartIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PackageIcon,
  DownloadIcon,
  PrinterIcon,
  RefreshCwIcon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

export function Analytics() {
  const [isExporting, setIsExporting] = React.useState(false);
  const [isPrinting, setIsPrinting] = React.useState(false);

  // Sample data for the dashboard
  const dashboardData = {
    totalRevenue: 45250,
    orders: 128,
    customers: 215,
    avgOrderValue: 353,
    revenueGrowth: 12,
    ordersGrowth: 8,
    customersGrowth: 15,
    aovGrowth: -3,
    monthlySales: [
      { name: 'Jan', revenue: 12000, orders: 120 },
      { name: 'Feb', revenue: 15000, orders: 150 },
      { name: 'Mar', revenue: 18000, orders: 170 },
      { name: 'Apr', revenue: 16000, orders: 160 },
      { name: 'May', revenue: 21000, orders: 200 },
      { name: 'Jun', revenue: 19000, orders: 180 },
    ],
    topProducts: [
      { name: 'Banarasi Silk Saree', sold: 15, revenue: 89985 },
      { name: 'Kanjivaram Silk Saree', sold: 8, revenue: 71992 },
      { name: 'Cotton Handloom Saree', sold: 25, revenue: 62475 },
      { name: 'Linen Saree', sold: 12, revenue: 41988 },
    ],
    recentOrders: [
      { id: 'ORD-001', customer: 'Rahul Sharma', amount: 2450, status: 'Completed' },
      { id: 'ORD-002', customer: 'Priya Patel', amount: 1850, status: 'Processing' },
      { id: 'ORD-003', customer: 'Amit Kumar', amount: 3200, status: 'Shipped' },
      { id: 'ORD-004', customer: 'Sneha Gupta', amount: 1750, status: 'Completed' },
    ],
    customerDemographics: [
      { name: '18-24', value: 25 },
      { name: '25-34', value: 40 },
      { name: '35-44', value: 20 },
      { name: '45-54', value: 10 },
      { name: '55+', value: 5 },
    ]
  };

  // Function to export the dashboard as CSV
  const handleExportReport = () => {
    setIsExporting(true);

    try {
      // Create CSV content for each section
      const revenueData = [
        "Revenue Overview",
        "Total Revenue,Orders,Customers,Avg. Order Value",
        `${dashboardData.totalRevenue},${dashboardData.orders},${dashboardData.customers},${dashboardData.avgOrderValue}`,
        "",
        "Monthly Sales",
        "Month,Revenue,Orders",
        ...dashboardData.monthlySales.map(item => `${item.name},${item.revenue},${item.orders}`),
        "",
        "Top Products",
        "Product,Units Sold,Revenue",
        ...dashboardData.topProducts.map(item => `"${item.name}",${item.sold},${item.revenue}`),
        "",
        "Recent Orders",
        "Order ID,Customer,Amount,Status",
        ...dashboardData.recentOrders.map(item => `${item.id},"${item.customer}",${item.amount},${item.status}`),
        "",
        "Customer Demographics",
        "Age Group,Percentage",
        ...dashboardData.customerDemographics.map(item => `${item.name},${item.value}%`),
      ].join("\n");

      // Create a blob and trigger download
      const blob = new Blob([revenueData], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `dashboard_report_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

    } catch (error) {
      console.error("Error exporting report:", error);
      alert("Failed to export report. Please try again.");
    } finally {
      setTimeout(() => {
        setIsExporting(false);
      }, 1000);
    }
  };

  // Function to print the dashboard
  const handlePrintReport = () => {
    setIsPrinting(true);

    try {
      // Create a print-friendly version
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error("Could not open print window. Please check your popup blocker settings.");
      }

      printWindow.document.write(`
        <html>
          <head>
            <title>Dashboard Report - ${new Date().toLocaleDateString()}</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1, h2 { color: #333; }
              .dashboard-section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
              .metrics { display: flex; flex-wrap: wrap; gap: 20px; margin: 20px 0; }
              .metric { flex: 1; min-width: 200px; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }
              .metric h3 { margin: 0 0 10px 0; font-size: 16px; }
              .metric .value { font-size: 24px; font-weight: bold; margin: 0; }
              .metric .change { font-size: 12px; margin: 5px 0 0 0; }
              .positive { color: green; }
              .negative { color: red; }
              table { width: 100%; border-collapse: collapse; margin-top: 20px; }
              th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
              th { background-color: #f2f2f2; }
              .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
            </style>
          </head>
          <body>
            <h1>Dashboard Report</h1>
            <p>Generated on ${new Date().toLocaleString()}</p>

            <div class="dashboard-section">
              <h2>Overview</h2>
              <div class="metrics">
                <div class="metric">
                  <h3>Total Revenue</h3>
                  <p class="value">₹${dashboardData.totalRevenue.toLocaleString()}</p>
                  <p class="change ${dashboardData.revenueGrowth >= 0 ? 'positive' : 'negative'}">
                    ${dashboardData.revenueGrowth >= 0 ? '↑' : '↓'} ${Math.abs(dashboardData.revenueGrowth)}% from last month
                  </p>
                </div>
                <div class="metric">
                  <h3>Orders</h3>
                  <p class="value">${dashboardData.orders}</p>
                  <p class="change ${dashboardData.ordersGrowth >= 0 ? 'positive' : 'negative'}">
                    ${dashboardData.ordersGrowth >= 0 ? '↑' : '↓'} ${Math.abs(dashboardData.ordersGrowth)}% from last month
                  </p>
                </div>
                <div class="metric">
                  <h3>Customers</h3>
                  <p class="value">${dashboardData.customers}</p>
                  <p class="change ${dashboardData.customersGrowth >= 0 ? 'positive' : 'negative'}">
                    ${dashboardData.customersGrowth >= 0 ? '↑' : '↓'} ${Math.abs(dashboardData.customersGrowth)}% from last month
                  </p>
                </div>
                <div class="metric">
                  <h3>Avg. Order Value</h3>
                  <p class="value">₹${dashboardData.avgOrderValue}</p>
                  <p class="change ${dashboardData.aovGrowth >= 0 ? 'positive' : 'negative'}">
                    ${dashboardData.aovGrowth >= 0 ? '↑' : '↓'} ${Math.abs(dashboardData.aovGrowth)}% from last month
                  </p>
                </div>
              </div>
            </div>

            <div class="dashboard-section">
              <h2>Monthly Sales</h2>
              <table>
                <thead>
                  <tr>
                    <th>Month</th>
                    <th>Revenue</th>
                    <th>Orders</th>
                  </tr>
                </thead>
                <tbody>
                  ${dashboardData.monthlySales.map(item => `
                    <tr>
                      <td>${item.name}</td>
                      <td>₹${item.revenue.toLocaleString()}</td>
                      <td>${item.orders}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="dashboard-section">
              <h2>Top Products</h2>
              <table>
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Units Sold</th>
                    <th>Revenue</th>
                  </tr>
                </thead>
                <tbody>
                  ${dashboardData.topProducts.map(item => `
                    <tr>
                      <td>${item.name}</td>
                      <td>${item.sold}</td>
                      <td>₹${item.revenue.toLocaleString()}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="dashboard-section">
              <h2>Recent Orders</h2>
              <table>
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Amount</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  ${dashboardData.recentOrders.map(item => `
                    <tr>
                      <td>${item.id}</td>
                      <td>${item.customer}</td>
                      <td>₹${item.amount.toLocaleString()}</td>
                      <td>${item.status}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="footer">
              <p>Sajawat Sarees Admin Dashboard | Confidential Business Report</p>
            </div>
          </body>
        </html>
      `);

      printWindow.document.close();
      printWindow.focus();

      // Print after a short delay to ensure content is loaded
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        setIsPrinting(false);
      }, 500);

    } catch (error) {
      console.error("Error printing report:", error);
      alert("Failed to print report. Please try again.");
      setIsPrinting(false);
    }
  };

  return (
    <div className="animate-fade-in">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6 animate-slide-up">
        <div className="flex items-center gap-2">
          <BarChart3Icon className="h-8 w-8 text-primary/80" />
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handlePrintReport}
            disabled={isPrinting}
          >
            {isPrinting ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <PrinterIcon className="mr-2 h-4 w-4" />
            )}
            {isPrinting ? 'Printing...' : 'Print Dashboard'}
          </Button>
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handleExportReport}
            disabled={isExporting}
          >
            {isExporting ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <DownloadIcon className="mr-2 h-4 w-4" />
            )}
            {isExporting ? 'Exporting...' : 'Export Dashboard'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <TrendingUpIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Total Revenue</h2>
          </div>
          <p className="text-3xl font-bold">₹45,250</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>12% from last month</span>
          </p>
        </div>
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <ShoppingBagIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Orders</h2>
          </div>
          <p className="text-3xl font-bold">128</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>8% from last month</span>
          </p>
        </div>
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '150ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <UsersIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Customers</h2>
          </div>
          <p className="text-3xl font-bold">215</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>15% from last month</span>
          </p>
        </div>
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '200ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <CreditCardIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Avg. Order Value</h2>
          </div>
          <p className="text-3xl font-bold">₹353</p>
          <p className="text-sm text-red-600 mt-2 flex items-center gap-1">
            <ArrowDownIcon className="h-3.5 w-3.5" />
            <span>3% from last month</span>
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '250ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <LineChartIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Sales Overview</h2>
          </div>
          <div className="h-64 rounded-lg border border-border/40 overflow-hidden relative group p-4">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-sm font-medium">Monthly Sales</h3>
              <div className="flex items-center gap-2 text-xs">
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-primary/80"></div>
                  Revenue
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                  Orders
                </span>
              </div>
            </div>
            <ResponsiveContainer width="100%" height="85%">
              <AreaChart data={[
                { name: 'Jan', revenue: 12000, orders: 120 },
                { name: 'Feb', revenue: 15000, orders: 150 },
                { name: 'Mar', revenue: 18000, orders: 170 },
                { name: 'Apr', revenue: 16000, orders: 160 },
                { name: 'May', revenue: 21000, orders: 200 },
                { name: 'Jun', revenue: 19000, orders: 180 },
              ]}>
                <defs>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="var(--chart-1)" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="var(--chart-1)" stopOpacity={0}/>
                  </linearGradient>
                  <linearGradient id="colorOrders" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="var(--chart-2)" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="var(--chart-2)" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <XAxis dataKey="name" axisLine={false} tickLine={false} />
                <YAxis axisLine={false} tickLine={false} />
                <Tooltip />
                <Area type="monotone" dataKey="revenue" stroke="var(--chart-1)" fillOpacity={1} fill="url(#colorRevenue)" />
                <Area type="monotone" dataKey="orders" stroke="var(--chart-2)" fillOpacity={1} fill="url(#colorOrders)" />
              </AreaChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" asChild className="text-xs hover:bg-primary/5 transition-colors">
              <Link to="/analytics/sales">
                View Detailed Report
              </Link>
            </Button>
          </div>
        </div>
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '300ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <ShoppingBagIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Top Products</h2>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 rounded-lg hover:bg-muted/30 transition-colors">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                  <PackageIcon className="h-5 w-5 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">Banarasi Silk Saree</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <ShoppingBagIcon className="h-3 w-3" />
                    15 sold
                  </p>
                </div>
              </div>
              <p className="font-medium">₹89,985</p>
            </div>
            <div className="flex justify-between items-center p-3 rounded-lg hover:bg-muted/30 transition-colors">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                  <PackageIcon className="h-5 w-5 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">Kanjivaram Silk Saree</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <ShoppingBagIcon className="h-3 w-3" />
                    8 sold
                  </p>
                </div>
              </div>
              <p className="font-medium">₹71,992</p>
            </div>
            <div className="flex justify-between items-center p-3 rounded-lg hover:bg-muted/30 transition-colors">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                  <PackageIcon className="h-5 w-5 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">Cotton Handloom Saree</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <ShoppingBagIcon className="h-3 w-3" />
                    25 sold
                  </p>
                </div>
              </div>
              <p className="font-medium">₹62,475</p>
            </div>
            <div className="flex justify-between items-center p-3 rounded-lg hover:bg-muted/30 transition-colors">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                  <PackageIcon className="h-5 w-5 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">Linen Saree</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <ShoppingBagIcon className="h-3 w-3" />
                    12 sold
                  </p>
                </div>
              </div>
              <p className="font-medium">₹41,988</p>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" asChild className="text-xs hover:bg-primary/5 transition-colors">
              <Link to="/analytics/products">
                View All Products
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '350ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <ShoppingBagIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Recent Orders</h2>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
              <div className="flex items-center gap-2">
                <div className="bg-primary/5 p-1.5 rounded-full">
                  <ShoppingBagIcon className="h-4 w-4 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">#ORD-001</p>
                  <p className="text-sm text-muted-foreground">Rahul Sharma</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">₹2,450</p>
                <p className="text-sm text-green-600 flex items-center justify-end gap-1">
                  <span className="h-2 w-2 rounded-full bg-green-500"></span>
                  Completed
                </p>
              </div>
            </div>
            <div className="flex justify-between items-center p-3 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
              <div className="flex items-center gap-2">
                <div className="bg-primary/5 p-1.5 rounded-full">
                  <ShoppingBagIcon className="h-4 w-4 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">#ORD-002</p>
                  <p className="text-sm text-muted-foreground">Priya Patel</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">₹1,850</p>
                <p className="text-sm text-yellow-600 flex items-center justify-end gap-1">
                  <span className="h-2 w-2 rounded-full bg-yellow-500 animate-pulse"></span>
                  Processing
                </p>
              </div>
            </div>
            <div className="flex justify-between items-center p-3 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
              <div className="flex items-center gap-2">
                <div className="bg-primary/5 p-1.5 rounded-full">
                  <ShoppingBagIcon className="h-4 w-4 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">#ORD-003</p>
                  <p className="text-sm text-muted-foreground">Amit Kumar</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">₹3,200</p>
                <p className="text-sm text-blue-600 flex items-center justify-end gap-1">
                  <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                  Shipped
                </p>
              </div>
            </div>
            <div className="flex justify-between items-center p-3 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
              <div className="flex items-center gap-2">
                <div className="bg-primary/5 p-1.5 rounded-full">
                  <ShoppingBagIcon className="h-4 w-4 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">#ORD-004</p>
                  <p className="text-sm text-muted-foreground">Sneha Gupta</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">₹1,750</p>
                <p className="text-sm text-green-600 flex items-center justify-end gap-1">
                  <span className="h-2 w-2 rounded-full bg-green-500"></span>
                  Completed
                </p>
              </div>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" asChild className="text-xs hover:bg-primary/5 transition-colors">
              <Link to="/orders">
                View All Orders
              </Link>
            </Button>
          </div>
        </div>
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '400ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <PieChartIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Customer Demographics</h2>
          </div>
          <div className="h-64 rounded-lg border border-border/40 overflow-hidden relative group p-4">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-sm font-medium">Customer Demographics</h3>
              <div className="flex items-center gap-2 text-xs">
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-primary/80"></div>
                  Age 18-24
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                  Age 25-34
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-green-400"></div>
                  Age 35-44
                </span>
              </div>
            </div>
            <ResponsiveContainer width="100%" height="85%">
              <PieChart>
                <Pie
                  data={[
                    { name: '18-24', value: 25 },
                    { name: '25-34', value: 40 },
                    { name: '35-44', value: 20 },
                    { name: '45-54', value: 10 },
                    { name: '55+', value: 5 },
                  ]}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  <Cell fill="var(--chart-1)" />
                  <Cell fill="var(--chart-2)" />
                  <Cell fill="var(--chart-3)" />
                  <Cell fill="var(--chart-4)" />
                  <Cell fill="#8884d8" />
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" size="sm" asChild className="text-xs hover:bg-primary/5 transition-colors">
              <Link to="/analytics/customers">
                View Customer Analytics
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
