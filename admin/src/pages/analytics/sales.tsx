import * as React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  ChevronLeftIcon,
  DownloadIcon,
  CalendarIcon,
  FilterIcon,
  TrendingUpIcon,
  ShoppingBagIcon,
  CreditCardIcon,
  LineChartIcon,
  PieChartIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PackageIcon,
  PrinterIcon,
  RefreshCwIcon
} from "lucide-react";
import { useAppSelector } from "@/hooks/use-redux";
import {
  AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  Legend, BarChart, Bar, PieChart, Pie, Cell
} from 'recharts';

export function SalesReport() {
  const orders = useAppSelector((state) => state.orders.items);
  const [dateRange, setDateRange] = React.useState("last30");
  const [filterStatus, setFilterStatus] = React.useState("all");
  const [isExporting, setIsExporting] = React.useState(false);
  const [isPrinting, setIsPrinting] = React.useState(false);

  // Calculate sales metrics
  const salesMetrics = React.useMemo(() => {
    // Filter orders based on date range and status
    let filteredOrders = [...orders];

    // Apply date range filter
    const today = new Date();
    const getDateLimit = () => {
      switch (dateRange) {
        case "last7":
          const sevenDaysAgo = new Date(today);
          sevenDaysAgo.setDate(today.getDate() - 7);
          return sevenDaysAgo;
        case "last30":
          const thirtyDaysAgo = new Date(today);
          thirtyDaysAgo.setDate(today.getDate() - 30);
          return thirtyDaysAgo;
        case "last90":
          const ninetyDaysAgo = new Date(today);
          ninetyDaysAgo.setDate(today.getDate() - 90);
          return ninetyDaysAgo;
        case "year":
          return new Date(today.getFullYear(), 0, 1); // January 1st of current year
        default:
          return new Date(0); // Beginning of time
      }
    };

    const dateLimit = getDateLimit();
    filteredOrders = filteredOrders.filter(order => {
      const orderDate = new Date(order.date);
      return orderDate >= dateLimit && orderDate <= today;
    });

    // Apply status filter
    if (filterStatus !== "all") {
      filteredOrders = filteredOrders.filter(order => order.status === filterStatus);
    }

    // Sort by date
    filteredOrders.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Calculate total revenue
    const totalRevenue = filteredOrders.reduce((sum, order) => sum + order.amount, 0);

    // Calculate average order value
    const avgOrderValue = filteredOrders.length > 0
      ? Math.round(totalRevenue / filteredOrders.length)
      : 0;

    // Group orders by date for the chart
    const salesByDate = filteredOrders.reduce((acc, order) => {
      const date = order.date;
      if (!acc[date]) {
        acc[date] = 0;
      }
      acc[date] += order.amount;
      return acc;
    }, {} as Record<string, number>);

    // Convert to array for chart
    const chartData = Object.entries(salesByDate).map(([date, amount]) => ({
      date,
      amount
    }));

    return {
      totalOrders: filteredOrders.length,
      totalRevenue,
      avgOrderValue,
      chartData
    };
  }, [orders, dateRange, filterStatus]);

  // Function to export the report as CSV
  const handleExportReport = () => {
    setIsExporting(true);

    try {
      // Create CSV content
      const headers = ["Date", "Revenue", "Orders"];
      const csvContent = [
        headers.join(","),
        ...salesMetrics.chartData.map(item =>
          `${item.date},${item.amount},${Math.round(item.amount / salesMetrics.avgOrderValue)}`
        )
      ].join("\n");

      // Create a blob and trigger download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_report_${dateRange}_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // In a real app, you might want to use a library like ExcelJS or jsPDF for better formatting
    } catch (error) {
      console.error("Error exporting report:", error);
      alert("Failed to export report. Please try again.");
    } finally {
      setTimeout(() => {
        setIsExporting(false);
      }, 1000);
    }
  };

  // Function to print the report
  const handlePrintReport = () => {
    setIsPrinting(true);

    try {
      // In a real app, you would format the report for printing
      // For now, we'll just use the browser's print functionality

      // Create a print-friendly version
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error("Could not open print window. Please check your popup blocker settings.");
      }

      printWindow.document.write(`
        <html>
          <head>
            <title>Sales Report - ${new Date().toLocaleDateString()}</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { color: #333; }
              table { width: 100%; border-collapse: collapse; margin-top: 20px; }
              th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
              th { background-color: #f2f2f2; }
              .summary { margin: 20px 0; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }
              .summary div { margin: 10px 0; }
              .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
            </style>
          </head>
          <body>
            <h1>Sales Report</h1>
            <div>Date Range: ${dateRange === "last7" ? "Last 7 Days" :
                              dateRange === "last30" ? "Last 30 Days" :
                              dateRange === "last90" ? "Last 90 Days" :
                              dateRange === "year" ? "This Year" : "Custom Range"}</div>
            <div>Status Filter: ${filterStatus === "all" ? "All Orders" : filterStatus}</div>

            <div class="summary">
              <div><strong>Total Revenue:</strong> ₹${salesMetrics.totalRevenue.toLocaleString()}</div>
              <div><strong>Total Orders:</strong> ${salesMetrics.totalOrders}</div>
              <div><strong>Average Order Value:</strong> ₹${salesMetrics.avgOrderValue.toLocaleString()}</div>
            </div>

            <table>
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Revenue</th>
                  <th>Orders</th>
                </tr>
              </thead>
              <tbody>
                ${salesMetrics.chartData.map(item => `
                  <tr>
                    <td>${item.date}</td>
                    <td>₹${item.amount.toLocaleString()}</td>
                    <td>${Math.round(item.amount / salesMetrics.avgOrderValue)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <div class="footer">
              <p>Generated on ${new Date().toLocaleString()} | Sajawat Sarees Admin Dashboard</p>
            </div>
          </body>
        </html>
      `);

      printWindow.document.close();
      printWindow.focus();

      // Print after a short delay to ensure content is loaded
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        setIsPrinting(false);
      }, 500);

    } catch (error) {
      console.error("Error printing report:", error);
      alert("Failed to print report. Please try again.");
      setIsPrinting(false);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/analytics">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <LineChartIcon className="h-6 w-6 text-primary/80" />
              Sales Report
            </h1>
          </div>
          <p className="text-muted-foreground">
            Analyze your sales performance and trends.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handlePrintReport}
            disabled={isPrinting}
          >
            {isPrinting ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <PrinterIcon className="mr-2 h-4 w-4" />
            )}
            {isPrinting ? 'Printing...' : 'Print Report'}
          </Button>
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handleExportReport}
            disabled={isExporting}
          >
            {isExporting ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <DownloadIcon className="mr-2 h-4 w-4" />
            )}
            {isExporting ? 'Exporting...' : 'Export Report'}
          </Button>
        </div>
      </div>

      <div className="bg-card p-5 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </div>
            <select
              className="w-full h-10 pl-10 pr-4 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
            >
              <option value="last7">Last 7 Days</option>
              <option value="last30">Last 30 Days</option>
              <option value="last90">Last 90 Days</option>
              <option value="year">This Year</option>
              <option value="custom">Custom Range</option>
            </select>
          </div>

          <div className="relative flex-1">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <FilterIcon className="h-4 w-4 text-muted-foreground" />
            </div>
            <select
              className="w-full h-10 pl-10 pr-4 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">All Orders</option>
              <option value="Completed">Completed</option>
              <option value="Processing">Processing</option>
              <option value="Shipped">Shipped</option>
              <option value="Pending">Pending</option>
              <option value="Cancelled">Cancelled</option>
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <TrendingUpIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Total Revenue</h2>
          </div>
          <p className="text-3xl font-bold">₹{salesMetrics.totalRevenue.toLocaleString()}</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>12% from previous period</span>
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <ShoppingBagIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Total Orders</h2>
          </div>
          <p className="text-3xl font-bold">{salesMetrics.totalOrders}</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>8% from previous period</span>
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '150ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <CreditCardIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Avg. Order Value</h2>
          </div>
          <p className="text-3xl font-bold">₹{salesMetrics.avgOrderValue.toLocaleString()}</p>
          <p className="text-sm text-red-600 mt-2 flex items-center gap-1">
            <ArrowDownIcon className="h-3.5 w-3.5" />
            <span>3% from previous period</span>
          </p>
        </div>
      </div>

      <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '200ms' }}>
        <div className="flex items-center gap-2 mb-4">
          <div className="bg-primary/10 p-1.5 rounded-full">
            <LineChartIcon className="h-5 w-5 text-primary/80" />
          </div>
          <h2 className="text-xl font-semibold">Sales Trend</h2>
        </div>
        <div className="h-80 w-full p-4 rounded-lg border border-border/40 overflow-hidden relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-sm font-medium">Monthly Revenue</h3>
            <div className="flex items-center gap-2 text-xs">
              <span className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-primary/80"></div>
                Revenue
              </span>
              <span className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                Orders
              </span>
            </div>
          </div>
          <ResponsiveContainer width="100%" height="85%">
            <AreaChart
              data={[
                { name: 'Jan', revenue: 12000, orders: 20 },
                { name: 'Feb', revenue: 15000, orders: 25 },
                { name: 'Mar', revenue: 18000, orders: 30 },
                { name: 'Apr', revenue: 16000, orders: 28 },
                { name: 'May', revenue: 21000, orders: 35 },
                { name: 'Jun', revenue: 19000, orders: 32 },
                { name: 'Jul', revenue: 22000, orders: 38 },
                { name: 'Aug', revenue: 25000, orders: 42 },
                { name: 'Sep', revenue: 23000, orders: 40 },
                { name: 'Oct', revenue: 27000, orders: 45 },
                { name: 'Nov', revenue: 30000, orders: 50 },
                { name: 'Dec', revenue: 35000, orders: 60 },
              ]}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
              <XAxis dataKey="name" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  borderRadius: '8px',
                  border: '1px solid rgba(0, 0, 0, 0.1)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Legend />
              <Area
                type="monotone"
                dataKey="revenue"
                stroke="var(--chart-1)"
                fillOpacity={0.3}
                fill="url(#colorRevenue)"
                activeDot={{ r: 6 }}
              />
              <Area
                type="monotone"
                dataKey="orders"
                stroke="var(--chart-2)"
                fillOpacity={0.3}
                fill="url(#colorOrders)"
              />
              <defs>
                <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="var(--chart-1)" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="var(--chart-1)" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="colorOrders" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="var(--chart-2)" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="var(--chart-2)" stopOpacity={0}/>
                </linearGradient>
              </defs>
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '250ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <PieChartIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Sales by Payment Method</h2>
          </div>
          <div className="h-60 w-full p-4 rounded-lg border border-border/40 overflow-hidden relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={[
                    { name: 'Credit Card', value: 65 },
                    { name: 'UPI', value: 25 },
                    { name: 'Net Banking', value: 10 },
                  ]}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  <Cell fill="var(--chart-1)" />
                  <Cell fill="var(--chart-2)" />
                  <Cell fill="var(--chart-3)" />
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderRadius: '8px',
                    border: '1px solid rgba(0, 0, 0, 0.1)',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 space-y-3 p-3 bg-muted/20 rounded-lg">
            <div className="flex justify-between items-center p-2 hover:bg-muted/30 rounded-md transition-colors">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded-full bg-primary/80 shadow-sm"></div>
                <span className="font-medium">Credit Card</span>
              </div>
              <span className="font-medium">65%</span>
            </div>
            <div className="flex justify-between items-center p-2 hover:bg-muted/30 rounded-md transition-colors">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded-full bg-blue-500/80 shadow-sm"></div>
                <span className="font-medium">UPI</span>
              </div>
              <span className="font-medium">25%</span>
            </div>
            <div className="flex justify-between items-center p-2 hover:bg-muted/30 rounded-md transition-colors">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded-full bg-green-500/80 shadow-sm"></div>
                <span className="font-medium">Net Banking</span>
              </div>
              <span className="font-medium">10%</span>
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '300ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <ShoppingBagIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Top Selling Products</h2>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                  <PackageIcon className="h-5 w-5 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">Banarasi Silk Saree</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <ShoppingBagIcon className="h-3 w-3" />
                    15 sold
                  </p>
                </div>
              </div>
              <p className="font-medium">₹89,985</p>
            </div>

            <div className="flex justify-between items-center p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                  <PackageIcon className="h-5 w-5 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">Kanjivaram Silk Saree</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <ShoppingBagIcon className="h-3 w-3" />
                    8 sold
                  </p>
                </div>
              </div>
              <p className="font-medium">₹71,992</p>
            </div>

            <div className="flex justify-between items-center p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                  <PackageIcon className="h-5 w-5 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">Cotton Handloom Saree</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <ShoppingBagIcon className="h-3 w-3" />
                    25 sold
                  </p>
                </div>
              </div>
              <p className="font-medium">₹62,475</p>
            </div>

            <div className="flex justify-between items-center p-3 bg-muted/20 rounded-lg hover:bg-muted/30 transition-colors border border-border/40">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                  <PackageIcon className="h-5 w-5 text-primary/70" />
                </div>
                <div>
                  <p className="font-medium">Linen Saree</p>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <ShoppingBagIcon className="h-3 w-3" />
                    12 sold
                  </p>
                </div>
              </div>
              <p className="font-medium">₹41,988</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
