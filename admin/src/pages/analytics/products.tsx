import * as React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  ChevronLeftIcon,
  DownloadIcon,
  FilterIcon,
  SearchIcon,
  PackageIcon,
  BarChart3Icon,
  TrendingUpIcon,
  DollarSignIcon,
  AlertTriangleIcon,
  PieChartIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  BoxIcon,
  PrinterIcon,
  RefreshCwIcon
} from "lucide-react";
import { useAppSelector } from "@/hooks/use-redux";

export function ProductsReport() {
  const products = useAppSelector((state) => state.products.items);
  const [categoryFilter, setCategoryFilter] = React.useState("all");
  const [searchTerm, setSearchTerm] = React.useState("");
  const [isExporting, setIsExporting] = React.useState(false);
  const [isPrinting, setIsPrinting] = React.useState(false);

  // Filter products
  const filteredProducts = React.useMemo(() => {
    let result = [...products];

    // Apply category filter
    if (categoryFilter !== "all") {
      result = result.filter(product => product.category === categoryFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.category.toLowerCase().includes(searchLower)
      );
    }

    return result;
  }, [products, categoryFilter, searchTerm]);

  // Get unique categories
  const categories = React.useMemo(() => {
    const uniqueCategories = new Set(products.map(product => product.category));
    return Array.from(uniqueCategories);
  }, [products]);

  // Calculate product metrics
  const productMetrics = React.useMemo(() => {
    // Total stock value
    const totalStockValue = filteredProducts.reduce(
      (sum, product) => sum + (product.price * product.stock),
      0
    );

    // Average price
    const avgPrice = filteredProducts.length > 0
      ? Math.round(filteredProducts.reduce((sum, product) => sum + product.price, 0) / filteredProducts.length)
      : 0;

    // Low stock products
    const lowStockProducts = filteredProducts.filter(product => product.stock <= 5);

    // Products by category
    const productsByCategory = filteredProducts.reduce((acc, product) => {
      if (!acc[product.category]) {
        acc[product.category] = 0;
      }
      acc[product.category]++;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalProducts: filteredProducts.length,
      totalStockValue,
      avgPrice,
      lowStockCount: lowStockProducts.length,
      productsByCategory
    };
  }, [filteredProducts]);

  // Function to export the report as CSV
  const handleExportReport = () => {
    setIsExporting(true);

    try {
      // Create CSV content
      const headers = ["Product Name", "Category", "Price", "Stock", "Stock Value", "Status"];
      const csvContent = [
        headers.join(","),
        ...filteredProducts.map(product => {
          const stockStatus = product.stock > 10
            ? "In Stock"
            : product.stock > 0
              ? "Low Stock"
              : "Out of Stock";

          return `"${product.name}","${product.category}",${product.price},${product.stock},${product.price * product.stock},"${stockStatus}"`;
        })
      ].join("\n");

      // Create a blob and trigger download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `products_report_${categoryFilter}_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // In a real app, you might want to use a library like ExcelJS or jsPDF for better formatting
    } catch (error) {
      console.error("Error exporting report:", error);
      alert("Failed to export report. Please try again.");
    } finally {
      setTimeout(() => {
        setIsExporting(false);
      }, 1000);
    }
  };

  // Function to print the report
  const handlePrintReport = () => {
    setIsPrinting(true);

    try {
      // Create a print-friendly version
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error("Could not open print window. Please check your popup blocker settings.");
      }

      printWindow.document.write(`
        <html>
          <head>
            <title>Products Report - ${new Date().toLocaleDateString()}</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { color: #333; }
              table { width: 100%; border-collapse: collapse; margin-top: 20px; }
              th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
              th { background-color: #f2f2f2; }
              .summary { margin: 20px 0; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }
              .summary div { margin: 10px 0; }
              .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
              .status-in-stock { color: green; }
              .status-low-stock { color: orange; }
              .status-out-of-stock { color: red; }
            </style>
          </head>
          <body>
            <h1>Products Report</h1>
            <div>Category Filter: ${categoryFilter === "all" ? "All Categories" : categoryFilter}</div>

            <div class="summary">
              <div><strong>Total Products:</strong> ${productMetrics.totalProducts}</div>
              <div><strong>Total Stock Value:</strong> ₹${productMetrics.totalStockValue.toLocaleString()}</div>
              <div><strong>Average Price:</strong> ₹${productMetrics.avgPrice.toLocaleString()}</div>
              <div><strong>Low Stock Items:</strong> ${productMetrics.lowStockCount}</div>
            </div>

            <table>
              <thead>
                <tr>
                  <th>Product Name</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Stock</th>
                  <th>Stock Value</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                ${filteredProducts.map(product => {
                  const stockStatus = product.stock > 10
                    ? '<span class="status-in-stock">In Stock</span>'
                    : product.stock > 0
                      ? '<span class="status-low-stock">Low Stock</span>'
                      : '<span class="status-out-of-stock">Out of Stock</span>';

                  return `
                    <tr>
                      <td>${product.name}</td>
                      <td>${product.category}</td>
                      <td>₹${product.price.toLocaleString()}</td>
                      <td>${product.stock}</td>
                      <td>₹${(product.price * product.stock).toLocaleString()}</td>
                      <td>${stockStatus}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>

            <div class="footer">
              <p>Generated on ${new Date().toLocaleString()} | Sajawat Sarees Admin Dashboard</p>
            </div>
          </body>
        </html>
      `);

      printWindow.document.close();
      printWindow.focus();

      // Print after a short delay to ensure content is loaded
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        setIsPrinting(false);
      }, 500);

    } catch (error) {
      console.error("Error printing report:", error);
      alert("Failed to print report. Please try again.");
      setIsPrinting(false);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/analytics">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <PackageIcon className="h-6 w-6 text-primary/80" />
              Products Report
            </h1>
          </div>
          <p className="text-muted-foreground">
            Analyze your product performance and inventory.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handlePrintReport}
            disabled={isPrinting}
          >
            {isPrinting ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <PrinterIcon className="mr-2 h-4 w-4" />
            )}
            {isPrinting ? 'Printing...' : 'Print Report'}
          </Button>
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handleExportReport}
            disabled={isExporting}
          >
            {isExporting ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <DownloadIcon className="mr-2 h-4 w-4" />
            )}
            {isExporting ? 'Exporting...' : 'Export Report'}
          </Button>
        </div>
      </div>

      <div className="bg-card p-5 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search products..."
              className="w-full pl-10 transition-all duration-200 focus:border-primary/30"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="relative min-w-[180px]">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <FilterIcon className="h-4 w-4 text-muted-foreground" />
            </div>
            <select
              className="w-full h-10 pl-10 pr-4 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <PackageIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Total Products</h2>
          </div>
          <p className="text-3xl font-bold">{productMetrics.totalProducts}</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>5% from last month</span>
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <DollarSignIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Total Stock Value</h2>
          </div>
          <p className="text-3xl font-bold">₹{productMetrics.totalStockValue.toLocaleString()}</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>8% from last month</span>
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '150ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <TrendingUpIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Average Price</h2>
          </div>
          <p className="text-3xl font-bold">₹{productMetrics.avgPrice.toLocaleString()}</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>2% from last month</span>
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '200ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <AlertTriangleIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Low Stock Items</h2>
          </div>
          <p className="text-3xl font-bold">{productMetrics.lowStockCount}</p>
          <p className="text-sm text-red-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>3 more than last month</span>
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '250ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <PieChartIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Products by Category</h2>
          </div>
          <div className="h-60 w-full">
            {/* This would be a chart in a real application */}
            <div className="h-full w-full bg-muted/30 rounded-lg border border-border/40 overflow-hidden relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="text-center flex flex-col items-center justify-center h-full">
                <PieChartIcon className="h-12 w-12 text-primary/30 mb-2 animate-pulse-slow" />
                <p className="text-muted-foreground font-medium">Pie Chart Placeholder</p>
                <p className="text-sm text-muted-foreground mt-1 max-w-md">
                  In a real application, this would be a pie chart showing products by category.
                </p>

                {/* Fake pie chart for visual appeal */}
                <div className="relative w-32 h-32 mt-4">
                  <div className="absolute inset-0 rounded-full bg-primary/60 animate-pulse-slow"></div>
                  <div className="absolute inset-0 rounded-full border-8 border-blue-500/60" style={{ clipPath: 'polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%)' }}></div>
                  <div className="absolute inset-0 rounded-full border-8 border-green-500/60" style={{ clipPath: 'polygon(50% 0%, 0% 0%, 0% 30%, 50% 30%)' }}></div>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-4 space-y-2 p-3 bg-muted/20 rounded-lg">
            {Object.entries(productMetrics.productsByCategory).map(([category, count], index) => (
              <div key={category} className="flex justify-between items-center p-2 hover:bg-muted/30 rounded-md transition-colors">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded-full bg-primary/80 shadow-sm" style={{
                    backgroundColor: index % 3 === 0 ? 'rgba(var(--primary), 0.8)' :
                                     index % 3 === 1 ? 'rgba(59, 130, 246, 0.8)' :
                                     'rgba(34, 197, 94, 0.8)'
                  }}></div>
                  <span className="font-medium">{category}</span>
                </div>
                <span className="font-medium">{count} products</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '300ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <BarChart3Icon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Stock Levels</h2>
          </div>
          <div className="h-60 w-full">
            {/* This would be a chart in a real application */}
            <div className="h-full w-full bg-muted/30 rounded-lg border border-border/40 overflow-hidden relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="text-center flex flex-col items-center justify-center h-full">
                <BarChart3Icon className="h-12 w-12 text-primary/30 mb-2 animate-pulse-slow" />
                <p className="text-muted-foreground font-medium">Bar Chart Placeholder</p>
                <p className="text-sm text-muted-foreground mt-1 max-w-md">
                  In a real application, this would be a bar chart showing stock levels by product.
                </p>

                {/* Fake bar chart for visual appeal */}
                <div className="flex items-end gap-1 mt-6 h-24">
                  {[35, 65, 40, 80, 25, 55, 70, 45].map((height, index) => (
                    <div
                      key={index}
                      className="w-6 bg-primary/40 rounded-t-sm transition-all duration-300 hover:bg-primary/60"
                      style={{
                        height: `${height}%`,
                        animationDelay: `${index * 100}ms`,
                        animation: 'slide-up 0.5s ease-out forwards',
                        opacity: 0.7
                      }}
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '350ms' }}>
        <div className="flex items-center gap-2 mb-4">
          <div className="bg-primary/10 p-1.5 rounded-full">
            <BoxIcon className="h-5 w-5 text-primary/80" />
          </div>
          <h2 className="text-xl font-semibold">Product Performance</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Product</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Category</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Price</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Stock</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Stock Value</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border/60">
              {filteredProducts.slice(0, 5).map((product, index) => (
                <tr
                  key={product.id}
                  className="bg-card hover:bg-muted/30 transition-colors"
                  style={{
                    animationDelay: `${index * 50 + 400}ms`,
                    animation: 'fade-in 0.5s ease-out forwards',
                    opacity: 0
                  }}
                >
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <div className="bg-primary/5 p-1 rounded">
                        <PackageIcon className="h-4 w-4 text-primary/70" />
                      </div>
                      <span className="font-medium">{product.name}</span>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="px-2.5 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary/80">
                      {product.category}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap font-medium">₹{product.price.toLocaleString()}</td>
                  <td className="px-4 py-4 whitespace-nowrap font-medium">{product.stock}</td>
                  <td className="px-4 py-4 whitespace-nowrap font-medium">₹{(product.price * product.stock).toLocaleString()}</td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium shadow-sm ${
                      product.stock > 10
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-800 shadow-green-800/5"
                        : product.stock > 0
                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800 shadow-yellow-800/5"
                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 border border-red-200 dark:border-red-800 shadow-red-800/5"
                    }`}>
                      <span className={`h-1.5 w-1.5 rounded-full ${
                        product.stock > 10
                          ? "bg-green-500"
                          : product.stock > 0
                            ? "bg-yellow-500 animate-pulse"
                            : "bg-red-500"
                      }`}></span>
                      {product.stock > 10
                        ? "In Stock"
                        : product.stock > 0
                          ? "Low Stock"
                          : "Out of Stock"}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-6 flex justify-center">
          <Button asChild variant="outline" className="hover:bg-primary/5 transition-colors">
            <Link to="/products/inventory">
              <PackageIcon className="mr-2 h-4 w-4" />
              View All Products
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
