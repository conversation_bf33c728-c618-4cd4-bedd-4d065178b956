import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  ChevronLeftIcon,
  DownloadIcon,
  SearchIcon,
  CalendarIcon,
  UsersIcon,
  TrendingUpIcon,
  DollarSignIcon,
  LineChartIcon,
  BarChart3Icon,
  UserIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  UserPlusIcon,
  RefreshCwIcon,
  UserMinusIcon,
  PackageIcon, // Using PackageIcon instead of ShoppingBagIcon
  PrinterIcon
} from "lucide-react";
import { useAppSelector } from "@/hooks/use-redux";

export function CustomersReport() {
  const customers = useAppSelector((state) => state.customers.items);
  const [dateRange, setDateRange] = React.useState("last30");
  const [searchTerm, setSearchTerm] = React.useState("");
  const [isExporting, setIsExporting] = React.useState(false);
  const [isPrinting, setIsPrinting] = React.useState(false);

  // Filter customers
  const filteredCustomers = React.useMemo(() => {
    let result = [...customers];

    // Apply date range filter (simulated since we don't have actual join dates for all customers)
    // In a real app, you would filter based on actual customer join dates
    const today = new Date();
    const getDateLimit = () => {
      switch (dateRange) {
        case "last30":
          const thirtyDaysAgo = new Date(today);
          thirtyDaysAgo.setDate(today.getDate() - 30);
          return thirtyDaysAgo;
        case "last90":
          const ninetyDaysAgo = new Date(today);
          ninetyDaysAgo.setDate(today.getDate() - 90);
          return ninetyDaysAgo;
        case "last180":
          const oneEightyDaysAgo = new Date(today);
          oneEightyDaysAgo.setDate(today.getDate() - 180);
          return oneEightyDaysAgo;
        case "year":
          return new Date(today.getFullYear(), 0, 1); // January 1st of current year
        default:
          return new Date(0); // Beginning of time
      }
    };

    // For demo purposes, we'll just filter a percentage of customers based on date range
    // In a real app, you would filter based on actual customer join dates
    if (dateRange !== "all") {
      const filterPercentage = dateRange === "last30" ? 0.3 :
                              dateRange === "last90" ? 0.5 :
                              dateRange === "last180" ? 0.7 :
                              dateRange === "year" ? 0.9 : 1;

      const cutoffIndex = Math.floor(result.length * filterPercentage);
      result = result.slice(0, cutoffIndex);
    }

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter(customer =>
        customer.name.toLowerCase().includes(searchLower) ||
        customer.email.toLowerCase().includes(searchLower) ||
        customer.phone.toLowerCase().includes(searchLower)
      );
    }

    return result;
  }, [customers, searchTerm, dateRange]);

  // Calculate customer metrics
  const customerMetrics = React.useMemo(() => {
    // Total customers
    const totalCustomers = filteredCustomers.length;

    // Total revenue
    const totalRevenue = filteredCustomers.reduce(
      (sum, customer) => sum + customer.totalSpent,
      0
    );

    // Average order value
    const totalOrders = filteredCustomers.reduce(
      (sum, customer) => sum + customer.orders,
      0
    );

    const avgOrderValue = totalOrders > 0
      ? Math.round(totalRevenue / totalOrders)
      : 0;

    // Average revenue per customer
    const avgRevenuePerCustomer = totalCustomers > 0
      ? Math.round(totalRevenue / totalCustomers)
      : 0;

    return {
      totalCustomers,
      totalRevenue,
      totalOrders,
      avgOrderValue,
      avgRevenuePerCustomer
    };
  }, [filteredCustomers]);

  // Function to export the report as CSV
  const handleExportReport = () => {
    setIsExporting(true);

    try {
      // Create CSV content
      const headers = ["Name", "Email", "Phone", "Orders", "Total Spent", "Avg. Order Value"];
      const csvContent = [
        headers.join(","),
        ...filteredCustomers.map(customer => {
          const avgOrderValue = customer.orders > 0
            ? Math.round(customer.totalSpent / customer.orders)
            : 0;

          return `"${customer.name}","${customer.email}","${customer.phone}",${customer.orders},${customer.totalSpent},${avgOrderValue}`;
        })
      ].join("\n");

      // Create a blob and trigger download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `customers_report_${dateRange}_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // In a real app, you might want to use a library like ExcelJS or jsPDF for better formatting
    } catch (error) {
      console.error("Error exporting report:", error);
      alert("Failed to export report. Please try again.");
    } finally {
      setTimeout(() => {
        setIsExporting(false);
      }, 1000);
    }
  };

  // Function to print the report
  const handlePrintReport = () => {
    setIsPrinting(true);

    try {
      // Create a print-friendly version
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error("Could not open print window. Please check your popup blocker settings.");
      }

      printWindow.document.write(`
        <html>
          <head>
            <title>Customers Report - ${new Date().toLocaleDateString()}</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { color: #333; }
              table { width: 100%; border-collapse: collapse; margin-top: 20px; }
              th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
              th { background-color: #f2f2f2; }
              .summary { margin: 20px 0; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }
              .summary div { margin: 10px 0; }
              .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
            </style>
          </head>
          <body>
            <h1>Customers Report</h1>
            <div>Date Range: ${dateRange === "last30" ? "Last 30 Days" :
                              dateRange === "last90" ? "Last 90 Days" :
                              dateRange === "last180" ? "Last 180 Days" :
                              dateRange === "year" ? "This Year" : "All Time"}</div>

            <div class="summary">
              <div><strong>Total Customers:</strong> ${customerMetrics.totalCustomers}</div>
              <div><strong>Total Revenue:</strong> ₹${customerMetrics.totalRevenue.toLocaleString()}</div>
              <div><strong>Total Orders:</strong> ${customerMetrics.totalOrders}</div>
              <div><strong>Average Order Value:</strong> ₹${customerMetrics.avgOrderValue.toLocaleString()}</div>
              <div><strong>Average Revenue Per Customer:</strong> ₹${customerMetrics.avgRevenuePerCustomer.toLocaleString()}</div>
            </div>

            <table>
              <thead>
                <tr>
                  <th>Customer Name</th>
                  <th>Email</th>
                  <th>Phone</th>
                  <th>Orders</th>
                  <th>Total Spent</th>
                  <th>Avg. Order Value</th>
                </tr>
              </thead>
              <tbody>
                ${filteredCustomers.map(customer => {
                  const avgOrderValue = customer.orders > 0
                    ? Math.round(customer.totalSpent / customer.orders)
                    : 0;

                  return `
                    <tr>
                      <td>${customer.name}</td>
                      <td>${customer.email}</td>
                      <td>${customer.phone}</td>
                      <td>${customer.orders}</td>
                      <td>₹${customer.totalSpent.toLocaleString()}</td>
                      <td>₹${avgOrderValue.toLocaleString()}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>

            <div class="footer">
              <p>Generated on ${new Date().toLocaleString()} | Sajawat Sarees Admin Dashboard</p>
            </div>
          </body>
        </html>
      `);

      printWindow.document.close();
      printWindow.focus();

      // Print after a short delay to ensure content is loaded
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        setIsPrinting(false);
      }, 500);

    } catch (error) {
      console.error("Error printing report:", error);
      alert("Failed to print report. Please try again.");
      setIsPrinting(false);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 animate-slide-up">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-primary/5 transition-colors"
              asChild
            >
              <Link to="/analytics">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <UsersIcon className="h-6 w-6 text-primary/80" />
              Customers Report
            </h1>
          </div>
          <p className="text-muted-foreground">
            Analyze your customer base and purchasing behavior.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handlePrintReport}
            disabled={isPrinting}
          >
            {isPrinting ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <PrinterIcon className="mr-2 h-4 w-4" />
            )}
            {isPrinting ? 'Printing...' : 'Print Report'}
          </Button>
          <Button
            variant="outline"
            className="hover:bg-primary/5 transition-colors"
            onClick={handleExportReport}
            disabled={isExporting}
          >
            {isExporting ? (
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <DownloadIcon className="mr-2 h-4 w-4" />
            )}
            {isExporting ? 'Exporting...' : 'Export Report'}
          </Button>
        </div>
      </div>

      <div className="bg-card p-5 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search customers..."
              className="w-full pl-10 transition-all duration-200 focus:border-primary/30"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="relative min-w-[180px]">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </div>
            <select
              className="w-full h-10 pl-10 pr-4 rounded-md border border-input bg-transparent text-sm shadow-xs outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] appearance-none transition-all duration-200"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
            >
              <option value="last30">Last 30 Days</option>
              <option value="last90">Last 90 Days</option>
              <option value="last180">Last 180 Days</option>
              <option value="year">This Year</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <UsersIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Total Customers</h2>
          </div>
          <p className="text-3xl font-bold">{customerMetrics.totalCustomers}</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>15% from last month</span>
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <DollarSignIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Total Revenue</h2>
          </div>
          <p className="text-3xl font-bold">₹{customerMetrics.totalRevenue.toLocaleString()}</p>
          <p className="text-sm text-green-600 mt-2 flex items-center gap-1">
            <ArrowUpIcon className="h-3.5 w-3.5" />
            <span>12% from last month</span>
          </p>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '150ms' }}>
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <TrendingUpIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-lg font-medium">Avg. Revenue Per Customer</h2>
          </div>
          <p className="text-3xl font-bold">₹{customerMetrics.avgRevenuePerCustomer.toLocaleString()}</p>
          <p className="text-sm text-red-600 mt-2 flex items-center gap-1">
            <ArrowDownIcon className="h-3.5 w-3.5" />
            <span>3% from last month</span>
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '200ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <LineChartIcon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Customer Growth</h2>
          </div>
          <div className="h-60 w-full">
            {/* This would be a chart in a real application */}
            <div className="h-full w-full bg-muted/30 rounded-lg border border-border/40 overflow-hidden relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="text-center flex flex-col items-center justify-center h-full">
                <LineChartIcon className="h-12 w-12 text-primary/30 mb-2 animate-pulse-slow" />
                <p className="text-muted-foreground font-medium">Line Chart Placeholder</p>
                <p className="text-sm text-muted-foreground mt-1 max-w-md">
                  In a real application, this would be a line chart showing customer growth over time.
                </p>

                {/* Fake line chart for visual appeal */}
                <div className="relative w-full max-w-xs h-24 mt-4 flex items-end">
                  <svg className="w-full h-full" viewBox="0 0 300 100" preserveAspectRatio="none">
                    <path
                      d="M0,80 C20,70 40,90 60,75 C80,60 100,80 120,70 C140,60 160,50 180,40 C200,30 220,35 240,25 C260,15 280,20 300,10"
                      fill="none"
                      stroke="rgba(var(--primary), 0.5)"
                      strokeWidth="3"
                      strokeLinecap="round"
                      className="animate-draw-line"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '250ms' }}>
          <div className="flex items-center gap-2 mb-4">
            <div className="bg-primary/10 p-1.5 rounded-full">
              <BarChart3Icon className="h-5 w-5 text-primary/80" />
            </div>
            <h2 className="text-xl font-semibold">Customer Retention</h2>
          </div>
          <div className="h-60 w-full">
            {/* This would be a chart in a real application */}
            <div className="h-full w-full bg-muted/30 rounded-lg border border-border/40 overflow-hidden relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="text-center flex flex-col items-center justify-center h-full">
                <BarChart3Icon className="h-12 w-12 text-primary/30 mb-2 animate-pulse-slow" />
                <p className="text-muted-foreground font-medium">Bar Chart Placeholder</p>
                <p className="text-sm text-muted-foreground mt-1 max-w-md">
                  In a real application, this would be a bar chart showing customer retention rates.
                </p>

                {/* Fake bar chart for visual appeal */}
                <div className="flex items-end gap-1 mt-6 h-24">
                  {[75, 85, 65, 90, 80, 70, 85, 95].map((height, index) => (
                    <div
                      key={index}
                      className="w-6 bg-primary/40 rounded-t-sm transition-all duration-300 hover:bg-primary/60"
                      style={{
                        height: `${height}%`,
                        animationDelay: `${index * 100}ms`,
                        animation: 'slide-up 0.5s ease-out forwards',
                        opacity: 0.7
                      }}
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '300ms' }}>
        <div className="flex items-center gap-2 mb-4">
          <div className="bg-primary/10 p-1.5 rounded-full">
            <UserIcon className="h-5 w-5 text-primary/80" />
          </div>
          <h2 className="text-xl font-semibold">Top Customers</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Customer</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Email</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Orders</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Total Spent</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Avg. Order Value</th>
                <th className="px-4 py-3.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border/60">
              {filteredCustomers
                .sort((a, b) => b.totalSpent - a.totalSpent)
                .slice(0, 5)
                .map((customer, index) => (
                  <tr
                    key={customer.id}
                    className="bg-card hover:bg-muted/30 transition-colors"
                    style={{
                      animationDelay: `${index * 50 + 350}ms`,
                      animation: 'fade-in 0.5s ease-out forwards',
                      opacity: 0
                    }}
                  >
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <div className="bg-primary/5 p-1.5 rounded-full">
                          <UserIcon className="h-4 w-4 text-primary/70" />
                        </div>
                        <span className="font-medium">{customer.name}</span>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-muted-foreground">{customer.email}</td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary/80">
                        <PackageIcon className="h-3 w-3" />
                        {customer.orders}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap font-medium">₹{customer.totalSpent.toLocaleString()}</td>
                    <td className="px-4 py-4 whitespace-nowrap font-medium">
                      ₹{customer.orders > 0
                        ? Math.round(customer.totalSpent / customer.orders).toLocaleString()
                        : 0}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <Button variant="outline" size="sm" asChild className="h-8 px-3 text-xs hover:bg-primary/5 transition-colors">
                        <Link to={`/customers/${customer.id}`}>
                          <UserIcon className="mr-1.5 h-3.5 w-3.5" />
                          View Profile
                        </Link>
                      </Button>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        <div className="mt-6 flex justify-center">
          <Button asChild variant="outline" className="hover:bg-primary/5 transition-colors">
            <Link to="/customers">
              <UsersIcon className="mr-2 h-4 w-4" />
              View All Customers
            </Link>
          </Button>
        </div>
      </div>

      <div className="bg-card p-6 rounded-lg shadow-sm border border-border/40 transition-all duration-200 hover:shadow-md animate-slide-up" style={{ animationDelay: '350ms' }}>
        <div className="flex items-center gap-2 mb-4">
          <div className="bg-primary/10 p-1.5 rounded-full">
            <UsersIcon className="h-5 w-5 text-primary/80" />
          </div>
          <h2 className="text-xl font-semibold">Customer Segments</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-muted/20 p-5 rounded-lg border border-border/40 hover:bg-muted/30 transition-all duration-200 hover:shadow-sm animate-slide-up" style={{ animationDelay: '400ms' }}>
            <div className="flex items-center gap-2 mb-2">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <UserPlusIcon className="h-4 w-4 text-primary/80" />
              </div>
              <h3 className="font-medium">New Customers</h3>
            </div>
            <p className="text-2xl font-bold">32</p>
            <p className="text-sm text-muted-foreground flex items-center gap-1.5 mt-1">
              <CalendarIcon className="h-3.5 w-3.5" />
              Last 30 days
            </p>
            <div className="mt-2 w-full bg-muted/30 h-1.5 rounded-full overflow-hidden">
              <div className="bg-primary/60 h-full rounded-full" style={{ width: '32%' }}></div>
            </div>
          </div>

          <div className="bg-muted/20 p-5 rounded-lg border border-border/40 hover:bg-muted/30 transition-all duration-200 hover:shadow-sm animate-slide-up" style={{ animationDelay: '450ms' }}>
            <div className="flex items-center gap-2 mb-2">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <RefreshCwIcon className="h-4 w-4 text-primary/80" />
              </div>
              <h3 className="font-medium">Returning Customers</h3>
            </div>
            <p className="text-2xl font-bold">48</p>
            <p className="text-sm text-muted-foreground flex items-center gap-1.5 mt-1">
              <CalendarIcon className="h-3.5 w-3.5" />
              Last 30 days
            </p>
            <div className="mt-2 w-full bg-muted/30 h-1.5 rounded-full overflow-hidden">
              <div className="bg-primary/60 h-full rounded-full" style={{ width: '48%' }}></div>
            </div>
          </div>

          <div className="bg-muted/20 p-5 rounded-lg border border-border/40 hover:bg-muted/30 transition-all duration-200 hover:shadow-sm animate-slide-up" style={{ animationDelay: '500ms' }}>
            <div className="flex items-center gap-2 mb-2">
              <div className="bg-primary/10 p-1.5 rounded-full">
                <UserMinusIcon className="h-4 w-4 text-primary/80" />
              </div>
              <h3 className="font-medium">Inactive Customers</h3>
            </div>
            <p className="text-2xl font-bold">15</p>
            <p className="text-sm text-muted-foreground flex items-center gap-1.5 mt-1">
              <CalendarIcon className="h-3.5 w-3.5" />
              No purchase in 90+ days
            </p>
            <div className="mt-2 w-full bg-muted/30 h-1.5 rounded-full overflow-hidden">
              <div className="bg-primary/60 h-full rounded-full" style={{ width: '15%' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
