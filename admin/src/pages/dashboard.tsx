import * as React from "react";
import { useAppSelector, useAppDispatch } from "@/hooks/use-redux";
import { fetchOrders } from "@/store/slices/ordersSlice";
import { fetchCustomers } from "@/store/slices/customersSlice";
import { fetchProducts } from "@/store/slices/productsSlice";
import { Link } from "react-router-dom";
import {
  ArrowUpIcon,
  ArrowDownIcon,
  PackageIcon,
  ShoppingCartIcon,
  UsersIcon,
  TrendingUpIcon,
  CalendarIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  TruckIcon,
  DollarSignIcon,
  RefreshCwIcon,
  EyeIcon,
  PieChartIcon,
  BarChartIcon,
  LineChartIcon,
  LayoutDashboardIcon,
  BadgeIndianRupeeIcon,
  SparklesIcon,
  DownloadIcon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';
import { motion } from "framer-motion";
import CountUp from "react-countup";
import { cn } from "@/lib/utils";

export function Dashboard() {
  const dispatch = useAppDispatch();
  const products = useAppSelector((state) => state.products.items);
  const orders = useAppSelector((state) => state.orders.items);
  const customers = useAppSelector((state) => state.customers.items);
  const [isLoading, setIsLoading] = React.useState(false);

  // Debug data
  React.useEffect(() => {
    console.log('Dashboard: Redux state data:', {
      orders: orders?.length || 0,
      products: products?.length || 0,
      customers: customers?.length || 0,
      ordersData: orders,
      productsData: products,
      customersData: customers
    });
  }, [orders, products, customers]);
  const [timeRange, setTimeRange] = React.useState("week");
  const [chartType, setChartType] = React.useState<'area' | 'bar' | 'line' | 'pie'>('area');
  const [isGeneratingReport, setIsGeneratingReport] = React.useState(false);

  // Fetch data on component mount
  React.useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        console.log('Dashboard: Starting to fetch data...');

        // Fetch all data without filters to get complete counts
        const results = await Promise.all([
          dispatch(fetchOrders({ page: 1, limit: 1000 })), // Fetch more orders for accurate count
          dispatch(fetchCustomers({ page: 1, limit: 1000 })), // Fetch more customers for accurate count
          dispatch(fetchProducts({ page: 1, limit: 1000 })) // Fetch more products for accurate count
        ]);

        console.log('Dashboard: Fetch results:', results);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [dispatch]);

  // Calculate total revenue
  const totalRevenue = React.useMemo(() => {
    if (!orders || !Array.isArray(orders)) {
      console.log('Dashboard: No orders data for revenue calculation');
      return 0;
    }
    const revenue = orders.reduce((sum, order) => sum + (order.totals?.total || 0), 0);
    console.log('Dashboard: Total revenue calculated:', revenue, 'from', orders.length, 'orders');
    return revenue;
  }, [orders]);

  // Get recent orders (last 5)
  const recentOrders = React.useMemo(() => {
    if (!orders || !Array.isArray(orders)) return [];
    return [...orders]
      .sort((a, b) => new Date(b.placedAt || b.createdAt).getTime() - new Date(a.placedAt || a.createdAt).getTime())
      .slice(0, 5);
  }, [orders]);

  // Calculate order statistics
  const orderStats = React.useMemo(() => {
    if (!orders || !Array.isArray(orders)) {
      return { pending: 0, processing: 0, shipped: 0, completed: 0 };
    }

    const pending = orders.filter(order => order.status?.toLowerCase() === "pending").length;
    const processing = orders.filter(order => order.status?.toLowerCase() === "processing").length;
    const shipped = orders.filter(order => order.status?.toLowerCase() === "shipped").length;
    const completed = orders.filter(order => order.status?.toLowerCase() === "delivered").length;

    console.log('Order Stats Debug:', {
      totalOrders: orders.length,
      pending,
      processing,
      shipped,
      completed,
      orderStatuses: orders.map(o => o.status)
    });

    return { pending, processing, shipped, completed };
  }, [orders]);

  // Filter data based on selected time range
  const filteredSalesData = React.useMemo(() => {
    // Sample data - in a real app, this would come from your API
    const allData = [
      { name: 'Jan', sales: 4000, revenue: 240000, profit: 100000 },
      { name: 'Feb', sales: 3000, revenue: 198000, profit: 80000 },
      { name: 'Mar', sales: 5000, revenue: 280000, profit: 120000 },
      { name: 'Apr', sales: 2780, revenue: 190000, profit: 78000 },
      { name: 'May', sales: 1890, revenue: 140000, profit: 60000 },
      { name: 'Jun', sales: 2390, revenue: 178000, profit: 70000 },
      { name: 'Jul', sales: 3490, revenue: 220000, profit: 90000 },
    ];

    // Filter based on time range
    switch (timeRange) {
      case 'day':
        return allData.slice(-1);
      case 'week':
        return allData.slice(-4);
      case 'month':
        return allData.slice(-6);
      case 'year':
        return allData;
      default:
        return allData;
    }
  }, [timeRange]);

  // Pie chart data
  const pieChartData = React.useMemo(() => {
    return [
      { name: 'Silk', value: 400 },
      { name: 'Cotton', value: 300 },
      { name: 'Linen', value: 200 },
      { name: 'Georgette', value: 150 },
      { name: 'Chiffon', value: 100 },
    ];
  }, []);

  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe'];

  // Refresh data function
  const refreshData = async () => {
    try {
      setIsLoading(true);
      console.log('Dashboard: Refreshing data...');
      await Promise.all([
        dispatch(fetchOrders({ page: 1, limit: 1000 })),
        dispatch(fetchCustomers({ page: 1, limit: 1000 })),
        dispatch(fetchProducts({ page: 1, limit: 1000 }))
      ]);
    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate report function
  const generateReport = () => {
    setIsGeneratingReport(true);

    // Simulate report generation
    setTimeout(() => {
      // Create a simple CSV report
      const reportData = [
        ['Date', 'Orders', 'Revenue', 'Products', 'Customers'],
        [new Date().toLocaleDateString(), orders?.length || 0, totalRevenue, products?.length || 0, customers?.length || 0]
      ];

      const csvContent = reportData.map(row => row.join(',')).join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `sajawat_report_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      setIsGeneratingReport(false);
    }, 1500);
  };

  // Get current date
  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="w-full max-w-[1600px] mx-auto">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 p-6 rounded-2xl shadow-sm border border-primary/10 mb-8"
      >
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <LayoutDashboardIcon className="h-8 w-8 text-primary" />
              <span>Welcome to Sajawat Sarees Dashboard</span>
            </h1>
            <p className="text-muted-foreground mt-2 flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              <span>{currentDate}</span>
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshData}
              disabled={isLoading}
              className="flex items-center gap-1 hover:bg-primary/10 transition-colors"
            >
              <RefreshCwIcon className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </Button>
            <Button
              onClick={generateReport}
              disabled={isGeneratingReport}
              className="flex items-center gap-1 bg-primary hover:bg-primary/90 transition-colors"
            >
              {isGeneratingReport ? (
                <RefreshCwIcon className="h-4 w-4 animate-spin" />
              ) : (
                <DownloadIcon className="h-4 w-4" />
              )}
              <span>{isGeneratingReport ? 'Generating...' : 'Generate Report'}</span>
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Time Range Selector */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="flex justify-end mb-6"
      >
        <div className="bg-card rounded-lg shadow-sm border border-border p-1 flex">
          {["day", "week", "month", "year"].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={cn(
                "px-3 py-1.5 text-sm font-medium rounded-md transition-colors",
                timeRange === range
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:bg-muted"
              )}
            >
              {range.charAt(0).toUpperCase() + range.slice(1)}
            </button>
          ))}
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-gradient-to-br from-primary/5 to-primary/10 p-6 rounded-xl shadow-sm border border-primary/10 hover:shadow-md transition-all duration-200 transform hover:-translate-y-1 group"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-1">Total Orders</p>
              <h2 className="text-3xl font-bold">
                <CountUp end={orders?.length || 0} duration={2} />
              </h2>
            </div>
            <div className="bg-primary/20 p-3 rounded-xl group-hover:bg-primary/30 transition-colors">
              <ShoppingCartIcon className="h-6 w-6 text-primary" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-green-600">
            <ArrowUpIcon className="h-4 w-4 mr-1" />
            <span>8% from last month</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-xl shadow-sm border border-green-200 dark:border-green-800/30 hover:shadow-md transition-all duration-200 transform hover:-translate-y-1 group"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-1">Total Revenue</p>
              <h2 className="text-3xl font-bold flex items-center">
                <BadgeIndianRupeeIcon className="h-5 w-5 mr-1" />
                <CountUp end={totalRevenue} duration={2} separator="," />
              </h2>
            </div>
            <div className="bg-green-200 dark:bg-green-800/30 p-3 rounded-xl group-hover:bg-green-300 dark:group-hover:bg-green-700/40 transition-colors">
              <DollarSignIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-green-600 dark:text-green-400">
            <ArrowUpIcon className="h-4 w-4 mr-1" />
            <span>12% from last month</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-xl shadow-sm border border-blue-200 dark:border-blue-800/30 hover:shadow-md transition-all duration-200 transform hover:-translate-y-1 group"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-1">Total Products</p>
              <h2 className="text-3xl font-bold">
                <CountUp end={products?.length || 0} duration={2} />
              </h2>
            </div>
            <div className="bg-blue-200 dark:bg-blue-800/30 p-3 rounded-xl group-hover:bg-blue-300 dark:group-hover:bg-blue-700/40 transition-colors">
              <PackageIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-red-600 dark:text-red-400">
            <ArrowDownIcon className="h-4 w-4 mr-1" />
            <span>3% from last month</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-xl shadow-sm border border-purple-200 dark:border-purple-800/30 hover:shadow-md transition-all duration-200 transform hover:-translate-y-1 group"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-1">Total Customers</p>
              <h2 className="text-3xl font-bold">
                <CountUp end={customers?.length || 0} duration={2} />
              </h2>
            </div>
            <div className="bg-purple-200 dark:bg-purple-800/30 p-3 rounded-xl group-hover:bg-purple-300 dark:group-hover:bg-purple-700/40 transition-colors">
              <UsersIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-green-600 dark:text-green-400">
            <ArrowUpIcon className="h-4 w-4 mr-1" />
            <span>15% from last month</span>
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.6 }}
        className="mb-8 bg-card rounded-xl shadow-sm border border-border p-6"
      >
        <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
          <SparklesIcon className="h-5 w-5 text-primary" />
          Quick Actions
        </h2>
        <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-4">
          <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center gap-2 hover:bg-primary/5">
            <Link to="/products/add">
              <PackageIcon className="h-6 w-6 text-primary" />
              <span>Add Product</span>
            </Link>
          </Button>
          <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20">
            <Link to="/products/bulk-upload">
              <DownloadIcon className="h-6 w-6 text-blue-600" />
              <span>Bulk Upload</span>
            </Link>
          </Button>
          <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center gap-2 hover:bg-purple-50 dark:hover:bg-purple-900/20">
            <Link to="/products/categories">
              <PieChartIcon className="h-6 w-6 text-purple-600" />
              <span>Categories</span>
            </Link>
          </Button>
          <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center gap-2 hover:bg-green-50 dark:hover:bg-green-900/20">
            <Link to="/orders">
              <ShoppingCartIcon className="h-6 w-6 text-green-600" />
              <span>Orders</span>
            </Link>
          </Button>
          <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center gap-2 hover:bg-orange-50 dark:hover:bg-orange-900/20">
            <Link to="/customers">
              <UsersIcon className="h-6 w-6 text-orange-600" />
              <span>Customers</span>
            </Link>
          </Button>
          <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center gap-2 hover:bg-indigo-50 dark:hover:bg-indigo-900/20">
            <Link to="/analytics">
              <TrendingUpIcon className="h-6 w-6 text-indigo-600" />
              <span>Analytics</span>
            </Link>
          </Button>
        </div>
      </motion.div>

      {/* Product Management Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.7 }}
        className="mb-8 bg-card rounded-xl shadow-sm border border-border p-6"
      >
        <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
          <PackageIcon className="h-5 w-5 text-primary" />
          Product Management
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gradient-to-br from-primary/5 to-primary/10 p-4 rounded-lg border border-primary/20">
            <h3 className="font-semibold text-primary mb-2">Manage Products</h3>
            <p className="text-sm text-muted-foreground mb-3">Add, edit, and organize your product catalog</p>
            <div className="flex gap-2">
              <Button asChild size="sm" variant="outline">
                <Link to="/products">View All</Link>
              </Button>
              <Button asChild size="sm">
                <Link to="/products/add">Add New</Link>
              </Button>
            </div>
          </div>
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800/30">
            <h3 className="font-semibold text-blue-600 dark:text-blue-400 mb-2">Bulk Operations</h3>
            <p className="text-sm text-muted-foreground mb-3">Import products via CSV/Excel files</p>
            <div className="flex gap-2">
              <Button asChild size="sm" variant="outline">
                <Link to="/products/bulk-upload">Upload CSV</Link>
              </Button>
            </div>
          </div>
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800/30">
            <h3 className="font-semibold text-purple-600 dark:text-purple-400 mb-2">Categories</h3>
            <p className="text-sm text-muted-foreground mb-3">Organize products into categories</p>
            <div className="flex gap-2">
              <Button asChild size="sm" variant="outline">
                <Link to="/products/categories">Manage</Link>
              </Button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Order Status Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.8 }}
        className="mb-8"
      >
        <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
          <ShoppingCartIcon className="h-5 w-5 text-primary" />
          Order Status
        </h2>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <motion.div
            whileHover={{ scale: 1.03 }}
            className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-4 rounded-xl shadow-sm border border-red-200 dark:border-red-800/30 flex items-center gap-3 hover:shadow-md transition-all duration-200"
          >
            <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-lg">
              <AlertCircleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Pending</p>
              <p className="text-xl font-bold">
                <CountUp end={orderStats.pending} duration={1.5} />
              </p>
            </div>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.03 }}
            className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-4 rounded-xl shadow-sm border border-yellow-200 dark:border-yellow-800/30 flex items-center gap-3 hover:shadow-md transition-all duration-200"
          >
            <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
              <ClockIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Processing</p>
              <p className="text-xl font-bold">
                <CountUp end={orderStats.processing} duration={1.5} />
              </p>
            </div>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.03 }}
            className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl shadow-sm border border-blue-200 dark:border-blue-800/30 flex items-center gap-3 hover:shadow-md transition-all duration-200"
          >
            <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
              <TruckIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Shipped</p>
              <p className="text-xl font-bold">
                <CountUp end={orderStats.shipped} duration={1.5} />
              </p>
            </div>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.03 }}
            className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl shadow-sm border border-green-200 dark:border-green-800/30 flex items-center gap-3 hover:shadow-md transition-all duration-200"
          >
            <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
              <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Completed</p>
              <p className="text-xl font-bold">
                <CountUp end={orderStats.completed} duration={1.5} />
              </p>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Recent Orders */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.8 }}
        className="mb-8"
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <CalendarIcon className="h-5 w-5 text-primary" />
            Recent Orders
          </h2>
          <Button asChild variant="outline" size="sm" className="gap-1 hover:bg-primary/5 transition-colors">
            <Link to="/orders">
              <EyeIcon className="h-4 w-4" />
              View All
            </Link>
          </Button>
        </div>
        <div className="bg-card rounded-xl shadow-sm border border-border overflow-hidden hover:shadow-md transition-all duration-200">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="p-3 text-left font-medium text-muted-foreground">Order ID</th>
                  <th className="p-3 text-left font-medium text-muted-foreground">Customer</th>
                  <th className="p-3 text-left font-medium text-muted-foreground">Date</th>
                  <th className="p-3 text-left font-medium text-muted-foreground">Status</th>
                  <th className="p-3 text-left font-medium text-muted-foreground">Amount</th>
                  <th className="p-3 text-left font-medium text-muted-foreground">Actions</th>
                </tr>
              </thead>
              <tbody>
                {recentOrders.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="p-4 text-center">No orders found</td>
                  </tr>
                ) : (
                  recentOrders.map((order, index) => {
                    const getStatusBadgeClass = (status: string) => {
                      switch (status?.toLowerCase()) {
                        case "delivered":
                        case "completed":
                          return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
                        case "processing":
                          return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
                        case "shipped":
                          return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
                        case "pending":
                          return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
                        case "confirmed":
                          return "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-400";
                        case "cancelled":
                        case "returned":
                          return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
                        default:
                          return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
                      }
                    };

                    return (
                      <motion.tr
                        key={order._id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.2, delay: 0.1 * index }}
                        className="border-b border-border hover:bg-muted/30 transition-colors"
                      >
                        <td className="p-3 font-medium">#{order.orderNumber || order._id}</td>
                        <td className="p-3">
                          {order.userId?.profile?.firstName && order.userId?.profile?.lastName
                            ? `${order.userId.profile.firstName} ${order.userId.profile.lastName}`
                            : order.shippingAddress?.fullName || order.customerInfo?.name || 'Guest Customer'
                          }
                        </td>
                        <td className="p-3">{new Date(order.placedAt || order.createdAt).toLocaleDateString()}</td>
                        <td className="p-3">
                          <span className={`${getStatusBadgeClass(order.status)} px-2 py-1 rounded-full text-xs font-medium`}>
                            {order.status?.charAt(0).toUpperCase() + order.status?.slice(1) || 'Unknown'}
                          </span>
                        </td>
                        <td className="p-3 font-medium">₹{(order.totals?.total || 0).toLocaleString()}</td>
                        <td className="p-3">
                          <Button asChild variant="outline" size="sm" className="rounded-lg hover:bg-primary/5 transition-colors">
                            <Link to={`/orders/${order._id}`}>View</Link>
                          </Button>
                        </td>
                      </motion.tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>
      </motion.div>

      {/* Products and Customers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-6">
        {/* Top Products */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.9 }}
          className="bg-gradient-to-br from-card to-card/80 p-6 rounded-xl shadow-sm border border-border hover:shadow-md transition-all duration-200"
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold flex items-center gap-2">
              <PackageIcon className="h-5 w-5 text-primary" />
              Top Products
            </h2>
            <Button asChild variant="outline" size="sm" className="gap-1 hover:bg-primary/5 transition-colors">
              <Link to="/products">
                <EyeIcon className="h-4 w-4" />
                View All
              </Link>
            </Button>
          </div>
          <div className="space-y-4">
            {products.slice(0, 4).map((product, index) => (
              <motion.div
                key={product._id}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: 0.1 * index + 0.9 }}
                className="flex justify-between items-center p-4 rounded-lg hover:bg-muted/50 transition-colors border border-border/50 hover:border-primary/20"
              >
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-muted rounded-lg overflow-hidden flex-shrink-0 shadow-sm">
                    <img
                      src={product.product_images?.[0]?.image_url || 'https://placehold.co/100x100'}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-medium">{product.name}</p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <BadgeIndianRupeeIcon className="h-3 w-3 mr-1" />
                      {product.price?.current?.toLocaleString() || 0}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-end">
                  <p className="font-medium">{product.inventory?.available_stock || 0} in stock</p>
                  <p className="text-xs text-muted-foreground">
                    {product.social_proof?.view_count || 0} views
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Recent Customers */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.0 }}
          className="bg-gradient-to-br from-card to-card/80 p-6 rounded-xl shadow-sm border border-border hover:shadow-md transition-all duration-200"
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold flex items-center gap-2">
              <UsersIcon className="h-5 w-5 text-primary" />
              Recent Customers
            </h2>
            <Button asChild variant="outline" size="sm" className="gap-1 hover:bg-primary/5 transition-colors">
              <Link to="/customers">
                <EyeIcon className="h-4 w-4" />
                View All
              </Link>
            </Button>
          </div>
          <div className="space-y-4">
            {customers.slice(0, 4).map((customer, index) => (
              <motion.div
                key={customer._id}
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: 0.1 * index + 1.0 }}
                className="flex justify-between items-center p-4 rounded-lg hover:bg-muted/50 transition-colors border border-border/50 hover:border-primary/20"
              >
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                    <span className="text-primary font-medium">{customer.name?.charAt(0) || 'U'}</span>
                  </div>
                  <div>
                    <p className="font-medium">{customer.name || 'Unknown'}</p>
                    <p className="text-sm text-muted-foreground">{customer.email || customer.phone || 'N/A'}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{customer.orderHistory?.length || 0} orders</p>
                  <p className="text-sm text-muted-foreground flex items-center justify-end">
                    <BadgeIndianRupeeIcon className="h-3 w-3 mr-1" />
                    {(customer.totalSpent || 0).toLocaleString()}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Sales Analytics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 1.1 }}
        className="mb-8 bg-card rounded-xl shadow-sm border border-border p-6 hover:shadow-md transition-all duration-200"
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold flex items-center gap-2">
            <TrendingUpIcon className="h-5 w-5 text-primary" />
            Sales Analytics
          </h2>
          <div className="flex gap-2">
            <Button
              variant={chartType === 'bar' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('bar')}
              className="gap-1 hover:bg-primary/5 transition-colors"
            >
              <BarChartIcon className="h-4 w-4" />
              <span>Bar</span>
            </Button>
            <Button
              variant={chartType === 'line' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('line')}
              className="gap-1 hover:bg-primary/5 transition-colors"
            >
              <LineChartIcon className="h-4 w-4" />
              <span>Line</span>
            </Button>
            <Button
              variant={chartType === 'area' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('area')}
              className="gap-1 hover:bg-primary/5 transition-colors"
            >
              <TrendingUpIcon className="h-4 w-4" />
              <span>Area</span>
            </Button>
            <Button
              variant={chartType === 'pie' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('pie')}
              className="gap-1 hover:bg-primary/5 transition-colors"
            >
              <PieChartIcon className="h-4 w-4" />
              <span>Pie</span>
            </Button>
          </div>
        </div>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'area' && (
              <AreaChart data={filteredSalesData}>
                <defs>
                  <linearGradient id="colorSales" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#8884d8" stopOpacity={0}/>
                  </linearGradient>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#82ca9d" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <XAxis dataKey="name" />
                <YAxis />
                <CartesianGrid strokeDasharray="3 3" />
                <Tooltip />
                <Legend />
                <Area type="monotone" dataKey="sales" stroke="#8884d8" fillOpacity={1} fill="url(#colorSales)" />
                <Area type="monotone" dataKey="revenue" stroke="#82ca9d" fillOpacity={1} fill="url(#colorRevenue)" />
              </AreaChart>
            )}

            {chartType === 'bar' && (
              <BarChart data={filteredSalesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="sales" fill="#8884d8" />
                <Bar dataKey="revenue" fill="#82ca9d" />
              </BarChart>
            )}

            {chartType === 'line' && (
              <LineChart data={filteredSalesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="sales" stroke="#8884d8" />
                <Line type="monotone" dataKey="revenue" stroke="#82ca9d" />
              </LineChart>
            )}

            {chartType === 'pie' && (
              <PieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {pieChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            )}
          </ResponsiveContainer>
        </div>
      </motion.div>
    </div>
  );
}
