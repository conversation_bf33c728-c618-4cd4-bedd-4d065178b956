import React from 'react';
import { useParams } from 'react-router-dom';
import ProductFormPage from './add';

const EditProductPage: React.FC = () => {
  const { id } = useParams();
  
  // The ProductFormPage component already handles both add and edit modes
  // based on the presence of the id parameter
  return <ProductFormPage />;
};

export default EditProductPage;
export { EditProductPage as EditProduct };
