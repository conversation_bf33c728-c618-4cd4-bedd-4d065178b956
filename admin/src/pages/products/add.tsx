import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save, Upload, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import AdminProductService, { type AdminProduct } from '@/services/adminProductService';
import { toast } from '@/utils/toast';

interface ProductFormData {
  name: string;
  brand: string;
  category: string;
  subcategory: string;
  description: string;
  short_description: string;
  price: {
    original: number;
    current: number;
    currency: string;
  };
  material: string;
  style: string;
  occasion: string;
  season: string;
  care_instructions: string;
  tags: string[];
  inventory: {
    total_stock: number;
    available_stock: number;
    low_stock_threshold: number;
    track_inventory: boolean;
  };
  delivery_info: {
    free_shipping: boolean;
    delivery_time: string;
    delivery_cost: number;
  };
  is_featured: boolean;
  is_bestseller: boolean;
  is_new_arrival: boolean;
  is_trending: boolean;
  is_active: boolean;
}

const ProductFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    brand: '',
    category: '',
    subcategory: '',
    description: '',
    short_description: '',
    price: {
      original: 0,
      current: 0,
      currency: 'INR'
    },
    material: '',
    style: '',
    occasion: '',
    season: 'All Season',
    care_instructions: '',
    tags: [],
    inventory: {
      total_stock: 0,
      available_stock: 0,
      low_stock_threshold: 10,
      track_inventory: true
    },
    delivery_info: {
      free_shipping: false,
      delivery_time: '5-7 business days',
      delivery_cost: 0
    },
    is_featured: false,
    is_bestseller: false,
    is_new_arrival: false,
    is_trending: false,
    is_active: true
  });

  const [tagInput, setTagInput] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);

  // Load product data for editing
  useEffect(() => {
    if (isEdit && id) {
      loadProduct();
    }
  }, [isEdit, id]);

  const loadProduct = async () => {
    try {
      setLoading(true);
      const response = await AdminProductService.getProductById(id!);
      
      if (response.success) {
        const product = response.data.product;
        setFormData({
          name: product.name,
          brand: product.brand,
          category: product.category._id,
          subcategory: product.subcategory || '',
          description: product.description,
          short_description: product.short_description || '',
          price: product.price,
          material: product.material || '',
          style: product.style || '',
          occasion: product.occasion || '',
          season: product.season || 'All Season',
          care_instructions: product.care_instructions || '',
          tags: product.tags,
          inventory: product.inventory,
          delivery_info: product.delivery_info,
          is_featured: product.is_featured,
          is_bestseller: product.is_bestseller,
          is_new_arrival: product.is_new_arrival,
          is_trending: product.is_trending,
          is_active: product.is_active
        });
      } else {
        toast.error('Failed to load product');
        navigate('/products');
      }
    } catch (error) {
      console.error('Error loading product:', error);
      toast.error('Failed to load product');
      navigate('/products');
    } finally {
      setLoading(false);
    }
  };

  // Handle form field changes
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => {
      const keys = field.split('.');
      if (keys.length === 1) {
        return { ...prev, [field]: value };
      } else if (keys.length === 2) {
        return {
          ...prev,
          [keys[0]]: {
            ...prev[keys[0] as keyof ProductFormData],
            [keys[1]]: value
          }
        };
      }
      return prev;
    });
  };

  // Handle tag addition
  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  // Handle tag removal
  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Handle image selection
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedImages(prev => [...prev, ...files]);
  };

  // Handle image removal
  const handleRemoveImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.brand || !formData.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setSaving(true);
      
      let response;
      if (isEdit) {
        response = await AdminProductService.updateProduct(id!, formData);
      } else {
        response = await AdminProductService.createProduct(formData);
      }

      if (response.success) {
        const productId = response.data.product._id;
        
        // Upload images if any selected
        if (selectedImages.length > 0) {
          await AdminProductService.uploadProductImages(productId, selectedImages);
        }

        toast.success(`Product ${isEdit ? 'updated' : 'created'} successfully`);
        navigate('/products');
      } else {
        toast.error(`Failed to ${isEdit ? 'update' : 'create'} product`);
      }
    } catch (error) {
      console.error('Error saving product:', error);
      toast.error(`Failed to ${isEdit ? 'update' : 'create'} product`);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading product...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => navigate('/products')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">
            {isEdit ? 'Edit Product' : 'Add New Product'}
          </h1>
          <p className="text-muted-foreground">
            {isEdit ? 'Update product information' : 'Create a new product in your catalog'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                  placeholder="Enter product name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="brand">Brand *</Label>
                <Input
                  id="brand"
                  value={formData.brand}
                  onChange={(e) => handleFieldChange('brand', e.target.value)}
                  placeholder="Enter brand name"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => handleFieldChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Add category options here */}
                    <SelectItem value="category1">Category 1</SelectItem>
                    <SelectItem value="category2">Category 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="subcategory">Subcategory</Label>
                <Input
                  id="subcategory"
                  value={formData.subcategory}
                  onChange={(e) => handleFieldChange('subcategory', e.target.value)}
                  placeholder="Enter subcategory"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Enter product description"
                rows={4}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="short_description">Short Description</Label>
              <Textarea
                id="short_description"
                value={formData.short_description}
                onChange={(e) => handleFieldChange('short_description', e.target.value)}
                placeholder="Enter short description"
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Pricing */}
        <Card>
          <CardHeader>
            <CardTitle>Pricing</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="current_price">Current Price *</Label>
                <Input
                  id="current_price"
                  type="number"
                  step="0.01"
                  value={formData.price.current}
                  onChange={(e) => handleFieldChange('price.current', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="original_price">Original Price</Label>
                <Input
                  id="original_price"
                  type="number"
                  step="0.01"
                  value={formData.price.original}
                  onChange={(e) => handleFieldChange('price.original', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={formData.price.currency}
                  onValueChange={(value) => handleFieldChange('price.currency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="INR">INR</SelectItem>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Product Details */}
        <Card>
          <CardHeader>
            <CardTitle>Product Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="material">Material</Label>
                <Input
                  id="material"
                  value={formData.material}
                  onChange={(e) => handleFieldChange('material', e.target.value)}
                  placeholder="e.g., Cotton, Silk, Polyester"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="style">Style</Label>
                <Input
                  id="style"
                  value={formData.style}
                  onChange={(e) => handleFieldChange('style', e.target.value)}
                  placeholder="e.g., Traditional, Modern, Casual"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="occasion">Occasion</Label>
                <Input
                  id="occasion"
                  value={formData.occasion}
                  onChange={(e) => handleFieldChange('occasion', e.target.value)}
                  placeholder="e.g., Wedding, Party, Casual"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="season">Season</Label>
                <Select
                  value={formData.season}
                  onValueChange={(value) => handleFieldChange('season', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Season">All Season</SelectItem>
                    <SelectItem value="Spring">Spring</SelectItem>
                    <SelectItem value="Summer">Summer</SelectItem>
                    <SelectItem value="Fall">Fall</SelectItem>
                    <SelectItem value="Winter">Winter</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="care_instructions">Care Instructions</Label>
              <Textarea
                id="care_instructions"
                value={formData.care_instructions}
                onChange={(e) => handleFieldChange('care_instructions', e.target.value)}
                placeholder="Enter care instructions"
                rows={2}
              />
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex gap-2 mb-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder="Add a tag"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                />
                <Button type="button" onClick={handleAddTag}>Add</Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => handleRemoveTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Inventory */}
        <Card>
          <CardHeader>
            <CardTitle>Inventory</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="total_stock">Total Stock</Label>
                <Input
                  id="total_stock"
                  type="number"
                  value={formData.inventory.total_stock}
                  onChange={(e) => handleFieldChange('inventory.total_stock', parseInt(e.target.value) || 0)}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="available_stock">Available Stock</Label>
                <Input
                  id="available_stock"
                  type="number"
                  value={formData.inventory.available_stock}
                  onChange={(e) => handleFieldChange('inventory.available_stock', parseInt(e.target.value) || 0)}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="low_stock_threshold">Low Stock Threshold</Label>
                <Input
                  id="low_stock_threshold"
                  type="number"
                  value={formData.inventory.low_stock_threshold}
                  onChange={(e) => handleFieldChange('inventory.low_stock_threshold', parseInt(e.target.value) || 0)}
                  placeholder="10"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="track_inventory"
                checked={formData.inventory.track_inventory}
                onCheckedChange={(checked) => handleFieldChange('inventory.track_inventory', checked)}
              />
              <Label htmlFor="track_inventory">Track inventory</Label>
            </div>
          </CardContent>
        </Card>

        {/* Delivery Information */}
        <Card>
          <CardHeader>
            <CardTitle>Delivery Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="delivery_time">Delivery Time</Label>
                <Input
                  id="delivery_time"
                  value={formData.delivery_info.delivery_time}
                  onChange={(e) => handleFieldChange('delivery_info.delivery_time', e.target.value)}
                  placeholder="5-7 business days"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="delivery_cost">Delivery Cost</Label>
                <Input
                  id="delivery_cost"
                  type="number"
                  step="0.01"
                  value={formData.delivery_info.delivery_cost}
                  onChange={(e) => handleFieldChange('delivery_info.delivery_cost', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="free_shipping"
                checked={formData.delivery_info.free_shipping}
                onCheckedChange={(checked) => handleFieldChange('delivery_info.free_shipping', checked)}
              />
              <Label htmlFor="free_shipping">Free shipping</Label>
            </div>
          </CardContent>
        </Card>

        {/* Product Images */}
        <Card>
          <CardHeader>
            <CardTitle>Product Images</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="images">Upload Images</Label>
              <Input
                id="images"
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageSelect}
              />
            </div>

            {selectedImages.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {selectedImages.map((image, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(image)}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-32 object-cover rounded border"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute top-1 right-1"
                      onClick={() => handleRemoveImage(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Product Flags */}
        <Card>
          <CardHeader>
            <CardTitle>Product Flags</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_featured"
                  checked={formData.is_featured}
                  onCheckedChange={(checked) => handleFieldChange('is_featured', checked)}
                />
                <Label htmlFor="is_featured">Featured</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_bestseller"
                  checked={formData.is_bestseller}
                  onCheckedChange={(checked) => handleFieldChange('is_bestseller', checked)}
                />
                <Label htmlFor="is_bestseller">Bestseller</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_new_arrival"
                  checked={formData.is_new_arrival}
                  onCheckedChange={(checked) => handleFieldChange('is_new_arrival', checked)}
                />
                <Label htmlFor="is_new_arrival">New Arrival</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_trending"
                  checked={formData.is_trending}
                  onCheckedChange={(checked) => handleFieldChange('is_trending', checked)}
                />
                <Label htmlFor="is_trending">Trending</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => handleFieldChange('is_active', checked)}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/products')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isEdit ? 'Update Product' : 'Create Product'}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ProductFormPage;
export { ProductFormPage as AddProduct };
