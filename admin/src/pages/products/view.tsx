import React, { useState, useEffect } from 'react';
import { useNavigate, usePara<PERSON>, Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Star, 
  Package, 
  TrendingUp, 
  Eye,
  Calendar,
  DollarSign,
  Truck,
  Tag,
  Palette,
  Ruler
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import AdminProductService, { type AdminProduct } from '@/services/adminProductService';
import { toast } from '@/utils/toast';

const ProductViewPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [product, setProduct] = useState<AdminProduct | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadProduct();
    }
  }, [id]);

  const loadProduct = async () => {
    try {
      setLoading(true);
      const response = await AdminProductService.getProductById(id!);
      
      if (response.success) {
        setProduct(response.data.product);
      } else {
        toast.error('Failed to load product');
        navigate('/products');
      }
    } catch (error) {
      console.error('Error loading product:', error);
      toast.error('Failed to load product');
      navigate('/products');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!product || !confirm('Are you sure you want to delete this product?')) {
      return;
    }

    try {
      const response = await AdminProductService.deleteProduct(product._id);
      
      if (response.success) {
        toast.success('Product deleted successfully');
        navigate('/products');
      } else {
        toast.error('Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading product...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Product not found</p>
        <Button onClick={() => navigate('/products')} className="mt-4">
          Back to Products
        </Button>
      </div>
    );
  }

  const getStatusBadge = () => {
    if (!product.is_active) {
      return <Badge variant="secondary">Inactive</Badge>;
    }
    
    switch (product.availability) {
      case 'In Stock':
        return <Badge variant="default">In Stock</Badge>;
      case 'Out of Stock':
        return <Badge variant="destructive">Out of Stock</Badge>;
      case 'Pre-order':
        return <Badge variant="outline">Pre-order</Badge>;
      case 'Discontinued':
        return <Badge variant="secondary">Discontinued</Badge>;
      default:
        return <Badge variant="outline">{product.availability}</Badge>;
    }
  };

  const getStockBadge = () => {
    const stock = product.inventory.available_stock;
    const threshold = product.inventory.low_stock_threshold;
    
    if (stock === 0) {
      return <Badge variant="destructive">No Stock</Badge>;
    } else if (stock <= threshold) {
      return <Badge variant="outline" className="text-orange-600">Low Stock</Badge>;
    } else {
      return <Badge variant="default">Good Stock</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => navigate('/products')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{product.name}</h1>
            <p className="text-muted-foreground">Product ID: {product.product_id}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link to={`/products/edit/${product._id}`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Product Images */}
          {product.product_images.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Product Images</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {product.product_images.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={image.image_url}
                        alt={image.alt_text}
                        className="w-full h-32 object-cover rounded border"
                      />
                      {image.is_primary && (
                        <Badge className="absolute top-2 left-2" variant="default">
                          Primary
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Brand</Label>
                  <p className="text-sm">{product.brand}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Category</Label>
                  <p className="text-sm">{product.category?.name || 'Uncategorized'}</p>
                </div>
                {product.subcategory && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Subcategory</Label>
                    <p className="text-sm">{product.subcategory}</p>
                  </div>
                )}
                {product.material && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Material</Label>
                    <p className="text-sm">{product.material}</p>
                  </div>
                )}
                {product.style && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Style</Label>
                    <p className="text-sm">{product.style}</p>
                  </div>
                )}
                {product.occasion && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Occasion</Label>
                    <p className="text-sm">{product.occasion}</p>
                  </div>
                )}
                {product.season && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Season</Label>
                    <p className="text-sm">{product.season}</p>
                  </div>
                )}
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                <p className="text-sm mt-1">{product.description}</p>
              </div>

              {product.short_description && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Short Description</Label>
                  <p className="text-sm mt-1">{product.short_description}</p>
                </div>
              )}

              {product.care_instructions && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Care Instructions</Label>
                  <p className="text-sm mt-1">{product.care_instructions}</p>
                </div>
              )}

              {product.tags.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Tags</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {product.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Colors and Sizes */}
          {(product.colors.length > 0 || product.sizes.length > 0) && (
            <Card>
              <CardHeader>
                <CardTitle>Variants</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {product.colors.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                      <Palette className="h-4 w-4" />
                      Colors
                    </Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {product.colors.map((color, index) => (
                        <div key={index} className="flex items-center gap-2 p-2 border rounded">
                          <div 
                            className="w-4 h-4 rounded border"
                            style={{ backgroundColor: color.color_hex }}
                          ></div>
                          <span className="text-sm">{color.color_name}</span>
                          {!color.is_available && (
                            <Badge variant="secondary" className="text-xs">Unavailable</Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {product.sizes.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                      <Ruler className="h-4 w-4" />
                      Sizes
                    </Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {product.sizes.map((size, index) => (
                        <div key={index} className="p-2 border rounded">
                          <div className="text-sm font-medium">{size.size_name}</div>
                          <div className="text-xs text-muted-foreground">
                            Stock: {size.available_stock}
                          </div>
                          {!size.is_available && (
                            <Badge variant="secondary" className="text-xs mt-1">Unavailable</Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status and Flags */}
          <Card>
            <CardHeader>
              <CardTitle>Status & Flags</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Status</span>
                {getStatusBadge()}
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Stock Status</span>
                {getStockBadge()}
              </div>

              <Separator />

              <div className="space-y-2">
                {product.is_featured && (
                  <Badge variant="outline" className="w-full justify-center">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                )}
                {product.is_bestseller && (
                  <Badge variant="outline" className="w-full justify-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    Bestseller
                  </Badge>
                )}
                {product.is_new_arrival && (
                  <Badge variant="outline" className="w-full justify-center">
                    <Package className="h-3 w-3 mr-1" />
                    New Arrival
                  </Badge>
                )}
                {product.is_trending && (
                  <Badge variant="outline" className="w-full justify-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    Trending
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Pricing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Current Price</span>
                <span className="font-medium">
                  {product.price.currency} {product.price.current}
                </span>
              </div>
              {product.price.original !== product.price.current && (
                <>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Original Price</span>
                    <span className="text-sm line-through">
                      {product.price.currency} {product.price.original}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Discount</span>
                    <span className="text-sm text-green-600">
                      {product.price.discount_percentage}% off
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Savings</span>
                    <span className="text-sm text-green-600">
                      {product.price.currency} {product.price.savings}
                    </span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Inventory */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Inventory
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Stock</span>
                <span className="font-medium">{product.inventory.total_stock}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Available</span>
                <span className="font-medium">{product.inventory.available_stock}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Reserved</span>
                <span className="font-medium">{product.inventory.reserved_stock}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Low Stock Threshold</span>
                <span className="font-medium">{product.inventory.low_stock_threshold}</span>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Delivery
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Delivery Time</span>
                <span className="text-sm">{product.delivery_info.delivery_time}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Delivery Cost</span>
                <span className="text-sm">
                  {product.delivery_info.free_shipping 
                    ? 'Free' 
                    : `${product.price.currency} ${product.delivery_info.delivery_cost}`
                  }
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Rating & Social Proof */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Rating & Social Proof
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Average Rating</span>
                <span className="font-medium">{product.rating.average_rating}/5</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Reviews</span>
                <span className="font-medium">{product.rating.reviews_count}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Views</span>
                <span className="font-medium">{product.social_proof.view_count}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Wishlist</span>
                <span className="font-medium">{product.social_proof.wishlist_count}</span>
              </div>
            </CardContent>
          </Card>

          {/* Timestamps */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Timestamps
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Created</span>
                <span className="text-sm">
                  {new Date(product.createdAt).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Updated</span>
                <span className="text-sm">
                  {new Date(product.updatedAt).toLocaleDateString()}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// Helper component for labels
const Label: React.FC<{ className?: string; children: React.ReactNode }> = ({ 
  className = '', 
  children 
}) => (
  <div className={className}>{children}</div>
);

export default ProductViewPage;
export { ProductViewPage as ProductView };
