import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Package, AlertTriangle, TrendingDown, Plus, Minus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import AdminProductService, { type AdminProduct } from '@/services/adminProductService';
import { toast } from '@/utils/toast';

const InventoryPage: React.FC = () => {
  const navigate = useNavigate();
  const [products, setProducts] = useState<AdminProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [adjustmentDialog, setAdjustmentDialog] = useState<{
    open: boolean;
    product: AdminProduct | null;
  }>({ open: false, product: null });
  const [adjustment, setAdjustment] = useState({
    amount: 0,
    reason: ''
  });

  useEffect(() => {
    loadLowStockProducts();
  }, []);

  const loadLowStockProducts = async () => {
    try {
      setLoading(true);
      const response = await AdminProductService.getLowStockProducts(20, 1, 50);
      
      if (response.success) {
        setProducts(response.data.products);
      } else {
        toast.error('Failed to load inventory data');
      }
    } catch (error) {
      console.error('Error loading inventory:', error);
      toast.error('Failed to load inventory data');
    } finally {
      setLoading(false);
    }
  };

  const handleAdjustInventory = async () => {
    if (!adjustmentDialog.product || adjustment.amount === 0) {
      toast.error('Please enter a valid adjustment amount');
      return;
    }

    try {
      const response = await AdminProductService.adjustInventory({
        productId: adjustmentDialog.product._id,
        adjustment: adjustment.amount,
        reason: adjustment.reason
      });

      if (response.success) {
        toast.success('Inventory adjusted successfully');
        setAdjustmentDialog({ open: false, product: null });
        setAdjustment({ amount: 0, reason: '' });
        loadLowStockProducts();
      } else {
        toast.error('Failed to adjust inventory');
      }
    } catch (error) {
      console.error('Error adjusting inventory:', error);
      toast.error('Failed to adjust inventory');
    }
  };

  const openAdjustmentDialog = (product: AdminProduct) => {
    setAdjustmentDialog({ open: true, product });
    setAdjustment({ amount: 0, reason: '' });
  };

  const getStockBadge = (product: AdminProduct) => {
    const stock = product.inventory.available_stock;
    const threshold = product.inventory.low_stock_threshold;
    
    if (stock === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>;
    } else if (stock <= threshold) {
      return <Badge variant="outline" className="text-orange-600">Low Stock</Badge>;
    } else {
      return <Badge variant="default">Good Stock</Badge>;
    }
  };

  const getStockLevel = (product: AdminProduct) => {
    const stock = product.inventory.available_stock;
    const threshold = product.inventory.low_stock_threshold;
    
    if (stock === 0) return 'critical';
    if (stock <= threshold) return 'low';
    return 'good';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => navigate('/products')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Inventory Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage product stock levels
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {products.filter(p => p.inventory.available_stock <= p.inventory.low_stock_threshold && p.inventory.available_stock > 0).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Items below threshold
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {products.filter(p => p.inventory.available_stock === 0).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Items unavailable
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {products.length}
            </div>
            <p className="text-xs text-muted-foreground">
              Items monitored
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Inventory Table */}
      <Card>
        <CardHeader>
          <CardTitle>Low Stock & Out of Stock Products</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Current Stock</TableHead>
                <TableHead>Threshold</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    Loading inventory data...
                  </TableCell>
                </TableRow>
              ) : products.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    No low stock items found
                  </TableCell>
                </TableRow>
              ) : (
                products.map((product) => (
                  <TableRow key={product._id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {product.product_images.length > 0 && (
                          <img
                            src={product.product_images[0].image_url}
                            alt={product.name}
                            className="w-10 h-10 rounded object-cover"
                          />
                        )}
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {product.product_id}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={`font-medium ${
                        getStockLevel(product) === 'critical' ? 'text-red-600' :
                        getStockLevel(product) === 'low' ? 'text-orange-600' :
                        'text-green-600'
                      }`}>
                        {product.inventory.available_stock}
                      </div>
                    </TableCell>
                    <TableCell>
                      {product.inventory.low_stock_threshold}
                    </TableCell>
                    <TableCell>
                      {getStockBadge(product)}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openAdjustmentDialog(product)}
                      >
                        Adjust Stock
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Adjustment Dialog */}
      <Dialog 
        open={adjustmentDialog.open} 
        onOpenChange={(open) => setAdjustmentDialog({ open, product: adjustmentDialog.product })}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adjust Inventory</DialogTitle>
          </DialogHeader>
          {adjustmentDialog.product && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">{adjustmentDialog.product.name}</h4>
                <p className="text-sm text-muted-foreground">
                  Current Stock: {adjustmentDialog.product.inventory.available_stock}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="adjustment">Adjustment Amount</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setAdjustment(prev => ({ ...prev, amount: prev.amount - 1 }))}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <Input
                    id="adjustment"
                    type="number"
                    value={adjustment.amount}
                    onChange={(e) => setAdjustment(prev => ({ 
                      ...prev, 
                      amount: parseInt(e.target.value) || 0 
                    }))}
                    className="text-center"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setAdjustment(prev => ({ ...prev, amount: prev.amount + 1 }))}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Use positive numbers to add stock, negative to reduce
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="reason">Reason (Optional)</Label>
                <Textarea
                  id="reason"
                  value={adjustment.reason}
                  onChange={(e) => setAdjustment(prev => ({ ...prev, reason: e.target.value }))}
                  placeholder="Enter reason for adjustment..."
                  rows={3}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setAdjustmentDialog({ open: false, product: null })}
                >
                  Cancel
                </Button>
                <Button onClick={handleAdjustInventory}>
                  Adjust Inventory
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InventoryPage;
export { InventoryPage as ProductInventory };
