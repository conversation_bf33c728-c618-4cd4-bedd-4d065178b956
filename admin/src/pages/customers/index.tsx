import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { SearchIcon, UserIcon, EditIcon, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAppSelector, useAppDispatch } from "@/hooks/use-redux";
import { fetchCustomers, setFilters, setPagination } from "@/store/slices/customersSlice";
import { toast } from "react-hot-toast";

export function Customers() {
  const [searchTerm, setSearchTerm] = React.useState("");
  const [sortOption, setSortOption] = React.useState("");
  const dispatch = useAppDispatch();

  // Get customers from Redux store
  const { items: allCustomers, loading, error, pagination, filters } = useAppSelector((state) => state.customers);

  // Fetch customers on component mount and when filters change
  React.useEffect(() => {
    const fetchData = async () => {
      try {
        await dispatch(fetchCustomers({
          ...filters,
          page: pagination.page,
          limit: pagination.limit,
          search: searchTerm || undefined,
          sortBy: sortOption ? sortOption.split('-')[0] : undefined,
          sortOrder: sortOption ? sortOption.split('-')[1] : undefined
        })).unwrap();
      } catch (error: any) {
        toast.error(error || 'Failed to fetch customers');
      }
    };

    fetchData();
  }, [dispatch, filters, pagination.page, pagination.limit, searchTerm, sortOption]);

  // Handle search with debounce
  const debouncedSearch = React.useMemo(
    () => {
      const timeoutId = setTimeout(() => {
        if (pagination.page !== 1) {
          dispatch(setPagination({ page: 1, limit: pagination.limit }));
        }
      }, 500);
      return () => clearTimeout(timeoutId);
    },
    [searchTerm, dispatch, pagination.page, pagination.limit]
  );

  React.useEffect(() => {
    return debouncedSearch;
  }, [debouncedSearch]);

  // Filter customers based on search term (for client-side filtering if needed)
  const filteredCustomers = React.useMemo(() => {
    return allCustomers.filter(customer => {
      const searchLower = searchTerm.toLowerCase();
      const customerName = customer.name || customer.fullName || `${customer.profile?.firstName || ''} ${customer.profile?.lastName || ''}`.trim();
      const customerEmail = customer.email || '';
      const customerPhone = customer.phone || '';

      return (
        customerName.toLowerCase().includes(searchLower) ||
        customerEmail.toLowerCase().includes(searchLower) ||
        customerPhone.includes(searchTerm)
      );
    });
  }, [allCustomers, searchTerm]);

  // For server-side pagination, we use the data as-is from the API
  // The sorting and filtering is handled by the backend
  const currentCustomers = allCustomers;

  const handlePreviousPage = () => {
    if (pagination.page > 1) {
      dispatch(setPagination({ page: pagination.page - 1, limit: pagination.limit }));
    }
  };

  const handleNextPage = () => {
    if (pagination.page < pagination.totalPages) {
      dispatch(setPagination({ page: pagination.page + 1, limit: pagination.limit }));
    }
  };

  const handleRefresh = async () => {
    try {
      await dispatch(fetchCustomers({
        ...filters,
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm || undefined,
        sortBy: sortOption ? sortOption.split('-')[0] : undefined,
        sortOrder: sortOption ? sortOption.split('-')[1] : undefined
      })).unwrap();
      toast.success('Customers refreshed successfully');
    } catch (error: any) {
      toast.error(error || 'Failed to refresh customers');
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Customers</h1>
        <Button
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-card p-4 rounded-lg shadow mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search customers..."
              className="w-full pl-10 pr-4 py-2 border rounded-md"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
              }}
              disabled={loading}
            />
          </div>
          <select
            className="border rounded-md px-4 py-2"
            value={sortOption}
            onChange={(e) => setSortOption(e.target.value)}
            disabled={loading}
          >
            <option value="">Sort By</option>
            <option value="firstName-asc">Name (A-Z)</option>
            <option value="firstName-desc">Name (Z-A)</option>
            <option value="createdAt-desc">Newest First</option>
            <option value="createdAt-asc">Oldest First</option>
            <option value="totalSpent-desc">Total Spent (High to Low)</option>
            <option value="totalSpent-asc">Total Spent (Low to High)</option>
          </select>
        </div>

        {searchTerm && (
          <div className="mt-2 text-sm text-muted-foreground">
            Searching for "{searchTerm}"...
          </div>
        )}
      </div>

      <div className="bg-card rounded-lg shadow overflow-hidden">
        <table className="w-full">
          <thead className="bg-muted">
            <tr>
              <th className="p-3 text-left">Name</th>
              <th className="p-3 text-left">Email</th>
              <th className="p-3 text-left">Phone</th>
              <th className="p-3 text-left">Orders</th>
              <th className="p-3 text-left">Total Spent</th>
              <th className="p-3 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={6} className="p-8 text-center text-muted-foreground">
                  <div className="flex items-center justify-center">
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    Loading customers...
                  </div>
                </td>
              </tr>
            ) : currentCustomers.length === 0 ? (
              <tr>
                <td colSpan={6} className="p-8 text-center text-muted-foreground">
                  {searchTerm
                    ? `No customers found matching "${searchTerm}"`
                    : "No customers found"}
                </td>
              </tr>
            ) : (
              currentCustomers.map((customer) => {
                const customerName = customer.name || customer.fullName || `${customer.profile?.firstName || ''} ${customer.profile?.lastName || ''}`.trim() || 'N/A';
                const customerEmail = customer.email || 'N/A';
                const customerPhone = customer.phone || 'N/A';
                const customerOrders = customer.orders || customer.orderStats?.totalOrders || 0;
                const customerSpent = customer.totalSpent || customer.orderStats?.totalSpent || 0;
                const customerId = customer._id || customer.id;

                return (
                  <tr key={customerId} className="border-b border-border hover:bg-muted/30 transition-colors">
                    <td className="p-3 font-medium">{customerName}</td>
                    <td className="p-3">{customerEmail}</td>
                    <td className="p-3">{customerPhone}</td>
                    <td className="p-3">{customerOrders}</td>
                    <td className="p-3">₹{customerSpent.toLocaleString()}</td>
                    <td className="p-3">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/customers/${customerId}`}>View</Link>
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/customers/edit/${customerId}`}>Edit</Link>
                        </Button>
                      </div>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      <div className="mt-4 flex flex-col sm:flex-row justify-between items-center gap-4">
        <div className="text-sm text-muted-foreground">
          {pagination.total > 0 ? (
            <>
              Showing {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} customers
            </>
          ) : (
            "No customers found"
          )}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreviousPage}
            disabled={pagination.page === 1 || loading}
          >
            Previous
          </Button>
          {pagination.totalPages > 0 && (
            <div className="flex items-center px-3 text-sm">
              Page {pagination.page} of {pagination.totalPages}
            </div>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleNextPage}
            disabled={pagination.page === pagination.totalPages || loading}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
