import * as React from "react";
import { usePara<PERSON>, useNavigate, <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  ChevronLeftIcon,
  SaveIcon,
  Trash2Icon,
  UserIcon,
  MailIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  AlertCircleIcon
} from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/hooks/use-redux";
import { updateCustomer, deleteCustomer } from "@/store/slices/customersSlice";

export function EditCustomer() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const customers = useAppSelector((state) => state.customers.items);
  const customerId = parseInt(id || "0");

  const customer = React.useMemo(() => {
    return customers.find(c => c.id === customerId);
  }, [customers, customerId]);

  const [formData, setFormData] = React.useState({
    name: "",
    email: "",
    phone: "",
    address: {
      street: "123 Main Street",
      apartment: "Apartment 4B",
      city: "Mumbai",
      state: "Maharashtra",
      zipCode: "400001",
      country: "India"
    },
    dateJoined: "January 15, 2023"
  });

  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
  const [errors, setErrors] = React.useState<Record<string, string>>({});

  React.useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: {
          street: "123 Main Street",
          apartment: "Apartment 4B",
          city: "Mumbai",
          state: "Maharashtra",
          zipCode: "400001",
          country: "India"
        },
        dateJoined: "January 15, 2023"
      });
    } else if (customerId > 0) {
      // If customer not found, redirect to customers page
      navigate("/customers");
    }
  }, [customer, customerId, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Handle nested address fields
    if (name.startsWith("address.")) {
      const addressField = name.split(".")[1];
      setFormData({
        ...formData,
        address: {
          ...formData.address,
          [addressField]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ""
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }
    
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = "Phone is required";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Dispatch action to update customer
      dispatch(updateCustomer({
        id: customerId,
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        orders: customer?.orders || 0,
        totalSpent: customer?.totalSpent || 0,
        address: `${formData.address.street}, ${formData.address.apartment}, ${formData.address.city}, ${formData.address.state}, ${formData.address.zipCode}, ${formData.address.country}`,
        dateJoined: formData.dateJoined
      }));

      // Navigate back to customer view page
      navigate(`/customers/${customerId}`);
    } catch (error) {
      console.error("Error updating customer:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      dispatch(deleteCustomer(customerId));
      navigate("/customers");
    } catch (error) {
      console.error("Error deleting customer:", error);
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  if (!customer && customerId > 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin h-8 w-8 border-4 border-primary/30 border-t-primary rounded-full"></div>
          <p className="text-muted-foreground">Loading customer information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              asChild
            >
              <Link to={`/customers/${customerId}`}>
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <UserIcon className="h-6 w-6 text-primary" />
              Edit Customer
            </h1>
          </div>
          <p className="text-muted-foreground">
            Update customer information and settings.
          </p>
        </div>
        <Button
          variant="destructive"
          onClick={() => setShowDeleteConfirm(true)}
          className="flex items-center gap-1"
          disabled={isDeleting}
        >
          {isDeleting ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-white/30 border-t-white rounded-full mr-1"></div>
              Deleting...
            </>
          ) : (
            <>
              <Trash2Icon className="h-4 w-4" />
              Delete Customer
            </>
          )}
        </Button>
      </div>

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full">
            <div className="flex items-center gap-3 text-red-500 mb-4">
              <AlertCircleIcon className="h-6 w-6" />
              <h2 className="text-xl font-semibold">Confirm Deletion</h2>
            </div>
            <p className="mb-6">
              Are you sure you want to delete this customer? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Customer"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Form */}
      <div className="bg-card p-6 rounded-lg shadow">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h2 className="text-lg font-medium">Basic Information</h2>
              
              <div className="space-y-2">
                <label htmlFor="name" className="block text-sm font-medium">
                  Full Name
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : ''}`}
                  value={formData.name}
                  onChange={handleChange}
                />
                {errors.name && (
                  <p className="text-red-500 text-sm">{errors.name}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium">
                  Email Address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  className={`w-full p-2 border rounded-md ${errors.email ? 'border-red-500' : ''}`}
                  value={formData.email}
                  onChange={handleChange}
                />
                {errors.email && (
                  <p className="text-red-500 text-sm">{errors.email}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <label htmlFor="phone" className="block text-sm font-medium">
                  Phone Number
                </label>
                <input
                  id="phone"
                  name="phone"
                  type="text"
                  className={`w-full p-2 border rounded-md ${errors.phone ? 'border-red-500' : ''}`}
                  value={formData.phone}
                  onChange={handleChange}
                />
                {errors.phone && (
                  <p className="text-red-500 text-sm">{errors.phone}</p>
                )}
              </div>
            </div>
            
            {/* Address Information */}
            <div className="space-y-4">
              <h2 className="text-lg font-medium">Address Information</h2>
              
              <div className="space-y-2">
                <label htmlFor="address.street" className="block text-sm font-medium">
                  Street Address
                </label>
                <input
                  id="address.street"
                  name="address.street"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  value={formData.address.street}
                  onChange={handleChange}
                />
              </div>
              
              <div className="space-y-2">
                <label htmlFor="address.apartment" className="block text-sm font-medium">
                  Apartment/Suite
                </label>
                <input
                  id="address.apartment"
                  name="address.apartment"
                  type="text"
                  className="w-full p-2 border rounded-md"
                  value={formData.address.apartment}
                  onChange={handleChange}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="address.city" className="block text-sm font-medium">
                    City
                  </label>
                  <input
                    id="address.city"
                    name="address.city"
                    type="text"
                    className="w-full p-2 border rounded-md"
                    value={formData.address.city}
                    onChange={handleChange}
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="address.state" className="block text-sm font-medium">
                    State/Province
                  </label>
                  <input
                    id="address.state"
                    name="address.state"
                    type="text"
                    className="w-full p-2 border rounded-md"
                    value={formData.address.state}
                    onChange={handleChange}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="address.zipCode" className="block text-sm font-medium">
                    Postal/Zip Code
                  </label>
                  <input
                    id="address.zipCode"
                    name="address.zipCode"
                    type="text"
                    className="w-full p-2 border rounded-md"
                    value={formData.address.zipCode}
                    onChange={handleChange}
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="address.country" className="block text-sm font-medium">
                    Country
                  </label>
                  <input
                    id="address.country"
                    name="address.country"
                    type="text"
                    className="w-full p-2 border rounded-md"
                    value={formData.address.country}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end gap-4 pt-4 border-t">
            <Button
              variant="outline"
              type="button"
              onClick={() => navigate(`/customers/${customerId}`)}
              className="flex items-center gap-1"
              disabled={isSubmitting}
            >
              <ChevronLeftIcon className="h-4 w-4" />
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex items-center gap-1"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white/30 border-t-white rounded-full mr-1"></div>
                  Saving...
                </>
              ) : (
                <>
                  <SaveIcon className="h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
