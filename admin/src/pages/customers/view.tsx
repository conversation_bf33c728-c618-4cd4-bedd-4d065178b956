import * as React from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  ChevronLeftIcon,
  MailIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  ShoppingCartIcon,
  CreditCardIcon,
  UserIcon,
  EditIcon,
  BarChart3Icon,
  RefreshCwIcon
} from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/hooks/use-redux";
import { fetchCustomerById } from "@/store/slices/customersSlice";
import { fetchOrders } from "@/store/slices/ordersSlice";

export function CustomerView() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { items: customers, loading: customersLoading } = useAppSelector((state) => state.customers);
  const { items: orders, loading: ordersLoading } = useAppSelector((state) => state.orders);

  const [customerNotes, setCustomerNotes] = React.useState("");
  const [isSavingNotes, setIsSavingNotes] = React.useState(false);
  const [notesSaved, setNotesSaved] = React.useState(false);

  // Fetch customer data on mount
  React.useEffect(() => {
    if (id) {
      dispatch(fetchCustomerById(id));
      // Fetch orders for this customer
      dispatch(fetchOrders({ userId: id, page: 1, limit: 10 }));
    }
  }, [dispatch, id]);

  const customer = React.useMemo(() => {
    return customers?.find(c => c._id === id);
  }, [customers, id]);

  const customerOrders = React.useMemo(() => {
    if (!customer || !orders) return [];

    // Filter orders for this specific customer
    return orders
      .filter(order => order.userId?._id === customer._id || order.userId === customer._id)
      .slice(0, 5)
      .sort((a, b) => new Date(b.placedAt || b.createdAt).getTime() - new Date(a.placedAt || a.createdAt).getTime());
  }, [customer, orders]);

  const handleSaveNotes = () => {
    setIsSavingNotes(true);

    // Simulate API call to save notes
    setTimeout(() => {
      setIsSavingNotes(false);
      setNotesSaved(true);

      // Hide the success message after 3 seconds
      setTimeout(() => {
        setNotesSaved(false);
      }, 3000);
    }, 1000);
  };

  if (customersLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <RefreshCwIcon className="h-8 w-8 animate-spin text-primary mb-4" />
        <div className="text-xl font-medium mb-2">Loading customer details...</div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <div className="text-xl font-medium mb-2">Customer not found</div>
        <p className="text-muted-foreground mb-4">The customer you're looking for doesn't exist or has been removed.</p>
        <Button asChild>
          <Link to="/customers">Back to Customers</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              asChild
            >
              <Link to="/customers">
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">
              {customer.fullName ||
               (customer.profile?.firstName && customer.profile?.lastName
                 ? `${customer.profile.firstName} ${customer.profile.lastName}`
                 : customer.name || 'Unknown Customer')}
            </h1>
          </div>
          <p className="text-muted-foreground">
            Customer details and order history.
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link to={`/customers/edit/${customer._id}`}>
            <EditIcon className="mr-2 h-4 w-4" />
            Edit Customer
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1 space-y-6">
          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-primary/10 p-4 rounded-full">
                <UserIcon className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">
                  {customer.fullName ||
                   (customer.profile?.firstName && customer.profile?.lastName
                     ? `${customer.profile.firstName} ${customer.profile.lastName}`
                     : customer.name || 'Unknown Customer')}
                </h2>
                <p className="text-muted-foreground">
                  Customer since {new Date(customer.createdAt || customer.registeredAt || '2023-01-01').getFullYear()}
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <MailIcon className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Email</div>
                  <div className="text-sm text-muted-foreground">
                    {customer.email || customer.profile?.email || 'Not provided'}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <PhoneIcon className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Phone</div>
                  <div className="text-sm text-muted-foreground">
                    {customer.phone || customer.profile?.phone || 'Not provided'}
                  </div>
                </div>
              </div>

              {customer.addresses && customer.addresses.length > 0 && (
                <div className="flex items-start gap-3">
                  <MapPinIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <div className="font-medium">Primary Address</div>
                    <div className="text-sm text-muted-foreground">
                      {customer.addresses[0].addressLine1}<br />
                      {customer.addresses[0].addressLine2 && <>{customer.addresses[0].addressLine2}<br /></>}
                      {customer.addresses[0].city}, {customer.addresses[0].state} {customer.addresses[0].pincode}<br />
                      {customer.addresses[0].country || 'India'}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-3">
                <CalendarIcon className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Member Since</div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(customer.createdAt || customer.registeredAt || '2023-01-01').toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Customer Stats</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-md">
                <div className="flex items-center gap-3">
                  <ShoppingCartIcon className="h-5 w-5 text-primary" />
                  <div className="font-medium">Total Orders</div>
                </div>
                <div className="text-xl font-bold">{customer.orderStats?.totalOrders || customer.orders || 0}</div>
              </div>

              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-md">
                <div className="flex items-center gap-3">
                  <CreditCardIcon className="h-5 w-5 text-primary" />
                  <div className="font-medium">Total Spent</div>
                </div>
                <div className="text-xl font-bold">₹{(customer.orderStats?.totalSpent || customer.totalSpent || 0).toLocaleString()}</div>
              </div>

              <div className="flex justify-between items-center p-3 bg-muted/50 rounded-md">
                <div className="flex items-center gap-3">
                  <BarChart3Icon className="h-5 w-5 text-primary" />
                  <div className="font-medium">Avg. Order Value</div>
                </div>
                <div className="text-xl font-bold">
                  ₹{(customer.orderStats?.totalOrders || customer.orders || 0) > 0
                    ? Math.round((customer.orderStats?.totalSpent || customer.totalSpent || 0) / (customer.orderStats?.totalOrders || customer.orders || 1)).toLocaleString()
                    : 0}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="md:col-span-2 space-y-6">
          <div className="bg-card p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Recent Orders</h2>
              <Button asChild variant="outline" size="sm">
                <Link to="/orders">View All Orders</Link>
              </Button>
            </div>

            {ordersLoading ? (
              <div className="text-center py-8">
                <RefreshCwIcon className="h-6 w-6 animate-spin text-primary mx-auto mb-2" />
                <p className="text-muted-foreground">Loading orders...</p>
              </div>
            ) : customerOrders.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No orders found for this customer.
              </div>
            ) : (
              <div className="space-y-4">
                {customerOrders.map((order) => (
                  <div key={order._id} className="flex justify-between items-center p-4 border rounded-md hover:bg-muted/50">
                    <div>
                      <div className="font-medium">Order #{order.orderNumber || order._id}</div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(order.placedAt || order.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {order.items?.length || 0} item{(order.items?.length || 0) !== 1 ? 's' : ''}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">₹{(order.totals?.total || 0).toLocaleString()}</div>
                      <div className="text-sm">
                        <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${
                          order.status?.toLowerCase() === "delivered"
                            ? "bg-green-100 text-green-800"
                            : order.status?.toLowerCase() === "processing"
                              ? "bg-yellow-100 text-yellow-800"
                              : order.status?.toLowerCase() === "shipped"
                                ? "bg-blue-100 text-blue-800"
                                : order.status?.toLowerCase() === "pending"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-gray-100 text-gray-800"
                        }`}>
                          {order.status?.charAt(0).toUpperCase() + order.status?.slice(1) || 'Unknown'}
                        </span>
                      </div>
                    </div>
                    <Button asChild variant="outline" size="sm">
                      <Link to={`/orders/${order._id}`}>View</Link>
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Notes</h2>
            <div className="space-y-4">
              {notesSaved && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex justify-between items-center">
                  <span>Notes saved successfully!</span>
                </div>
              )}
              <textarea
                rows={4}
                className="w-full p-3 border rounded-md"
                placeholder="Add notes about this customer..."
                value={customerNotes}
                onChange={(e) => setCustomerNotes(e.target.value)}
              ></textarea>
              <div className="flex justify-end">
                <Button
                  onClick={handleSaveNotes}
                  disabled={isSavingNotes || customerNotes.trim() === ""}
                >
                  {isSavingNotes ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-2 border-white/30 border-t-white rounded-full mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    "Save Notes"
                  )}
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Communication History</h2>
            <div className="space-y-4">
              <div className="p-4 border rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <div className="font-medium">Order Confirmation</div>
                    <div className="text-sm text-muted-foreground">
                      Email sent on June 15, 2023
                    </div>
                  </div>
                  <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs font-medium">
                    Delivered
                  </span>
                </div>
                <p className="text-sm">
                  Order confirmation email for order #ORD-001 was sent to {customer.email || customer.profile?.email || 'customer email'}.
                </p>
              </div>

              <div className="p-4 border rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <div className="font-medium">Shipping Notification</div>
                    <div className="text-sm text-muted-foreground">
                      Email sent on June 16, 2023
                    </div>
                  </div>
                  <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs font-medium">
                    Delivered
                  </span>
                </div>
                <p className="text-sm">
                  Shipping notification email for order #ORD-001 was sent to {customer.email || customer.profile?.email || 'customer email'}.
                </p>
              </div>

              <div className="p-4 border rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <div className="font-medium">Feedback Request</div>
                    <div className="text-sm text-muted-foreground">
                      Email sent on June 20, 2023
                    </div>
                  </div>
                  <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs font-medium">
                    Opened
                  </span>
                </div>
                <p className="text-sm">
                  Feedback request email for order #ORD-001 was sent to {customer.email || customer.profile?.email || 'customer email'}.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
