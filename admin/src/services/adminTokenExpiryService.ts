/**
 * Admin Token Expiry Service
 * Monitors JWT token expiration and handles automatic re-login prompts
 */

import { adminTokenService } from './adminTokenService';
import { clearAdminAuth } from './adminAuthService';

export interface TokenExpiryConfig {
  warningThreshold: number; // Minutes before expiry to show warning
  checkInterval: number; // Interval to check token status (ms)
  autoLogoutBuffer: number; // Minutes before expiry to auto-logout
}

export interface TokenStatus {
  isValid: boolean;
  isExpired: boolean;
  willExpireSoon: boolean;
  timeUntilExpiry: number; // milliseconds
  expiryDate: Date | null;
}

class AdminTokenExpiryService {
  private config: TokenExpiryConfig = {
    warningThreshold: 30, // Show warning 30 minutes before expiry
    checkInterval: 60000, // Check every minute
    autoLogoutBuffer: 5 // Auto-logout 5 minutes before expiry
  };

  private intervalId: NodeJS.Timeout | null = null;
  private warningShown = false;
  private onTokenExpiringCallback: ((status: TokenStatus) => void) | null = null;
  private onTokenExpiredCallback: (() => void) | null = null;

  /**
   * Start monitoring token expiration
   */
  startMonitoring(config?: Partial<TokenExpiryConfig>): void {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // Clear any existing interval
    this.stopMonitoring();

    // Start checking token status
    this.intervalId = setInterval(() => {
      this.checkTokenStatus();
    }, this.config.checkInterval);

    // Initial check
    this.checkTokenStatus();
  }

  /**
   * Stop monitoring token expiration
   */
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.warningShown = false;
  }

  /**
   * Set callback for when token is expiring soon
   */
  onTokenExpiring(callback: (status: TokenStatus) => void): void {
    this.onTokenExpiringCallback = callback;
  }

  /**
   * Set callback for when token has expired
   */
  onTokenExpired(callback: () => void): void {
    this.onTokenExpiredCallback = callback;
  }

  /**
   * Get current token status
   */
  getTokenStatus(): TokenStatus {
    const token = adminTokenService.getToken();
    
    if (!token) {
      return {
        isValid: false,
        isExpired: true,
        willExpireSoon: false,
        timeUntilExpiry: 0,
        expiryDate: null
      };
    }

    const expiryTimestamp = adminTokenService.getTokenExpiry(token);
    
    if (!expiryTimestamp) {
      return {
        isValid: false,
        isExpired: true,
        willExpireSoon: false,
        timeUntilExpiry: 0,
        expiryDate: null
      };
    }

    const now = Date.now();
    const timeUntilExpiry = expiryTimestamp - now;
    const isExpired = timeUntilExpiry <= 0;
    const warningThresholdMs = this.config.warningThreshold * 60 * 1000;
    const willExpireSoon = timeUntilExpiry <= warningThresholdMs && timeUntilExpiry > 0;

    return {
      isValid: !isExpired,
      isExpired,
      willExpireSoon,
      timeUntilExpiry: Math.max(0, timeUntilExpiry),
      expiryDate: new Date(expiryTimestamp)
    };
  }

  /**
   * Check token status and trigger callbacks
   */
  private checkTokenStatus(): void {
    const status = this.getTokenStatus();

    // If no token exists, don't trigger expired callback (user not logged in)
    if (!adminTokenService.getToken()) {
      return;
    }

    // Token has expired
    if (status.isExpired) {
      this.handleTokenExpired();
      return;
    }

    // Token will expire soon
    if (status.willExpireSoon && !this.warningShown) {
      this.handleTokenExpiring(status);
      this.warningShown = true;
    }

    // Auto-logout buffer check
    const autoLogoutMs = this.config.autoLogoutBuffer * 60 * 1000;
    if (status.timeUntilExpiry <= autoLogoutMs && status.timeUntilExpiry > 0) {
      this.handleTokenExpired();
    }
  }

  /**
   * Handle token expiring soon
   */
  private handleTokenExpiring(status: TokenStatus): void {
    console.warn('Admin token expiring soon:', {
      timeUntilExpiry: status.timeUntilExpiry,
      expiryDate: status.expiryDate
    });

    if (this.onTokenExpiringCallback) {
      this.onTokenExpiringCallback(status);
    }
  }

  /**
   * Handle token expired
   */
  private handleTokenExpired(): void {
    console.warn('Admin token has expired, logging out...');

    this.stopMonitoring();
    clearAdminAuth();

    if (this.onTokenExpiredCallback) {
      this.onTokenExpiredCallback();
    }
  }

  /**
   * Format time until expiry for display
   */
  formatTimeUntilExpiry(timeMs: number): string {
    const minutes = Math.floor(timeMs / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    } else {
      return 'less than a minute';
    }
  }

  /**
   * Extend session (would typically refresh token)
   */
  async extendSession(): Promise<boolean> {
    // In a real implementation, this would call a refresh token endpoint
    // For now, we'll return false to indicate session cannot be extended
    console.log('Session extension not implemented - user needs to re-login');
    return false;
  }

  /**
   * Reset warning state (useful after successful token refresh)
   */
  resetWarningState(): void {
    this.warningShown = false;
  }
}

// Export singleton instance
export const adminTokenExpiryService = new AdminTokenExpiryService();

// Export utility functions
export const startTokenMonitoring = (config?: Partial<TokenExpiryConfig>) => 
  adminTokenExpiryService.startMonitoring(config);

export const stopTokenMonitoring = () => 
  adminTokenExpiryService.stopMonitoring();

export const getTokenStatus = () => 
  adminTokenExpiryService.getTokenStatus();

export const formatTimeUntilExpiry = (timeMs: number) => 
  adminTokenExpiryService.formatTimeUntilExpiry(timeMs);

export default adminTokenExpiryService;
