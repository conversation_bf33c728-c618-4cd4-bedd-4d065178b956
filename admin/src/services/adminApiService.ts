/**
 * Admin API Service - Centralized API configuration for admin panel
 * All admin API calls should go through this service for consistency
 * Enhanced with JWT token authentication
 */

// Cookie-based authentication - no token service needed

const ADMIN_API_BASE_URL = import.meta.env.VITE_ADMIN_API_URL || "http://localhost:5000/api/admin";
const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:5000/api";

// API Response types
export interface AdminApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface AdminApiError {
  message: string;
  status?: number;
  code?: string;
}

// Base API configuration for cookie-based authentication
const defaultConfig: RequestInit = {
  credentials: 'include', // Include cookies for fallback
  headers: {
    'Content-Type': 'application/json',
  },
};

/**
 * Base API call function with error handling and JWT token authentication
 */
const adminApiCall = async <T = any>(
  endpoint: string,
  config: RequestInit = {}
): Promise<T> => {
  const url = endpoint.startsWith('/admin')
    ? `${ADMIN_API_BASE_URL}${endpoint.replace('/admin', '')}`
    : `${API_BASE_URL}${endpoint}`;

  // Cookie-based authentication - no manual headers needed
  const finalConfig: RequestInit = {
    ...defaultConfig,
    ...config,
    headers: {
      ...defaultConfig.headers,
      ...config.headers,
    },
  };

  try {
    const response = await fetch(url, finalConfig);
    
    // Handle different response types
    const contentType = response.headers.get('content-type');
    let responseData: any;
    
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }

    // Handle HTTP errors
    if (!response.ok) {
      // Handle authentication errors
      if (response.status === 401) {
        // Clear local user data on auth failure
        localStorage.removeItem('admin_user');

        // Don't redirect if this is a login request or verify request
        if (!endpoint.includes('/auth/login') && !endpoint.includes('/auth/verify')) {
          // Only redirect if we're not already on the login page
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        }
      }

      throw {
        message: responseData?.message || responseData || `HTTP ${response.status}: ${response.statusText}`,
        status: response.status,
        code: responseData?.code,
      } as AdminApiError;
    }

    return responseData;
  } catch (error) {
    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw {
        message: 'Network error. Please check your connection.',
        status: 0,
      } as AdminApiError;
    }

    // Re-throw API errors
    throw error;
  }
};

/**
 * GET request helper
 */
export const adminApiGet = <T = any>(endpoint: string): Promise<T> => {
  return adminApiCall<T>(endpoint, { method: 'GET' });
};

/**
 * POST request helper
 */
export const adminApiPost = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return adminApiCall<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
};

/**
 * PUT request helper
 */
export const adminApiPut = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return adminApiCall<T>(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
};

/**
 * DELETE request helper
 */
export const adminApiDelete = <T = any>(endpoint: string): Promise<T> => {
  return adminApiCall<T>(endpoint, { method: 'DELETE' });
};

/**
 * File upload helper
 */
export const adminApiUpload = <T = any>(endpoint: string, formData: FormData): Promise<T> => {
  return adminApiCall<T>(endpoint, {
    method: 'POST',
    body: formData,
    headers: {}, // Let browser set Content-Type for FormData
  });
};

/**
 * Admin API endpoints configuration
 */
export const ADMIN_API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/admin/auth/login',
    LOGOUT: '/admin/auth/logout',
    VERIFY: '/admin/auth/verify',
    REFRESH: '/admin/auth/refresh',
    PROFILE: '/admin/auth/profile',
    CHANGE_PASSWORD: '/admin/auth/change-password',
    ENABLE_2FA: '/admin/auth/2fa/enable',
    VERIFY_2FA: '/admin/auth/2fa/verify',
    DISABLE_2FA: '/admin/auth/2fa/disable',
  },

  // Dashboard & Analytics
  DASHBOARD: {
    STATS: '/admin/dashboard/stats',
    RECENT_ORDERS: '/admin/dashboard/recent-orders',
    RECENT_CUSTOMERS: '/admin/dashboard/recent-customers',
    LOW_STOCK: '/admin/dashboard/low-stock',
    SALES_CHART: '/admin/dashboard/sales-chart',
    TOP_PRODUCTS: '/admin/dashboard/top-products',
  },

  // Product Management
  PRODUCTS: {
    LIST: '/admin/products',
    CREATE: '/admin/products',
    UPDATE: (id: string) => `/admin/products/${id}`,
    DELETE: (id: string) => `/admin/products/${id}`,
    BULK_UPDATE: '/admin/products/bulk-update',
    BULK_DELETE: '/admin/products/bulk-delete',
    IMPORT: '/admin/products/import',
    EXPORT: '/admin/products/export',
    TEMPLATE: '/admin/products/template',
    STATISTICS: '/admin/products/statistics',
    LOW_STOCK: '/admin/products/low-stock',
    INVENTORY_ADJUST: '/admin/products/inventory/adjust',
    UPLOAD_IMAGES: (id: string) => `/admin/products/${id}/images`,
  },

  // Category Management
  CATEGORIES: {
    LIST: '/admin/categories',
    CREATE: '/admin/categories',
    UPDATE: (id: string) => `/admin/categories/${id}`,
    DELETE: (id: string) => `/admin/categories/${id}`,
    STATISTICS: '/admin/categories/statistics',
    REORDER: '/admin/categories/reorder',
  },

  // Order Management
  ORDERS: {
    LIST: '/admin/orders',
    DETAILS: (id: string) => `/admin/orders/${id}`,
    UPDATE_STATUS: (id: string) => `/admin/orders/${id}/status`,
    CANCEL: (id: string) => `/admin/orders/${id}/cancel`,
    REFUND: (id: string) => `/admin/orders/${id}/refund`,
    SHIP: (id: string) => `/admin/orders/${id}/ship`,
    DELIVER: (id: string) => `/admin/orders/${id}/deliver`,
    ANALYTICS: '/admin/orders/analytics',
    EXPORT: '/admin/orders/export',
  },

  // Customer Management
  CUSTOMERS: {
    LIST: '/admin/customers',
    DETAILS: (id: string) => `/admin/customers/${id}`,
    UPDATE: (id: string) => `/admin/customers/${id}`,
    DELETE: (id: string) => `/admin/customers/${id}`,
    ORDERS: (id: string) => `/admin/customers/${id}/orders`,
    ANALYTICS: '/admin/customers/analytics',
    SEGMENTS: '/admin/customers/segments',
    EXPORT: '/admin/customers/export',
  },

  // Content Management
  CONTENT: {
    BANNERS: '/admin/content/banners',
    BANNER_UPLOAD: '/admin/content/banners/upload',
    BANNER_DELETE: (id: string) => `/admin/content/banners/${id}`,
    VIDEOS: '/admin/content/videos',
    VIDEO_UPLOAD: '/admin/content/videos/upload',
    VIDEO_DELETE: (id: string) => `/admin/content/videos/${id}`,
    MEDIA: '/admin/content/media',
    MEDIA_UPLOAD: '/admin/content/media/upload',
    MEDIA_DELETE: (id: string) => `/admin/content/media/${id}`,
  },

  // Analytics & Reports
  ANALYTICS: {
    SALES: '/admin/analytics/sales',
    CUSTOMERS: '/admin/analytics/customers',
    PRODUCTS: '/admin/analytics/products',
    REVENUE: '/admin/analytics/revenue',
    CONVERSION: '/admin/analytics/conversion',
    TRAFFIC: '/admin/analytics/traffic',
    REPORTS: '/admin/analytics/reports',
    CUSTOM_REPORT: '/admin/analytics/custom-report',
  },

  // Marketing & Promotions
  MARKETING: {
    COUPONS: '/admin/marketing/coupons',
    CAMPAIGNS: '/admin/marketing/campaigns',
    EMAILS: '/admin/marketing/emails',
    NOTIFICATIONS: '/admin/marketing/notifications',
    DISCOUNTS: '/admin/marketing/discounts',
  },

  // System Settings
  SETTINGS: {
    GENERAL: '/admin/settings/general',
    PAYMENT: '/admin/settings/payment',
    SHIPPING: '/admin/settings/shipping',
    TAX: '/admin/settings/tax',
    NOTIFICATIONS: '/admin/settings/notifications',
    USERS: '/admin/settings/users',
    BACKUP: '/admin/settings/backup',
    LOGS: '/admin/settings/logs',
  },

  // Inventory Management
  INVENTORY: {
    OVERVIEW: '/admin/inventory/overview',
    ADJUSTMENTS: '/admin/inventory/adjustments',
    LOW_STOCK: '/admin/inventory/low-stock',
    STOCK_HISTORY: '/admin/inventory/history',
    SUPPLIERS: '/admin/inventory/suppliers',
  },
} as const;

/**
 * Admin API Service object for consistent API calls
 */
export const adminApiService = {
  get: adminApiGet,
  post: adminApiPost,
  put: adminApiPut,
  delete: adminApiDelete,
  upload: adminApiUpload,
  call: adminApiCall,
};

/**
 * Helper function to build query string from object
 */
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(v => searchParams.append(key, v.toString()));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });
  
  return searchParams.toString();
};

/**
 * Helper function to handle file downloads
 */
export const downloadFile = async (endpoint: string, filename: string): Promise<void> => {
  try {
    const response = await fetch(`${ADMIN_API_BASE_URL}${endpoint}`, {
      ...defaultConfig,
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Download error:', error);
    throw error;
  }
};
