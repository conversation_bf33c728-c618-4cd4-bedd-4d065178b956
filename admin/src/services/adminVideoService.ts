/**
 * Admin Video Service - Video management API calls for admin panel
 */

import {
  adminApiGet,
  adminApiPost,
  adminApiPut,
  adminApiDelete,
  adminApiUpload,
  buildQueryString,
  type AdminApiResponse
} from './adminApiService';
// Define all types locally to avoid import issues
export type AdminVideo = {
  _id: string;
  title: string;
  description: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: string;
  category: 'product_showcase' | 'fashion_tips' | 'styling_guide' | 'behind_scenes' |
           'customer_stories' | 'brand_story' | 'tutorial' | 'collection_launch' |
           'seasonal' | 'promotional' | 'testimonial';
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  isPublished: boolean;
  displayOrder: number;
  autoplay: boolean;
  showControls: boolean;
  loop: boolean;
  muted: boolean;
  analytics: {
    views: number;
    likes: number;
    shares: number;
    engagement_rate: number;
    average_watch_time: number;
  };
  seoTitle?: string;
  seoDescription?: string;
  altText?: string;
  relatedProducts: string[];
  relatedCategories: string[];
  publishedAt?: string;
  scheduledFor?: string;
  expiresAt?: string;
  uploadedBy?: string;
  moderatedBy?: string;
  moderatedAt?: string;
  moderationNotes?: string;
  encoding?: {
    codec: string;
    bitrate: string;
    framerate: string;
  };
  uploadSource: 'admin_panel' | 'api' | 'bulk_upload' | 'mobile_app';
  captions: Array<{
    language: string;
    captionUrl: string;
    isDefault: boolean;
  }>;
  transcript?: string;
  createdAt: string;
  updatedAt: string;
}

export type AdminVideoCreate = {
  title: string;
  description: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: string;
  category: AdminVideo['category'];
  tags?: string[];
  isActive?: boolean;
  isFeatured?: boolean;
  isPublished?: boolean;
  displayOrder?: number;
  autoplay?: boolean;
  showControls?: boolean;
  loop?: boolean;
  muted?: boolean;
  seoTitle?: string;
  seoDescription?: string;
  altText?: string;
  relatedProducts?: string[];
  relatedCategories?: string[];
  publishedAt?: string;
  scheduledFor?: string;
  expiresAt?: string;
  moderationNotes?: string;
  encoding?: {
    codec: string;
    bitrate: string;
    framerate: string;
  };
  captions?: Array<{
    language: string;
    captionUrl: string;
    isDefault: boolean;
  }>;
  transcript?: string;
}

export type VideoFilters = {
  category?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  isPublished?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}



/**
 * Get all videos with filters
 */
export const getAdminVideos = async (filters?: VideoFilters): Promise<{
  videos: AdminVideo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  const queryString = filters ? buildQueryString(filters) : '';
  const endpoint = queryString ? `/admin/videos?${queryString}` : '/admin/videos';
  return adminApiGet(endpoint);
};

/**
 * Get single video by ID
 */
export const getAdminVideo = async (id: string): Promise<{ video: AdminVideo }> => {
  return adminApiGet(`/admin/videos/${id}`);
};

/**
 * Create new video
 */
export const createAdminVideo = async (videoData: AdminVideoCreate): Promise<{ video: AdminVideo }> => {
  return adminApiPost('/admin/videos', videoData);
};

/**
 * Update video
 */
export const updateAdminVideo = async (id: string, videoData: Partial<AdminVideoCreate>): Promise<{ video: AdminVideo }> => {
  return adminApiPut(`/admin/videos/${id}`, videoData);
};

/**
 * Delete video
 */
export const deleteAdminVideo = async (id: string): Promise<AdminApiResponse> => {
  return adminApiDelete(`/admin/videos/${id}`);
};

/**
 * Toggle video active status
 */
export const toggleVideoStatus = async (id: string): Promise<{ video: AdminVideo }> => {
  return adminApiPost(`/admin/videos/${id}/toggle-status`, {});
};

/**
 * Toggle video featured status
 */
export const toggleVideoFeatured = async (id: string): Promise<{ video: AdminVideo }> => {
  return adminApiPost(`/admin/videos/${id}/toggle-featured`, {});
};

/**
 * Toggle video published status
 */
export const toggleVideoPublished = async (id: string): Promise<{ video: AdminVideo }> => {
  return adminApiPost(`/admin/videos/${id}/toggle-published`, {});
};

/**
 * Update video display order
 */
export const updateVideoOrder = async (orderUpdates: Array<{ id: string; displayOrder: number }>): Promise<{ videos: AdminVideo[] }> => {
  return adminApiPost('/admin/videos/update-order', { orderUpdates });
};

/**
 * Upload video file
 */
export const uploadVideoFile = async (file: File, metadata: {
  title: string;
  description?: string;
  category: AdminVideo['category'];
  tags?: string[];
}): Promise<{ video: AdminVideo }> => {
  const formData = new FormData();
  formData.append('video', file);
  formData.append('metadata', JSON.stringify(metadata));
  
  return adminApiUpload('/admin/videos/upload', formData);
};

/**
 * Upload video thumbnail
 */
export const uploadVideoThumbnail = async (videoId: string, file: File): Promise<{ thumbnailUrl: string }> => {
  const formData = new FormData();
  formData.append('thumbnail', file);
  
  return adminApiUpload(`/admin/videos/${videoId}/thumbnail`, formData);
};

/**
 * Get video analytics
 */
export const getVideoAnalytics = async (id: string, dateRange?: { start: string; end: string }): Promise<{
  analytics: {
    views: number;
    likes: number;
    shares: number;
    engagement_rate: number;
    average_watch_time: number;
    view_duration_chart: Array<{ date: string; views: number; watch_time: number }>;
    geographic_data: Array<{ country: string; views: number }>;
    device_breakdown: Array<{ device: string; views: number }>;
  };
}> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `/admin/videos/${id}/analytics?${queryString}` : `/admin/videos/${id}/analytics`;
  return adminApiGet(endpoint);
};

/**
 * Get video statistics overview
 */
export const getVideoStatistics = async (): Promise<{
  statistics: {
    total_videos: number;
    published_videos: number;
    featured_videos: number;
    total_views: number;
    total_likes: number;
    total_shares: number;
    average_engagement_rate: number;
    top_performing: Array<{
      id: string;
      title: string;
      views: number;
      engagement_rate: number;
    }>;
    category_breakdown: Array<{
      category: string;
      count: number;
      views: number;
    }>;
  };
}> => {
  return adminApiGet('/admin/videos/statistics');
};

/**
 * Bulk update videos
 */
export const bulkUpdateVideos = async (videoIds: string[], updateData: Partial<AdminVideoCreate>): Promise<{ updated: number }> => {
  return adminApiPost('/admin/videos/bulk-update', { videoIds, updateData });
};

/**
 * Bulk delete videos
 */
export const bulkDeleteVideos = async (videoIds: string[]): Promise<{ deleted: number }> => {
  return adminApiPost('/admin/videos/bulk-delete', { videoIds });
};

/**
 * Export videos data
 */
export const exportVideos = async (format: 'csv' | 'json' = 'csv'): Promise<Blob> => {
  const response = await fetch(`/admin/videos/export?format=${format}`, {
    method: 'GET',
    credentials: 'include',
  });
  
  if (!response.ok) {
    throw new Error('Export failed');
  }
  
  return response.blob();
};

/**
 * Import videos from file
 */
export const importVideos = async (file: File): Promise<{ imported: number; errors: string[] }> => {
  const formData = new FormData();
  formData.append('file', file);
  
  return adminApiUpload('/admin/videos/import', formData);
};
