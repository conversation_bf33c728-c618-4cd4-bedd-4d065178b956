/**
 * Admin Customer Service - All customer management API calls for admin panel
 * Handles customer CRUD, analytics, segmentation, support
 */

import { 
  adminApiGet, 
  adminApiPost, 
  adminApiPut, 
  adminApiDelete,
  ADMIN_API_ENDPOINTS, 
  buildQueryString,
  downloadFile,
  type AdminApiResponse 
} from './adminApiService';

// Customer-related types
export interface AdminCustomer {
  _id: string;
  email?: string;
  phone?: string;
  isVerified: boolean;
  profile: {
    firstName?: string;
    lastName?: string;
    dateOfBirth?: string;
    gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
    profilePhoto?: {
      url: string;
      publicId: string;
    };
    bio?: string;
  };
  preferences: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
      marketing: boolean;
    };
  };
  addresses: Array<{
    _id: string;
    type: 'home' | 'work' | 'other';
    label?: string;
    fullName: string;
    phone: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    isDefault: boolean;
    isActive: boolean;
  }>;
  orderStats: {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: string;
  };
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminCustomerFilters {
  search?: string;
  status?: string;
  verified?: boolean;
  gender?: string;
  registration_date_from?: string;
  registration_date_to?: string;
  last_login_from?: string;
  last_login_to?: string;
  min_orders?: number;
  max_orders?: number;
  min_spent?: number;
  max_spent?: number;
  location?: string;
  segment?: string;
  sort_by?: 'name' | 'email' | 'totalSpent' | 'totalOrders' | 'lastLoginAt' | 'createdAt';
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface AdminCustomerUpdate {
  profile?: {
    firstName?: string;
    lastName?: string;
    dateOfBirth?: string;
    gender?: string;
    bio?: string;
  };
  email?: string;
  phone?: string;
  preferences?: {
    language?: string;
    currency?: string;
    notifications?: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
      marketing?: boolean;
    };
  };
  status?: 'active' | 'inactive' | 'suspended';
}

export interface AdminCustomerSegment {
  _id: string;
  name: string;
  description: string;
  criteria: {
    min_orders?: number;
    max_orders?: number;
    min_spent?: number;
    max_spent?: number;
    registration_period?: {
      start: string;
      end: string;
    };
    last_activity_period?: {
      start: string;
      end: string;
    };
    location?: string[];
    gender?: string[];
    age_range?: {
      min: number;
      max: number;
    };
  };
  customer_count: number;
  created_at: string;
  updated_at: string;
}

export interface AdminCustomerAnalytics {
  total_customers: number;
  new_customers_this_month: number;
  active_customers: number;
  customer_retention_rate: number;
  average_customer_lifetime_value: number;
  customer_acquisition_cost: number;
  demographics: {
    by_gender: Record<string, number>;
    by_age_group: Record<string, number>;
    by_location: Record<string, number>;
  };
  behavior: {
    average_orders_per_customer: number;
    repeat_customer_rate: number;
    customer_churn_rate: number;
  };
  growth: Array<{
    month: string;
    new_customers: number;
    total_customers: number;
  }>;
}

/**
 * Get all customers with admin filters and pagination
 */
export const getAdminCustomers = async (filters: AdminCustomerFilters = {}): Promise<{
  customers: AdminCustomer[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  const queryString = buildQueryString(filters);
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.CUSTOMERS.LIST}?${queryString}` : ADMIN_API_ENDPOINTS.CUSTOMERS.LIST;
  return adminApiGet(endpoint);
};

/**
 * Get single customer by ID
 */
export const getAdminCustomer = async (id: string): Promise<{ customer: AdminCustomer }> => {
  return adminApiGet(ADMIN_API_ENDPOINTS.CUSTOMERS.DETAILS(id));
};

/**
 * Update customer information
 */
export const updateAdminCustomer = async (id: string, customerData: AdminCustomerUpdate): Promise<{ customer: AdminCustomer }> => {
  return adminApiPut(ADMIN_API_ENDPOINTS.CUSTOMERS.UPDATE(id), customerData);
};

/**
 * Delete customer (soft delete)
 */
export const deleteAdminCustomer = async (id: string, reason?: string): Promise<AdminApiResponse> => {
  return adminApiDelete(ADMIN_API_ENDPOINTS.CUSTOMERS.DELETE(id));
};

/**
 * Get customer orders
 */
export const getAdminCustomerOrders = async (id: string, page: number = 1, limit: number = 10): Promise<{
  orders: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.CUSTOMERS.ORDERS(id)}?page=${page}&limit=${limit}`);
};

/**
 * Get customer analytics
 */
export const getAdminCustomerAnalytics = async (dateRange?: { start: string; end: string }): Promise<AdminCustomerAnalytics> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.CUSTOMERS.ANALYTICS}?${queryString}` : ADMIN_API_ENDPOINTS.CUSTOMERS.ANALYTICS;
  return adminApiGet(endpoint);
};

/**
 * Get customer segments
 */
export const getAdminCustomerSegments = async (): Promise<{ segments: AdminCustomerSegment[] }> => {
  return adminApiGet(ADMIN_API_ENDPOINTS.CUSTOMERS.SEGMENTS);
};

/**
 * Create customer segment
 */
export const createAdminCustomerSegment = async (segmentData: Omit<AdminCustomerSegment, '_id' | 'customer_count' | 'created_at' | 'updated_at'>): Promise<{ segment: AdminCustomerSegment }> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.CUSTOMERS.SEGMENTS, segmentData);
};

/**
 * Update customer segment
 */
export const updateAdminCustomerSegment = async (id: string, segmentData: Partial<AdminCustomerSegment>): Promise<{ segment: AdminCustomerSegment }> => {
  return adminApiPut(`${ADMIN_API_ENDPOINTS.CUSTOMERS.SEGMENTS}/${id}`, segmentData);
};

/**
 * Delete customer segment
 */
export const deleteAdminCustomerSegment = async (id: string): Promise<AdminApiResponse> => {
  return adminApiDelete(`${ADMIN_API_ENDPOINTS.CUSTOMERS.SEGMENTS}/${id}`);
};

/**
 * Export customers to CSV
 */
export const exportAdminCustomers = async (filters: AdminCustomerFilters = {}): Promise<void> => {
  const queryString = buildQueryString(filters);
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.CUSTOMERS.EXPORT}?${queryString}` : ADMIN_API_ENDPOINTS.CUSTOMERS.EXPORT;
  
  const filename = `customers_export_${new Date().toISOString().split('T')[0]}.csv`;
  return downloadFile(endpoint, filename);
};

/**
 * Get recent customers for dashboard
 */
export const getRecentAdminCustomers = async (limit: number = 10): Promise<{ customers: AdminCustomer[] }> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.DASHBOARD.RECENT_CUSTOMERS}?limit=${limit}`);
};

/**
 * Search customers
 */
export const searchAdminCustomers = async (query: string): Promise<{ customers: AdminCustomer[] }> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.CUSTOMERS.LIST}/search?q=${encodeURIComponent(query)}`);
};

/**
 * Get customer lifetime value
 */
export const getCustomerLifetimeValue = async (id: string): Promise<{
  total_spent: number;
  total_orders: number;
  average_order_value: number;
  predicted_ltv: number;
  customer_segment: string;
}> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.CUSTOMERS.DETAILS(id)}/lifetime-value`);
};

/**
 * Send customer notification
 */
export const sendCustomerNotification = async (id: string, notification: {
  type: 'email' | 'sms' | 'push';
  subject?: string;
  message: string;
  template?: string;
}): Promise<AdminApiResponse> => {
  return adminApiPost(`${ADMIN_API_ENDPOINTS.CUSTOMERS.DETAILS(id)}/notify`, notification);
};

/**
 * Bulk send notifications to customers
 */
export const bulkSendCustomerNotifications = async (customerIds: string[], notification: {
  type: 'email' | 'sms' | 'push';
  subject?: string;
  message: string;
  template?: string;
}): Promise<AdminApiResponse> => {
  return adminApiPost('/admin/customers/bulk-notify', {
    customer_ids: customerIds,
    notification
  });
};

/**
 * Get customer activity timeline
 */
export const getCustomerActivityTimeline = async (id: string): Promise<{
  activities: Array<{
    type: 'order' | 'login' | 'registration' | 'profile_update' | 'support_ticket';
    description: string;
    timestamp: string;
    metadata?: any;
  }>;
}> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.CUSTOMERS.DETAILS(id)}/activity`);
};

/**
 * Get customer support tickets
 */
export const getCustomerSupportTickets = async (id: string): Promise<{
  tickets: Array<{
    _id: string;
    subject: string;
    status: 'open' | 'in_progress' | 'resolved' | 'closed';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    created_at: string;
    updated_at: string;
  }>;
}> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.CUSTOMERS.DETAILS(id)}/support-tickets`);
};

/**
 * Suspend customer account
 */
export const suspendCustomerAccount = async (id: string, reason: string, duration?: number): Promise<AdminApiResponse> => {
  return adminApiPost(`${ADMIN_API_ENDPOINTS.CUSTOMERS.DETAILS(id)}/suspend`, {
    reason,
    duration_days: duration
  });
};

/**
 * Reactivate customer account
 */
export const reactivateCustomerAccount = async (id: string): Promise<AdminApiResponse> => {
  return adminApiPost(`${ADMIN_API_ENDPOINTS.CUSTOMERS.DETAILS(id)}/reactivate`);
};
