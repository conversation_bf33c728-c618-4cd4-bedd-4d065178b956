/**
 * Admin Content Service - All content management API calls for admin panel
 * Handles banners, videos, media uploads, campaigns
 */

import { 
  adminApiGet, 
  adminApiPost, 
  adminApiPut, 
  adminApiDelete,
  adminApiUpload,
  ADMIN_API_ENDPOINTS, 
  buildQueryString,
  type AdminApiResponse 
} from './adminApiService';

// Content-related types
export interface AdminBanner {
  _id: string;
  title: string;
  subtitle?: string;
  description?: string;
  type: 'main_home' | 'category' | 'promotional' | 'seasonal';
  images: {
    desktop: string;
    mobile: string;
    tablet?: string;
  };
  link?: {
    url: string;
    target: '_self' | '_blank';
    text?: string;
  };
  position: number;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  targetAudience?: {
    segments: string[];
    locations: string[];
  };
  analytics: {
    views: number;
    clicks: number;
    ctr: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AdminVideo {
  _id: string;
  title: string;
  description?: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: number;
  category: 'product' | 'promotional' | 'tutorial' | 'testimonial';
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  position: number;
  analytics: {
    views: number;
    engagement_rate: number;
    average_watch_time: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AdminMediaFile {
  _id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  category: 'image' | 'video' | 'document' | 'other';
  tags: string[];
  alt_text?: string;
  usage_count: number;
  uploaded_by: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminCampaign {
  _id: string;
  name: string;
  description?: string;
  type: 'sale' | 'promotion' | 'seasonal' | 'product_launch' | 'clearance';
  status: 'draft' | 'scheduled' | 'active' | 'paused' | 'completed' | 'cancelled';
  startDate: string;
  endDate: string;
  content: {
    banners: string[];
    videos: string[];
    emails: string[];
    notifications: string[];
  };
  targeting: {
    customer_segments: string[];
    locations: string[];
    demographics: {
      age_range?: { min: number; max: number };
      gender?: string[];
    };
  };
  budget?: {
    total: number;
    spent: number;
    remaining: number;
  };
  analytics: {
    reach: number;
    engagement: number;
    conversions: number;
    revenue: number;
    roi: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AdminBannerCreate {
  title: string;
  subtitle?: string;
  description?: string;
  type: AdminBanner['type'];
  link?: {
    url: string;
    target: '_self' | '_blank';
    text?: string;
  };
  position?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  targetAudience?: {
    segments: string[];
    locations: string[];
  };
}

export interface AdminVideoCreate {
  title: string;
  description?: string;
  category: AdminVideo['category'];
  tags?: string[];
  isActive?: boolean;
  isFeatured?: boolean;
  position?: number;
}

/**
 * Get all banners
 */
export const getAdminBanners = async (filters?: {
  type?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
}): Promise<{
  banners: AdminBanner[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  const queryString = filters ? buildQueryString(filters) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.CONTENT.BANNERS}?${queryString}` : ADMIN_API_ENDPOINTS.CONTENT.BANNERS;
  return adminApiGet(endpoint);
};

/**
 * Get single banner by ID
 */
export const getAdminBanner = async (id: string): Promise<{ banner: AdminBanner }> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.CONTENT.BANNERS}/${id}`);
};

/**
 * Create new banner
 */
export const createAdminBanner = async (bannerData: AdminBannerCreate): Promise<{ banner: AdminBanner }> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.CONTENT.BANNERS, bannerData);
};

/**
 * Update banner
 */
export const updateAdminBanner = async (id: string, bannerData: Partial<AdminBannerCreate>): Promise<{ banner: AdminBanner }> => {
  return adminApiPut(`${ADMIN_API_ENDPOINTS.CONTENT.BANNERS}/${id}`, bannerData);
};

/**
 * Delete banner
 */
export const deleteAdminBanner = async (id: string): Promise<AdminApiResponse> => {
  return adminApiDelete(ADMIN_API_ENDPOINTS.CONTENT.BANNER_DELETE(id));
};

/**
 * Upload banner images
 */
export const uploadAdminBannerImages = async (files: {
  desktop: File;
  mobile: File;
  tablet?: File;
}): Promise<{
  images: {
    desktop: string;
    mobile: string;
    tablet?: string;
  };
}> => {
  const formData = new FormData();
  formData.append('desktop', files.desktop);
  formData.append('mobile', files.mobile);
  if (files.tablet) {
    formData.append('tablet', files.tablet);
  }
  
  return adminApiUpload(ADMIN_API_ENDPOINTS.CONTENT.BANNER_UPLOAD, formData);
};

/**
 * Get all videos
 */
export const getAdminVideos = async (filters?: {
  category?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  page?: number;
  limit?: number;
}): Promise<{
  videos: AdminVideo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  const queryString = filters ? buildQueryString(filters) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.CONTENT.VIDEOS}?${queryString}` : ADMIN_API_ENDPOINTS.CONTENT.VIDEOS;
  return adminApiGet(endpoint);
};

/**
 * Get single video by ID
 */
export const getAdminVideo = async (id: string): Promise<{ video: AdminVideo }> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.CONTENT.VIDEOS}/${id}`);
};

/**
 * Create new video
 */
export const createAdminVideo = async (videoData: AdminVideoCreate): Promise<{ video: AdminVideo }> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.CONTENT.VIDEOS, videoData);
};

/**
 * Update video
 */
export const updateAdminVideo = async (id: string, videoData: Partial<AdminVideoCreate>): Promise<{ video: AdminVideo }> => {
  return adminApiPut(`${ADMIN_API_ENDPOINTS.CONTENT.VIDEOS}/${id}`, videoData);
};

/**
 * Delete video
 */
export const deleteAdminVideo = async (id: string): Promise<AdminApiResponse> => {
  return adminApiDelete(ADMIN_API_ENDPOINTS.CONTENT.VIDEO_DELETE(id));
};

/**
 * Upload video file
 */
export const uploadAdminVideo = async (file: File, metadata: {
  title: string;
  description?: string;
  category: AdminVideo['category'];
  tags?: string[];
}): Promise<{ video: AdminVideo }> => {
  const formData = new FormData();
  formData.append('video', file);
  formData.append('metadata', JSON.stringify(metadata));
  
  return adminApiUpload(ADMIN_API_ENDPOINTS.CONTENT.VIDEO_UPLOAD, formData);
};

/**
 * Get all media files
 */
export const getAdminMediaFiles = async (filters?: {
  category?: string;
  mimeType?: string;
  tags?: string[];
  page?: number;
  limit?: number;
}): Promise<{
  files: AdminMediaFile[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  const queryString = filters ? buildQueryString(filters) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.CONTENT.MEDIA}?${queryString}` : ADMIN_API_ENDPOINTS.CONTENT.MEDIA;
  return adminApiGet(endpoint);
};

/**
 * Upload media file
 */
export const uploadAdminMediaFile = async (file: File, metadata: {
  category?: string;
  tags?: string[];
  alt_text?: string;
}): Promise<{ file: AdminMediaFile }> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('metadata', JSON.stringify(metadata));
  
  return adminApiUpload(ADMIN_API_ENDPOINTS.CONTENT.MEDIA_UPLOAD, formData);
};

/**
 * Delete media file
 */
export const deleteAdminMediaFile = async (id: string): Promise<AdminApiResponse> => {
  return adminApiDelete(ADMIN_API_ENDPOINTS.CONTENT.MEDIA_DELETE(id));
};

/**
 * Get all campaigns
 */
export const getAdminCampaigns = async (filters?: {
  type?: string;
  status?: string;
  page?: number;
  limit?: number;
}): Promise<{
  campaigns: AdminCampaign[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  const queryString = filters ? buildQueryString(filters) : '';
  const endpoint = queryString ? `/admin/marketing/campaigns?${queryString}` : '/admin/marketing/campaigns';
  return adminApiGet(endpoint);
};

/**
 * Create new campaign
 */
export const createAdminCampaign = async (campaignData: Omit<AdminCampaign, '_id' | 'analytics' | 'createdAt' | 'updatedAt'>): Promise<{ campaign: AdminCampaign }> => {
  return adminApiPost('/admin/marketing/campaigns', campaignData);
};

/**
 * Update campaign
 */
export const updateAdminCampaign = async (id: string, campaignData: Partial<AdminCampaign>): Promise<{ campaign: AdminCampaign }> => {
  return adminApiPut(`/admin/marketing/campaigns/${id}`, campaignData);
};

/**
 * Delete campaign
 */
export const deleteAdminCampaign = async (id: string): Promise<AdminApiResponse> => {
  return adminApiDelete(`/admin/marketing/campaigns/${id}`);
};

/**
 * Get content analytics
 */
export const getAdminContentAnalytics = async (dateRange?: { start: string; end: string }): Promise<{
  banners: {
    total_views: number;
    total_clicks: number;
    average_ctr: number;
    top_performing: Array<{
      id: string;
      title: string;
      views: number;
      clicks: number;
      ctr: number;
    }>;
  };
  videos: {
    total_views: number;
    total_watch_time: number;
    average_engagement: number;
    top_performing: Array<{
      id: string;
      title: string;
      views: number;
      engagement_rate: number;
    }>;
  };
  campaigns: {
    total_reach: number;
    total_conversions: number;
    total_revenue: number;
    average_roi: number;
  };
}> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `/admin/content/analytics?${queryString}` : '/admin/content/analytics';
  return adminApiGet(endpoint);
};
