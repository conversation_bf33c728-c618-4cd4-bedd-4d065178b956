// TopHeader interfaces
export interface TopHeader {
  _id: string;
  text: string;
  isActive: boolean;
  order: number;
  backgroundColor: string;
  textColor: string;
  link?: string;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTopHeaderData {
  text: string;
  isActive?: boolean;
  order?: number;
  backgroundColor?: string;
  textColor?: string;
  link?: string;
  startDate?: string;
  endDate?: string;
}

export interface UpdateTopHeaderData extends Partial<CreateTopHeaderData> {}

// Simple API service for admin
const API_BASE_URL = 'http://localhost:3000/api';

class AdminTopHeaderService {
  // Get all top headers
  async getAllTopHeaders(): Promise<TopHeader[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/top-headers/admin/all`);
      if (!response.ok) throw new Error('Failed to fetch top headers');
      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error fetching all top headers:', error);
      throw error;
    }
  }

  // Get top header by ID
  async getTopHeaderById(id: string): Promise<TopHeader> {
    try {
      const response = await fetch(`${API_BASE_URL}/top-headers/admin/${id}`);
      if (!response.ok) throw new Error('Failed to fetch top header');
      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error fetching top header:', error);
      throw error;
    }
  }

  // Create new top header
  async createTopHeader(data: CreateTopHeaderData): Promise<TopHeader> {
    try {
      const response = await fetch(`${API_BASE_URL}/top-headers/admin`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error('Failed to create top header');
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error creating top header:', error);
      throw error;
    }
  }

  // Update top header
  async updateTopHeader(id: string, data: UpdateTopHeaderData): Promise<TopHeader> {
    try {
      const response = await fetch(`${API_BASE_URL}/top-headers/admin/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error('Failed to update top header');
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error updating top header:', error);
      throw error;
    }
  }

  // Delete top header
  async deleteTopHeader(id: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/top-headers/admin/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) throw new Error('Failed to delete top header');
    } catch (error) {
      console.error('Error deleting top header:', error);
      throw error;
    }
  }

  // Toggle active status
  async toggleActiveStatus(id: string): Promise<TopHeader> {
    try {
      const response = await fetch(`${API_BASE_URL}/top-headers/admin/${id}/toggle`, {
        method: 'PATCH'
      });
      if (!response.ok) throw new Error('Failed to toggle top header status');
      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error toggling top header status:', error);
      throw error;
    }
  }

  // Update order of multiple top headers
  async updateOrder(orderUpdates: { id: string; order: number }[]): Promise<TopHeader[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/top-headers/admin/order`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ orderUpdates })
      });
      if (!response.ok) throw new Error('Failed to update top header order');
      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error updating top header order:', error);
      throw error;
    }
  }
}

export default new AdminTopHeaderService();
