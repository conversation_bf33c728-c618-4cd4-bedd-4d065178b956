/**
 * Admin Product Service - All product management API calls for admin panel
 * Handles CRUD operations, bulk actions, inventory management
 */

import { 
  adminApiGet, 
  adminApiPost, 
  adminApiPut, 
  adminApiDelete, 
  adminApiUpload,
  ADMIN_API_ENDPOINTS, 
  buildQueryString,
  downloadFile,
  type AdminApiResponse 
} from './adminApiService';

// Product-related types
export interface AdminProduct {
  _id: string;
  product_id: string;
  name: string;
  slug: string;
  brand: string;
  category: {
    _id: string;
    name: string;
    slug: string;
  };
  subcategory?: string;
  description: string;
  short_description?: string;
  price: {
    original: number;
    current: number;
    currency: string;
    discount_percentage: number;
    savings: number;
  };
  colors: Array<{
    color_name: string;
    color_hex: string;
    image_url: string;
    is_available: boolean;
  }>;
  sizes: Array<{
    size_name: string;
    available_stock: number;
    is_available: boolean;
  }>;
  material?: string;
  style?: string;
  occasion?: string;
  season?: string;
  care_instructions?: string;
  tags: string[];
  rating: {
    average_rating: number;
    reviews_count: number;
    rating_breakdown: {
      "5_star": number;
      "4_star": number;
      "3_star": number;
      "2_star": number;
      "1_star": number;
    };
  };
  availability: 'In Stock' | 'Out of Stock' | 'Pre-order' | 'Discontinued';
  stock_status: 'Good Stock' | 'Limited Stock' | 'Low Stock' | 'Out of Stock';
  inventory: {
    total_stock: number;
    reserved_stock: number;
    available_stock: number;
    low_stock_threshold: number;
    track_inventory: boolean;
  };
  delivery_info: {
    free_shipping: boolean;
    delivery_time: string;
    delivery_cost: number;
    express_delivery?: {
      available: boolean;
      cost: number;
      delivery_time: string;
    };
  };
  product_images: Array<{
    image_url: string;
    alt_text: string;
    is_primary: boolean;
    sort_order: number;
  }>;
  related_products: string[];
  social_proof: {
    likes: number;
    shares: number;
    comments: number;
    wishlist_count: number;
    view_count: number;
  };
  seo: {
    meta_title?: string;
    meta_description?: string;
    keywords: string[];
    canonical_url?: string;
  };
  product_url: string;
  video_url?: string;
  is_featured: boolean;
  is_bestseller: boolean;
  is_new_arrival: boolean;
  is_trending: boolean;
  is_active: boolean;
  created_by?: string;
  updated_by?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductFilters {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  subcategory?: string;
  brand?: string;
  status?: string;
  is_featured?: boolean;
  is_bestseller?: boolean;
  is_new_arrival?: boolean;
  is_trending?: boolean;
  low_stock?: boolean;
  min_price?: number;
  max_price?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface ProductStatistics {
  overview: {
    totalProducts: number;
    activeProducts: number;
    featuredProducts: number;
    bestsellerProducts: number;
    newArrivalProducts: number;
    trendingProducts: number;
    outOfStockProducts: number;
    lowStockProducts: number;
    averagePrice: number;
    totalInventoryValue: number;
  };
  categoryDistribution: Array<{
    _id: string;
    count: number;
  }>;
  topBrands: Array<{
    _id: string;
    count: number;
  }>;
}

export interface BulkImportResult {
  imported_count: number;
  error_count: number;
  errors: string[];
  total_errors: number;
}

export interface InventoryAdjustment {
  productId: string;
  adjustment: number;
  reason?: string;
}

class AdminProductService {
  /**
   * Get all products with filters and pagination
   */
  static async getAllProducts(filters: ProductFilters = {}): Promise<AdminApiResponse<{
    products: AdminProduct[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalProducts: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  }>> {
    const queryString = buildQueryString(filters);
    return adminApiGet(`${ADMIN_API_ENDPOINTS.PRODUCTS.LIST}${queryString ? '?' + queryString : ''}`);
  }

  /**
   * Get single product by ID
   */
  static async getProductById(id: string): Promise<AdminApiResponse<{ product: AdminProduct }>> {
    return adminApiGet(`${ADMIN_API_ENDPOINTS.PRODUCTS.LIST}/${id}`);
  }

  /**
   * Create new product
   */
  static async createProduct(productData: Partial<AdminProduct>): Promise<AdminApiResponse<{ product: AdminProduct }>> {
    return adminApiPost(ADMIN_API_ENDPOINTS.PRODUCTS.CREATE, productData);
  }

  /**
   * Update existing product
   */
  static async updateProduct(id: string, productData: Partial<AdminProduct>): Promise<AdminApiResponse<{ product: AdminProduct }>> {
    return adminApiPut(ADMIN_API_ENDPOINTS.PRODUCTS.UPDATE(id), productData);
  }

  /**
   * Delete product
   */
  static async deleteProduct(id: string): Promise<AdminApiResponse<void>> {
    return adminApiDelete(ADMIN_API_ENDPOINTS.PRODUCTS.DELETE(id));
  }

  /**
   * Bulk delete products
   */
  static async bulkDeleteProducts(productIds: string[]): Promise<AdminApiResponse<{ deletedCount: number }>> {
    return adminApiPost(ADMIN_API_ENDPOINTS.PRODUCTS.BULK_DELETE, { productIds });
  }

  /**
   * Bulk update products
   */
  static async bulkUpdateProducts(productIds: string[], updateData: Partial<AdminProduct>): Promise<AdminApiResponse<{ modifiedCount: number }>> {
    return adminApiPost(ADMIN_API_ENDPOINTS.PRODUCTS.BULK_UPDATE, { productIds, updateData });
  }

  /**
   * Get product statistics
   */
  static async getProductStatistics(): Promise<AdminApiResponse<ProductStatistics>> {
    return adminApiGet(ADMIN_API_ENDPOINTS.PRODUCTS.STATISTICS);
  }

  /**
   * Get low stock products
   */
  static async getLowStockProducts(threshold: number = 10, page: number = 1, limit: number = 20): Promise<AdminApiResponse<{
    products: AdminProduct[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalProducts: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  }>> {
    const queryString = buildQueryString({ threshold, page, limit });
    return adminApiGet(`${ADMIN_API_ENDPOINTS.PRODUCTS.LOW_STOCK}${queryString ? '?' + queryString : ''}`);
  }

  /**
   * Adjust inventory
   */
  static async adjustInventory(adjustment: InventoryAdjustment): Promise<AdminApiResponse<{
    product_id: string;
    name: string;
    previous_stock: number;
    current_stock: number;
    adjustment: number;
    reason?: string;
  }>> {
    return adminApiPost(ADMIN_API_ENDPOINTS.PRODUCTS.INVENTORY_ADJUST, adjustment);
  }

  /**
   * Bulk import products from CSV/Excel
   */
  static async bulkImportProducts(file: File, categoryId?: string): Promise<AdminApiResponse<BulkImportResult>> {
    const formData = new FormData();
    formData.append('bulk_file', file);
    if (categoryId) {
      formData.append('category_id', categoryId);
    }

    return adminApiUpload(ADMIN_API_ENDPOINTS.PRODUCTS.IMPORT, formData);
  }

  /**
   * Upload product images
   */
  static async uploadProductImages(productId: string, images: File[]): Promise<AdminApiResponse<{
    uploaded_images: Array<{
      image_url: string;
      alt_text: string;
      is_primary: boolean;
      sort_order: number;
    }>;
    total_images: number;
  }>> {
    const formData = new FormData();
    images.forEach(image => {
      formData.append('images', image);
    });

    return adminApiUpload(ADMIN_API_ENDPOINTS.PRODUCTS.UPLOAD_IMAGES(productId), formData);
  }

  /**
   * Export products to CSV
   */
  static async exportProducts(filters: { category?: string; brand?: string; status?: string } = {}): Promise<void> {
    const queryString = buildQueryString(filters);
    return downloadFile(`${ADMIN_API_ENDPOINTS.PRODUCTS.EXPORT}${queryString ? '?' + queryString : ''}`, 'products_export.csv');
  }

  /**
   * Download CSV template for bulk upload
   */
  static async downloadCSVTemplate(): Promise<void> {
    return downloadFile(ADMIN_API_ENDPOINTS.PRODUCTS.TEMPLATE, 'product_upload_template.csv');
  }
}

export default AdminProductService;
