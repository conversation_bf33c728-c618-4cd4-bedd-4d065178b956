/**
 * Admin Order Service - All order management API calls for admin panel
 * Handles order processing, status updates, refunds, analytics
 */

import { 
  adminApiGet, 
  adminApiPost, 
  adminApiPut, 
  ADMIN_API_ENDPOINTS, 
  buildQueryString,
  downloadFile,
  type AdminApiResponse 
} from './adminApiService';

// Order-related types
export interface AdminOrder {
  _id: string;
  orderNumber: string;
  userId: {
    _id: string;
    profile: {
      firstName: string;
      lastName: string;
    };
    email: string;
    phone: string;
  };
  items: Array<{
    productId: string;
    productDetails: {
      name: string;
      image: string;
      sku: string;
      brand: string;
    };
    variant: {
      size?: string;
      color?: string;
    };
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  totals: {
    subtotal: number;
    discount: number;
    tax: number;
    shipping: number;
    total: number;
  };
  shippingAddress: {
    fullName: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    phone: string;
  };
  billingAddress: {
    fullName: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    phone: string;
  };
  paymentMethod: {
    type: string;
    status: string;
    transactionId?: string;
  };
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  tracking: {
    trackingNumber?: string;
    carrier?: string;
    estimatedDelivery?: string;
    trackingUrl?: string;
  };
  timeline: Array<{
    status: string;
    timestamp: string;
    note?: string;
    updatedBy?: string;
  }>;
  notes?: string;
  internalNotes?: string;
  refund?: {
    amount: number;
    reason: string;
    status: 'pending' | 'processed' | 'failed';
    processedAt?: string;
    refundId?: string;
  };
  placedAt: string;
  confirmedAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
  cancelledAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminOrderFilters {
  search?: string;
  status?: string;
  payment_status?: string;
  date_from?: string;
  date_to?: string;
  customer_id?: string;
  min_amount?: number;
  max_amount?: number;
  shipping_method?: string;
  sort_by?: 'orderNumber' | 'total' | 'placedAt' | 'status';
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface AdminOrderStatusUpdate {
  status: AdminOrder['status'];
  notes?: string;
  tracking?: {
    trackingNumber?: string;
    carrier?: string;
    estimatedDelivery?: string;
  };
  notifyCustomer?: boolean;
}

export interface AdminOrderRefund {
  amount: number;
  reason: string;
  refund_shipping?: boolean;
  notify_customer?: boolean;
  internal_notes?: string;
}

export interface AdminOrderAnalytics {
  total_orders: number;
  total_revenue: number;
  average_order_value: number;
  orders_by_status: Record<string, number>;
  revenue_by_month: Array<{
    month: string;
    revenue: number;
    orders: number;
  }>;
  top_products: Array<{
    product_id: string;
    name: string;
    quantity_sold: number;
    revenue: number;
  }>;
  customer_segments: Array<{
    segment: string;
    orders: number;
    revenue: number;
  }>;
}

/**
 * Get all orders with admin filters and pagination
 */
export const getAdminOrders = async (filters: AdminOrderFilters = {}): Promise<{
  orders: AdminOrder[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  const queryString = buildQueryString(filters);
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ORDERS.LIST}?${queryString}` : ADMIN_API_ENDPOINTS.ORDERS.LIST;
  return adminApiGet(endpoint);
};

/**
 * Get single order by ID
 */
export const getAdminOrder = async (id: string): Promise<{ order: AdminOrder }> => {
  return adminApiGet(ADMIN_API_ENDPOINTS.ORDERS.DETAILS(id));
};

/**
 * Get single order by ID (alias for compatibility)
 */
export const getAdminOrderById = getAdminOrder;

/**
 * Update order status
 */
export const updateAdminOrderStatus = async (id: string, statusData: AdminOrderStatusUpdate): Promise<{ order: AdminOrder }> => {
  return adminApiPut(ADMIN_API_ENDPOINTS.ORDERS.UPDATE_STATUS(id), statusData);
};

/**
 * Cancel order
 */
export const cancelAdminOrder = async (id: string, reason: string, notifyCustomer: boolean = true): Promise<{ order: AdminOrder }> => {
  return adminApiPut(ADMIN_API_ENDPOINTS.ORDERS.CANCEL(id), { 
    reason, 
    notify_customer: notifyCustomer 
  });
};

/**
 * Process order refund
 */
export const refundAdminOrder = async (id: string, refundData: AdminOrderRefund): Promise<{ order: AdminOrder; refund: any }> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.ORDERS.REFUND(id), refundData);
};

/**
 * Mark order as shipped
 */
export const shipAdminOrder = async (id: string, shippingData: {
  trackingNumber: string;
  carrier: string;
  estimatedDelivery?: string;
  notifyCustomer?: boolean;
}): Promise<{ order: AdminOrder }> => {
  return adminApiPut(ADMIN_API_ENDPOINTS.ORDERS.SHIP(id), shippingData);
};

/**
 * Mark order as delivered
 */
export const deliverAdminOrder = async (id: string, deliveryData: {
  deliveredAt?: string;
  deliveryNotes?: string;
  notifyCustomer?: boolean;
}): Promise<{ order: AdminOrder }> => {
  return adminApiPut(ADMIN_API_ENDPOINTS.ORDERS.DELIVER(id), deliveryData);
};

/**
 * Get order analytics
 */
export const getAdminOrderAnalytics = async (dateRange?: { start: string; end: string }): Promise<AdminOrderAnalytics> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ORDERS.ANALYTICS}?${queryString}` : ADMIN_API_ENDPOINTS.ORDERS.ANALYTICS;
  return adminApiGet(endpoint);
};

/**
 * Export orders to CSV
 */
export const exportAdminOrders = async (filters: AdminOrderFilters = {}): Promise<void> => {
  const queryString = buildQueryString(filters);
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ORDERS.EXPORT}?${queryString}` : ADMIN_API_ENDPOINTS.ORDERS.EXPORT;
  
  const filename = `orders_export_${new Date().toISOString().split('T')[0]}.csv`;
  return downloadFile(endpoint, filename);
};

/**
 * Get recent orders for dashboard
 */
export const getRecentAdminOrders = async (limit: number = 10): Promise<{ orders: AdminOrder[] }> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.DASHBOARD.RECENT_ORDERS}?limit=${limit}`);
};

/**
 * Get order statistics for dashboard
 */
export const getAdminOrderStats = async (): Promise<{
  today: {
    orders: number;
    revenue: number;
  };
  week: {
    orders: number;
    revenue: number;
  };
  month: {
    orders: number;
    revenue: number;
  };
  pending_orders: number;
  processing_orders: number;
  shipped_orders: number;
}> => {
  return adminApiGet('/admin/dashboard/order-stats');
};

/**
 * Search orders
 */
export const searchAdminOrders = async (query: string): Promise<{ orders: AdminOrder[] }> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.ORDERS.LIST}/search?q=${encodeURIComponent(query)}`);
};

/**
 * Get order timeline
 */
export const getAdminOrderTimeline = async (id: string): Promise<{
  timeline: Array<{
    status: string;
    timestamp: string;
    note?: string;
    updatedBy?: string;
  }>;
}> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.ORDERS.DETAILS(id)}/timeline`);
};

/**
 * Add note to order
 */
export const addAdminOrderNote = async (id: string, note: string, isInternal: boolean = false): Promise<{ order: AdminOrder }> => {
  return adminApiPost(`${ADMIN_API_ENDPOINTS.ORDERS.DETAILS(id)}/notes`, {
    note,
    is_internal: isInternal
  });
};

/**
 * Update order tracking information
 */
export const updateAdminOrderTracking = async (id: string, trackingData: {
  trackingNumber: string;
  carrier: string;
  estimatedDelivery?: string;
  trackingUrl?: string;
  notes?: string;
}): Promise<{ order: AdminOrder }> => {
  return adminApiPut(`/admin/orders/${id}/tracking`, trackingData);
};

// Remove duplicate functions - using the ones defined above

/**
 * Send order notification to customer
 */
export const sendOrderNotification = async (id: string, type: 'status_update' | 'shipping' | 'delivery' | 'custom', message?: string): Promise<AdminApiResponse> => {
  return adminApiPost(`${ADMIN_API_ENDPOINTS.ORDERS.DETAILS(id)}/notify`, {
    type,
    message
  });
};

/**
 * Get order fulfillment metrics
 */
export const getOrderFulfillmentMetrics = async (dateRange?: { start: string; end: string }): Promise<{
  average_processing_time: number;
  average_shipping_time: number;
  average_delivery_time: number;
  fulfillment_rate: number;
  on_time_delivery_rate: number;
}> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `/admin/orders/fulfillment-metrics?${queryString}` : '/admin/orders/fulfillment-metrics';
  return adminApiGet(endpoint);
};

/**
 * Get orders by status count
 */
export const getOrdersByStatusCount = async (): Promise<Record<AdminOrder['status'], number>> => {
  return adminApiGet('/admin/orders/status-count');
};
