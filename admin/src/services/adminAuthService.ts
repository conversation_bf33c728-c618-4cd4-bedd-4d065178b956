/**
 * Admin Authentication Service - All admin auth-related API calls
 * Handles admin login, logout, verification, and profile management
 * Enhanced with JWT token management
 */

import { adminApiGet, adminApiPost, ADMIN_API_ENDPOINTS, type AdminApiResponse } from './adminApiService';

// Admin-related types
export interface AdminUser {
  id: string;
  username: string;
  email: string;
  role: 'super_admin' | 'admin' | 'manager';
  permissions: string[];
  profile: {
    firstName: string;
    lastName: string;
    avatar?: string;
    phone?: string;
  };
  twoFactorAuth: {
    enabled: boolean;
    secret?: string;
  };
  lastLoginAt?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AdminAuthResponse {
  success: boolean;
  message: string;
  user: AdminUser;
  token?: string; // JWT token for admin panel
  expiresAt?: number; // Token expiration timestamp
  expiresIn?: string; // Token expiration duration (e.g., "7d")
  rememberMe?: boolean; // Whether remember me was selected
  requiresTwoFactor?: boolean;
}

export interface AdminLoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface AdminChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface AdminProfileUpdateRequest {
  profile: {
    firstName?: string;
    lastName?: string;
    phone?: string;
  };
  email?: string;
}

export interface Admin2FARequest {
  token: string;
}

export interface Admin2FASetupResponse {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

/**
 * Admin login with username/password - Cookie-based authentication
 * No need to store tokens manually, backend sets httpOnly cookies
 */
export const adminLogin = async (data: AdminLoginRequest): Promise<AdminAuthResponse> => {
  const response = await adminApiPost<AdminAuthResponse>(ADMIN_API_ENDPOINTS.AUTH.LOGIN, data);

  // With cookie-based auth, we only store user data locally for quick access
  // The JWT token is automatically stored in httpOnly cookie by backend
  if (response.success && response.user) {
    // Store only user data for UI purposes (not sensitive token data)
    localStorage.setItem('admin_user', JSON.stringify(response.user));

    console.log('Admin login successful:', {
      user: response.user.username,
      role: response.user.role,
      expiresAt: response.expiresAt ? new Date(response.expiresAt) : 'Unknown',
      rememberMe: response.rememberMe
    });
  }

  return response;
};

/**
 * Admin logout and clear session - Cookie-based
 */
export const adminLogout = async (): Promise<AdminApiResponse> => {
  try {
    // Call backend logout endpoint (clears httpOnly cookie)
    const response = await adminApiPost(ADMIN_API_ENDPOINTS.AUTH.LOGOUT);

    // Clear local user data
    localStorage.removeItem('admin_user');

    return response;
  } catch (error) {
    // Clear local storage even if backend call fails
    localStorage.removeItem('admin_user');
    throw error;
  }
};

/**
 * Verify admin authentication token - called on app startup
 */
export const verifyAdminAuth = async (): Promise<AdminAuthResponse> => {
  return adminApiGet(ADMIN_API_ENDPOINTS.AUTH.VERIFY);
};

/**
 * Refresh admin authentication token
 */
export const refreshAdminAuth = async (): Promise<AdminAuthResponse> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.AUTH.REFRESH);
};

/**
 * Get current admin profile
 */
export const getAdminProfile = async (): Promise<{ user: AdminUser }> => {
  return adminApiGet(ADMIN_API_ENDPOINTS.AUTH.PROFILE);
};

/**
 * Update admin profile
 */
export const updateAdminProfile = async (data: AdminProfileUpdateRequest): Promise<{ user: AdminUser }> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.AUTH.PROFILE, data);
};

/**
 * Change admin password
 */
export const changeAdminPassword = async (data: AdminChangePasswordRequest): Promise<AdminApiResponse> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.AUTH.CHANGE_PASSWORD, data);
};

/**
 * Enable Two-Factor Authentication
 */
export const enableAdmin2FA = async (): Promise<Admin2FASetupResponse> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.AUTH.ENABLE_2FA);
};

/**
 * Verify Two-Factor Authentication setup
 */
export const verifyAdmin2FA = async (data: Admin2FARequest): Promise<AdminApiResponse> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.AUTH.VERIFY_2FA, data);
};

/**
 * Disable Two-Factor Authentication
 */
export const disableAdmin2FA = async (data: Admin2FARequest): Promise<AdminApiResponse> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.AUTH.DISABLE_2FA, data);
};

/**
 * Check if admin is authenticated (without throwing errors) - Cookie-based
 */
export const checkAdminAuthStatus = async (): Promise<{ isAuthenticated: boolean; user?: AdminUser }> => {
  try {
    // Try to verify with backend (cookie is sent automatically)
    const response = await verifyAdminAuth();

    // Update local user data if verification successful
    if (response.success && response.user) {
      localStorage.setItem('admin_user', JSON.stringify(response.user));
    }

    return {
      isAuthenticated: true,
      user: response.user,
    };
  } catch (error) {
    // Clear local user data on auth failure
    localStorage.removeItem('admin_user');
    return {
      isAuthenticated: false,
    };
  }
};

/**
 * Admin permission checker
 */
export const hasAdminPermission = (user: AdminUser | null, permission: string): boolean => {
  if (!user) return false;
  
  // Super admin has all permissions
  if (user.role === 'super_admin') return true;
  
  // Check specific permission
  return user.permissions.includes(permission);
};

/**
 * Admin role checker
 */
export const hasAdminRole = (user: AdminUser | null, role: AdminUser['role']): boolean => {
  if (!user) return false;
  return user.role === role;
};

/**
 * Check if admin can access resource
 */
export const canAccessResource = (user: AdminUser | null, resource: string, action: string): boolean => {
  if (!user) return false;

  // Super admin can access everything
  if (user.role === 'super_admin') return true;

  // Check specific permission
  const permission = `${resource}:${action}`;
  return user.permissions.includes(permission);
};

/**
 * Get stored admin user without API call
 */
export const getStoredAdminUser = (): AdminUser | null => {
  try {
    const userData = localStorage.getItem('admin_user');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Failed to get stored admin user:', error);
    return null;
  }
};

/**
 * Check if admin is authenticated locally (without API call) - Cookie-based
 * Note: This only checks if we have user data, actual auth is verified by backend via cookies
 */
export const isAdminAuthenticatedLocally = (): boolean => {
  return getStoredAdminUser() !== null;
};

/**
 * Get admin token expiry time - Not applicable for cookie-based auth
 * @deprecated Use cookie-based authentication instead
 */
export const getAdminTokenExpiry = (): number => {
  console.warn('getAdminTokenExpiry is deprecated with cookie-based auth');
  return 0;
};

/**
 * Force clear admin authentication - Cookie-based
 */
export const clearAdminAuth = (): void => {
  localStorage.removeItem('admin_user');
  // Note: httpOnly cookie will be cleared by backend on logout
};

/**
 * Update stored admin user data - Cookie-based
 */
export const updateStoredAdminUser = (user: AdminUser): void => {
  localStorage.setItem('admin_user', JSON.stringify(user));
};

/**
 * Admin permissions constants
 */
export const ADMIN_PERMISSIONS = {
  // Product permissions
  PRODUCTS_VIEW: 'products:view',
  PRODUCTS_CREATE: 'products:create',
  PRODUCTS_UPDATE: 'products:update',
  PRODUCTS_DELETE: 'products:delete',
  PRODUCTS_BULK: 'products:bulk',
  
  // Order permissions
  ORDERS_VIEW: 'orders:view',
  ORDERS_UPDATE: 'orders:update',
  ORDERS_CANCEL: 'orders:cancel',
  ORDERS_REFUND: 'orders:refund',
  ORDERS_EXPORT: 'orders:export',
  
  // Customer permissions
  CUSTOMERS_VIEW: 'customers:view',
  CUSTOMERS_UPDATE: 'customers:update',
  CUSTOMERS_DELETE: 'customers:delete',
  CUSTOMERS_EXPORT: 'customers:export',
  
  // Content permissions
  CONTENT_VIEW: 'content:view',
  CONTENT_CREATE: 'content:create',
  CONTENT_UPDATE: 'content:update',
  CONTENT_DELETE: 'content:delete',
  
  // Analytics permissions
  ANALYTICS_VIEW: 'analytics:view',
  ANALYTICS_EXPORT: 'analytics:export',
  
  // Marketing permissions
  MARKETING_VIEW: 'marketing:view',
  MARKETING_CREATE: 'marketing:create',
  MARKETING_UPDATE: 'marketing:update',
  MARKETING_DELETE: 'marketing:delete',
  
  // Settings permissions
  SETTINGS_VIEW: 'settings:view',
  SETTINGS_UPDATE: 'settings:update',
  SETTINGS_USERS: 'settings:users',
  SETTINGS_SYSTEM: 'settings:system',
  
  // Inventory permissions
  INVENTORY_VIEW: 'inventory:view',
  INVENTORY_UPDATE: 'inventory:update',
  INVENTORY_ADJUST: 'inventory:adjust',
} as const;

/**
 * Default permissions by role
 */
export const DEFAULT_ROLE_PERMISSIONS = {
  super_admin: Object.values(ADMIN_PERMISSIONS),
  admin: [
    ADMIN_PERMISSIONS.PRODUCTS_VIEW,
    ADMIN_PERMISSIONS.PRODUCTS_CREATE,
    ADMIN_PERMISSIONS.PRODUCTS_UPDATE,
    ADMIN_PERMISSIONS.ORDERS_VIEW,
    ADMIN_PERMISSIONS.ORDERS_UPDATE,
    ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
    ADMIN_PERMISSIONS.CUSTOMERS_UPDATE,
    ADMIN_PERMISSIONS.CONTENT_VIEW,
    ADMIN_PERMISSIONS.CONTENT_CREATE,
    ADMIN_PERMISSIONS.CONTENT_UPDATE,
    ADMIN_PERMISSIONS.ANALYTICS_VIEW,
    ADMIN_PERMISSIONS.MARKETING_VIEW,
    ADMIN_PERMISSIONS.MARKETING_CREATE,
    ADMIN_PERMISSIONS.MARKETING_UPDATE,
    ADMIN_PERMISSIONS.INVENTORY_VIEW,
    ADMIN_PERMISSIONS.INVENTORY_UPDATE,
  ],
  manager: [
    ADMIN_PERMISSIONS.PRODUCTS_VIEW,
    ADMIN_PERMISSIONS.ORDERS_VIEW,
    ADMIN_PERMISSIONS.ORDERS_UPDATE,
    ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
    ADMIN_PERMISSIONS.CONTENT_VIEW,
    ADMIN_PERMISSIONS.ANALYTICS_VIEW,
    ADMIN_PERMISSIONS.INVENTORY_VIEW,
  ],
} as const;

/**
 * Get user display name
 */
export const getAdminDisplayName = (user: AdminUser | null): string => {
  if (!user) return 'Unknown User';
  
  const { firstName, lastName } = user.profile;
  if (firstName && lastName) {
    return `${firstName} ${lastName}`;
  }
  if (firstName) {
    return firstName;
  }
  return user.username;
};

/**
 * Get user initials for avatar
 */
export const getAdminInitials = (user: AdminUser | null): string => {
  if (!user) return 'U';
  
  const { firstName, lastName } = user.profile;
  if (firstName && lastName) {
    return `${firstName[0]}${lastName[0]}`.toUpperCase();
  }
  if (firstName) {
    return firstName[0].toUpperCase();
  }
  return user.username[0].toUpperCase();
};
