/**
 * Admin Analytics Service - All analytics and reporting API calls for admin panel
 * Handles dashboard metrics, sales analytics, customer insights, reports
 */

import { 
  adminApiGet, 
  adminApiPost, 
  ADMIN_API_ENDPOINTS, 
  buildQueryString,
  downloadFile,
  type AdminApiResponse 
} from './adminApiService';

// Analytics-related types
export interface AdminDashboardStats {
  sales: {
    today: number;
    yesterday: number;
    this_week: number;
    last_week: number;
    this_month: number;
    last_month: number;
    this_year: number;
    growth_rate: number;
  };
  orders: {
    today: number;
    yesterday: number;
    this_week: number;
    last_week: number;
    this_month: number;
    last_month: number;
    pending: number;
    processing: number;
    shipped: number;
    delivered: number;
    cancelled: number;
    growth_rate: number;
  };
  customers: {
    total: number;
    new_today: number;
    new_this_week: number;
    new_this_month: number;
    active_customers: number;
    growth_rate: number;
  };
  products: {
    total: number;
    active: number;
    low_stock: number;
    out_of_stock: number;
    top_selling: Array<{
      id: string;
      name: string;
      sales: number;
    }>;
  };
  revenue: {
    total: number;
    average_order_value: number;
    conversion_rate: number;
    refund_rate: number;
  };
}

export interface AdminSalesAnalytics {
  overview: {
    total_revenue: number;
    total_orders: number;
    average_order_value: number;
    conversion_rate: number;
    growth_rate: number;
  };
  revenue_by_period: Array<{
    period: string;
    revenue: number;
    orders: number;
    average_order_value: number;
  }>;
  revenue_by_category: Array<{
    category: string;
    revenue: number;
    percentage: number;
  }>;
  revenue_by_product: Array<{
    product_id: string;
    name: string;
    revenue: number;
    quantity_sold: number;
  }>;
  payment_methods: Array<{
    method: string;
    count: number;
    revenue: number;
    percentage: number;
  }>;
  geographic_distribution: Array<{
    location: string;
    orders: number;
    revenue: number;
  }>;
}

export interface AdminCustomerAnalytics {
  overview: {
    total_customers: number;
    new_customers: number;
    active_customers: number;
    retention_rate: number;
    churn_rate: number;
    lifetime_value: number;
  };
  acquisition: Array<{
    period: string;
    new_customers: number;
    acquisition_cost: number;
  }>;
  demographics: {
    by_age: Record<string, number>;
    by_gender: Record<string, number>;
    by_location: Record<string, number>;
  };
  behavior: {
    repeat_purchase_rate: number;
    average_time_between_orders: number;
    most_popular_categories: Array<{
      category: string;
      customers: number;
    }>;
  };
  segments: Array<{
    segment: string;
    count: number;
    revenue: number;
    average_order_value: number;
  }>;
}

export interface AdminProductAnalytics {
  overview: {
    total_products: number;
    active_products: number;
    best_performers: number;
    underperformers: number;
  };
  performance: Array<{
    product_id: string;
    name: string;
    category: string;
    views: number;
    sales: number;
    revenue: number;
    conversion_rate: number;
    stock_level: number;
  }>;
  category_performance: Array<{
    category: string;
    products: number;
    sales: number;
    revenue: number;
    average_rating: number;
  }>;
  inventory_insights: {
    low_stock_products: number;
    out_of_stock_products: number;
    overstock_products: number;
    total_inventory_value: number;
  };
  trends: Array<{
    period: string;
    new_products: number;
    discontinued_products: number;
  }>;
}

export interface AdminCustomReport {
  name: string;
  description?: string;
  type: 'sales' | 'customers' | 'products' | 'inventory' | 'custom';
  filters: {
    date_range?: {
      start: string;
      end: string;
    };
    categories?: string[];
    products?: string[];
    customers?: string[];
    status?: string[];
    [key: string]: any;
  };
  metrics: string[];
  groupBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  format: 'json' | 'csv' | 'pdf';
}

/**
 * Get dashboard statistics
 */
export const getAdminDashboardStats = async (): Promise<AdminDashboardStats> => {
  return adminApiGet(ADMIN_API_ENDPOINTS.DASHBOARD.STATS);
};

/**
 * Get sales chart data
 */
export const getAdminSalesChart = async (period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<{
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }>;
}> => {
  return adminApiGet(`${ADMIN_API_ENDPOINTS.DASHBOARD.SALES_CHART}?period=${period}`);
};

/**
 * Get sales analytics
 */
export const getAdminSalesAnalytics = async (dateRange?: { start: string; end: string }): Promise<AdminSalesAnalytics> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ANALYTICS.SALES}?${queryString}` : ADMIN_API_ENDPOINTS.ANALYTICS.SALES;
  return adminApiGet(endpoint);
};

/**
 * Get customer analytics
 */
export const getAdminCustomerAnalytics = async (dateRange?: { start: string; end: string }): Promise<AdminCustomerAnalytics> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ANALYTICS.CUSTOMERS}?${queryString}` : ADMIN_API_ENDPOINTS.ANALYTICS.CUSTOMERS;
  return adminApiGet(endpoint);
};

/**
 * Get product analytics
 */
export const getAdminProductAnalytics = async (dateRange?: { start: string; end: string }): Promise<AdminProductAnalytics> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ANALYTICS.PRODUCTS}?${queryString}` : ADMIN_API_ENDPOINTS.ANALYTICS.PRODUCTS;
  return adminApiGet(endpoint);
};

/**
 * Get revenue analytics
 */
export const getAdminRevenueAnalytics = async (dateRange?: { start: string; end: string }): Promise<{
  total_revenue: number;
  revenue_growth: number;
  revenue_by_month: Array<{
    month: string;
    revenue: number;
    growth: number;
  }>;
  revenue_by_source: Array<{
    source: string;
    revenue: number;
    percentage: number;
  }>;
  profit_margins: {
    gross_margin: number;
    net_margin: number;
    operating_margin: number;
  };
}> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ANALYTICS.REVENUE}?${queryString}` : ADMIN_API_ENDPOINTS.ANALYTICS.REVENUE;
  return adminApiGet(endpoint);
};

/**
 * Get conversion analytics
 */
export const getAdminConversionAnalytics = async (dateRange?: { start: string; end: string }): Promise<{
  overall_conversion_rate: number;
  conversion_funnel: Array<{
    stage: string;
    visitors: number;
    conversion_rate: number;
  }>;
  conversion_by_source: Array<{
    source: string;
    visitors: number;
    conversions: number;
    conversion_rate: number;
  }>;
  cart_abandonment_rate: number;
  checkout_completion_rate: number;
}> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ANALYTICS.CONVERSION}?${queryString}` : ADMIN_API_ENDPOINTS.ANALYTICS.CONVERSION;
  return adminApiGet(endpoint);
};

/**
 * Get traffic analytics
 */
export const getAdminTrafficAnalytics = async (dateRange?: { start: string; end: string }): Promise<{
  total_visitors: number;
  unique_visitors: number;
  page_views: number;
  bounce_rate: number;
  average_session_duration: number;
  traffic_sources: Array<{
    source: string;
    visitors: number;
    percentage: number;
  }>;
  popular_pages: Array<{
    page: string;
    views: number;
    unique_views: number;
  }>;
  device_breakdown: Array<{
    device: string;
    visitors: number;
    percentage: number;
  }>;
}> => {
  const queryString = dateRange ? buildQueryString(dateRange) : '';
  const endpoint = queryString ? `${ADMIN_API_ENDPOINTS.ANALYTICS.TRAFFIC}?${queryString}` : ADMIN_API_ENDPOINTS.ANALYTICS.TRAFFIC;
  return adminApiGet(endpoint);
};

/**
 * Get available reports
 */
export const getAdminReports = async (): Promise<{
  reports: Array<{
    id: string;
    name: string;
    description: string;
    type: string;
    created_at: string;
    last_generated: string;
  }>;
}> => {
  return adminApiGet(ADMIN_API_ENDPOINTS.ANALYTICS.REPORTS);
};

/**
 * Generate custom report
 */
export const generateAdminCustomReport = async (reportConfig: AdminCustomReport): Promise<AdminApiResponse> => {
  return adminApiPost(ADMIN_API_ENDPOINTS.ANALYTICS.CUSTOM_REPORT, reportConfig);
};

/**
 * Download report
 */
export const downloadAdminReport = async (reportId: string, format: 'csv' | 'pdf' = 'csv'): Promise<void> => {
  const filename = `report_${reportId}_${new Date().toISOString().split('T')[0]}.${format}`;
  return downloadFile(`/admin/analytics/reports/${reportId}/download?format=${format}`, filename);
};

/**
 * Get real-time metrics
 */
export const getAdminRealTimeMetrics = async (): Promise<{
  active_users: number;
  current_orders: number;
  live_sales: number;
  cart_additions: number;
  recent_activities: Array<{
    type: string;
    description: string;
    timestamp: string;
  }>;
}> => {
  return adminApiGet('/admin/analytics/real-time');
};

/**
 * Get performance metrics
 */
export const getAdminPerformanceMetrics = async (): Promise<{
  website_speed: {
    average_load_time: number;
    page_speed_score: number;
  };
  api_performance: {
    average_response_time: number;
    error_rate: number;
    uptime: number;
  };
  database_performance: {
    query_time: number;
    connection_pool: number;
  };
}> => {
  return adminApiGet('/admin/analytics/performance');
};
