import * as React from "react";
import {
  admin<PERSON><PERSON>in,
  admin<PERSON>ogout,
  verifyAd<PERSON><PERSON><PERSON>,
  getStoredAdminUser,
  isAdminAuthenticatedLocally,
  clearAdminAuth,
  type AdminUser,
  type AdminLoginRequest
} from "../services/adminAuthService";

// Define the shape of our authentication context
interface AuthContextType {
  isAuthenticated: boolean;
  user: AdminUser | null;
  login: (email: string, password: string, rememberMe: boolean) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
  error: string | null;
}

// Create the context with a default value
const AuthContext = React.createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  login: async () => false,
  logout: () => {},
  loading: false,
  error: null,
});

// Define the provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = React.useState<boolean>(false);
  const [user, setUser] = React.useState<AdminUser | null>(null);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);

  // Check for existing auth on mount with JWT token support
  React.useEffect(() => {
    const checkAuth = async () => {
      try {
        setLoading(true);
        setError(null);

        // Clear any potentially corrupted auth data on startup
        // This prevents infinite loops from bad tokens
        try {
          // First check if we have a valid token locally
          if (isAdminAuthenticatedLocally()) {
            const storedUser = getStoredAdminUser();
            if (storedUser) {
              setUser(storedUser);
              setIsAuthenticated(true);
              setLoading(false);
              return; // Exit early to prevent backend call
            }
          }
        } catch (error) {
          // Clear corrupted auth data
          clearAdminAuth();
        }

        // If no local auth, just set as not authenticated
        // Don't try to verify with backend on initial load to prevent loops
        setIsAuthenticated(false);
        setUser(null);
      } catch (error: any) {
        console.error("Auth initialization error:", error);
        // Clear any auth data on error
        clearAdminAuth();
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (email: string, password: string, rememberMe: boolean): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      // Call backend login API
      const loginData: AdminLoginRequest = {
        username: email, // Backend expects username field
        password,
        rememberMe,
      };

      const response = await adminLogin(loginData);

      if (response.success && response.user) {
        setUser(response.user);
        setIsAuthenticated(true);
        setError(null);
        return true;
      } else {
        setError(response.message || "Login failed");
        return false;
      }
    } catch (error: any) {
      console.error("Login error:", error);
      setError(error.message || "Login failed. Please try again.");
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call backend logout API
      await adminLogout();
    } catch (error) {
      console.error("Logout error:", error);
      // Continue with local logout even if backend call fails
    } finally {
      // Clear local state
      setUser(null);
      setIsAuthenticated(false);
      setError(null);
    }
  };

  const value = {
    isAuthenticated,
    user,
    login,
    logout,
    loading,
    error,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  return React.useContext(AuthContext);
}
