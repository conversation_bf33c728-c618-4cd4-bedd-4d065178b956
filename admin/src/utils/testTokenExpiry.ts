/**
 * Test Token Expiry Functionality
 * This utility helps test the JWT token expiration system
 */

import { adminTokenService } from '../services/adminTokenService';
import { adminTokenExpiryService } from '../services/adminTokenExpiryService';

export interface TokenTestConfig {
  shortExpiryMinutes?: number; // For testing purposes
  warningThresholdMinutes?: number;
  checkIntervalMs?: number;
}

/**
 * Create a test token with short expiry for testing
 */
export function createTestToken(expiryMinutes: number = 2): string {
  const now = Math.floor(Date.now() / 1000);
  const exp = now + (expiryMinutes * 60); // Convert minutes to seconds
  
  // Create a mock JWT payload
  const header = btoa(JSON.stringify({ alg: "HS256", typ: "JWT" }));
  const payload = btoa(JSON.stringify({
    userId: "test-user-id",
    role: "admin",
    isAdmin: true,
    iat: now,
    exp: exp
  }));
  const signature = "test-signature";
  
  return `${header}.${payload}.${signature}`;
}

/**
 * Set up test token with short expiry
 */
export function setupTestToken(config: TokenTestConfig = {}): void {
  const {
    shortExpiryMinutes = 2,
    warningThresholdMinutes = 1,
    checkIntervalMs = 10000 // 10 seconds for testing
  } = config;

  // Create test token
  const testToken = createTestToken(shortExpiryMinutes);
  const expiryTime = Date.now() + (shortExpiryMinutes * 60 * 1000);

  // Store test token
  const tokenData = {
    accessToken: testToken,
    expiresAt: expiryTime,
    user: {
      id: "test-user-id",
      username: "<EMAIL>",
      email: "<EMAIL>",
      role: "admin",
      permissions: [],
      profile: {
        firstName: "Test",
        lastName: "Admin",
        avatar: null,
        phone: null
      },
      twoFactorAuth: {
        enabled: false
      },
      lastLoginAt: new Date().toISOString(),
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  };

  adminTokenService.setToken(tokenData);

  // Start monitoring with test configuration
  adminTokenExpiryService.startMonitoring({
    warningThreshold: warningThresholdMinutes,
    checkInterval: checkIntervalMs,
    autoLogoutBuffer: 0.5 // 30 seconds before expiry
  });

  console.log('🧪 Test token setup complete:', {
    expiryTime: new Date(expiryTime),
    warningThreshold: `${warningThresholdMinutes} minutes`,
    checkInterval: `${checkIntervalMs}ms`,
    timeUntilExpiry: `${shortExpiryMinutes} minutes`
  });
}

/**
 * Get current token status for testing
 */
export function getTestTokenStatus() {
  const status = adminTokenExpiryService.getTokenStatus();
  
  console.log('🔍 Current token status:', {
    isValid: status.isValid,
    isExpired: status.isExpired,
    willExpireSoon: status.willExpireSoon,
    timeUntilExpiry: status.timeUntilExpiry,
    timeUntilExpiryFormatted: adminTokenExpiryService.formatTimeUntilExpiry(status.timeUntilExpiry),
    expiryDate: status.expiryDate
  });

  return status;
}

/**
 * Test the token expiry warning system
 */
export function testTokenExpirySystem(config: TokenTestConfig = {}): void {
  console.log('🚀 Starting JWT Token Expiry Test...');
  
  // Set up callbacks
  adminTokenExpiryService.onTokenExpiring((status) => {
    console.log('⚠️ Token expiring soon!', {
      timeLeft: adminTokenExpiryService.formatTimeUntilExpiry(status.timeUntilExpiry),
      expiryDate: status.expiryDate
    });
  });

  adminTokenExpiryService.onTokenExpired(() => {
    console.log('🔴 Token has expired! User should be logged out.');
  });

  // Setup test token
  setupTestToken(config);

  // Log status every 30 seconds
  const statusInterval = setInterval(() => {
    const status = getTestTokenStatus();
    if (status.isExpired) {
      console.log('✅ Test completed - token expired');
      clearInterval(statusInterval);
    }
  }, 30000);

  console.log('📊 Test is running... Check console for updates');
}

/**
 * Reset token expiry system
 */
export function resetTokenExpiryTest(): void {
  adminTokenExpiryService.stopMonitoring();
  adminTokenService.clearToken();
  console.log('🔄 Token expiry test reset');
}

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as any).tokenExpiryTest = {
    start: testTokenExpirySystem,
    status: getTestTokenStatus,
    reset: resetTokenExpiryTest,
    setup: setupTestToken
  };
  
  console.log('🧪 Token expiry test utilities available at window.tokenExpiryTest');
  console.log('Usage:');
  console.log('  tokenExpiryTest.start() - Start test with 2-minute expiry');
  console.log('  tokenExpiryTest.start({shortExpiryMinutes: 1}) - Start test with 1-minute expiry');
  console.log('  tokenExpiryTest.status() - Check current status');
  console.log('  tokenExpiryTest.reset() - Reset test');
}
