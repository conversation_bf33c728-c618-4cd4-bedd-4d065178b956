/**
 * Token Expiry Warning Component
 * Shows warning dialog when JWT token is about to expire
 */

import * as React from "react";
import { AlertTriangleIcon, ClockIcon, RefreshCwIcon, LogOutIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { 
  adminTokenExpiryService, 
  formatTimeUntilExpiry,
  type TokenStatus 
} from "@/services/adminTokenExpiryService";

interface TokenExpiryWarningProps {
  isOpen: boolean;
  onClose: () => void;
  tokenStatus: TokenStatus;
}

export function TokenExpiryWarning({ isOpen, onClose, tokenStatus }: TokenExpiryWarningProps) {
  const { logout } = useAuth();
  const [isExtending, setIsExtending] = React.useState(false);
  const [timeLeft, setTimeLeft] = React.useState(tokenStatus.timeUntilExpiry);

  // Update countdown every second
  React.useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      const currentStatus = adminTokenExpiryService.getTokenStatus();
      setTimeLeft(currentStatus.timeUntilExpiry);

      // Auto-close if token expired
      if (currentStatus.isExpired) {
        onClose();
        logout();
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isOpen, onClose, logout]);

  const handleExtendSession = async () => {
    setIsExtending(true);
    try {
      const extended = await adminTokenExpiryService.extendSession();
      if (extended) {
        onClose();
        adminTokenExpiryService.resetWarningState();
      } else {
        // Extension failed, redirect to login
        logout();
      }
    } catch (error) {
      console.error('Failed to extend session:', error);
      logout();
    } finally {
      setIsExtending(false);
    }
  };

  const handleLogout = () => {
    onClose();
    logout();
  };

  const handleContinue = () => {
    onClose();
    // Reset warning state so it can show again later
    adminTokenExpiryService.resetWarningState();
  };

  const timeLeftFormatted = formatTimeUntilExpiry(timeLeft);
  const isUrgent = timeLeft < 5 * 60 * 1000; // Less than 5 minutes

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className={`p-2 rounded-full ${isUrgent ? 'bg-red-100 dark:bg-red-900/30' : 'bg-yellow-100 dark:bg-yellow-900/30'}`}>
              {isUrgent ? (
                <AlertTriangleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
              ) : (
                <ClockIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              )}
            </div>
            Session Expiring Soon
          </DialogTitle>
          <DialogDescription className="space-y-2">
            <p>
              Your admin session will expire in{" "}
              <span className={`font-semibold ${isUrgent ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'}`}>
                {timeLeftFormatted}
              </span>
            </p>
            <p className="text-sm text-muted-foreground">
              You'll be automatically logged out for security reasons. 
              Please save any unsaved work and choose an option below.
            </p>
          </DialogDescription>
        </DialogHeader>

        <div className={`p-4 rounded-lg border ${isUrgent ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800' : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'}`}>
          <div className="flex items-center gap-2 text-sm">
            <ClockIcon className="h-4 w-4" />
            <span>
              Session expires: {tokenStatus.expiryDate?.toLocaleString()}
            </span>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={handleContinue}
            className="w-full sm:w-auto"
          >
            Continue Working
          </Button>
          
          <Button
            variant="outline"
            onClick={handleExtendSession}
            disabled={isExtending}
            className="w-full sm:w-auto"
          >
            {isExtending ? (
              <>
                <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                Extending...
              </>
            ) : (
              <>
                <RefreshCwIcon className="mr-2 h-4 w-4" />
                Extend Session
              </>
            )}
          </Button>

          <Button
            variant="destructive"
            onClick={handleLogout}
            className="w-full sm:w-auto"
          >
            <LogOutIcon className="mr-2 h-4 w-4" />
            Logout Now
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

/**
 * Hook to manage token expiry warnings
 */
export function useTokenExpiryWarning() {
  const [isWarningOpen, setIsWarningOpen] = React.useState(false);
  const [tokenStatus, setTokenStatus] = React.useState<TokenStatus | null>(null);
  const { isAuthenticated } = useAuth();

  React.useEffect(() => {
    // Only start monitoring if user is authenticated
    if (!isAuthenticated) {
      adminTokenExpiryService.stopMonitoring();
      return;
    }

    // Set up token expiry monitoring
    adminTokenExpiryService.onTokenExpiring((status) => {
      setTokenStatus(status);
      setIsWarningOpen(true);
    });

    adminTokenExpiryService.onTokenExpired(() => {
      setIsWarningOpen(false);
      // The auth context will handle the logout
    });

    // Start monitoring with custom config only if authenticated
    adminTokenExpiryService.startMonitoring({
      warningThreshold: 30, // Show warning 30 minutes before expiry
      checkInterval: 60000, // Check every minute
      autoLogoutBuffer: 5 // Auto-logout 5 minutes before expiry
    });

    return () => {
      adminTokenExpiryService.stopMonitoring();
    };
  }, [isAuthenticated]);

  const closeWarning = () => {
    setIsWarningOpen(false);
  };

  return {
    isWarningOpen,
    tokenStatus,
    closeWarning,
    TokenExpiryWarning: tokenStatus ? (
      <TokenExpiryWarning
        isOpen={isWarningOpen}
        onClose={closeWarning}
        tokenStatus={tokenStatus}
      />
    ) : null
  };
}
