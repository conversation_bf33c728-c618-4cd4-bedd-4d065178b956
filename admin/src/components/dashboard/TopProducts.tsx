/**
 * Top Products Component
 * Displays the best-selling products in the dashboard
 */

import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Package, Star } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';

// Mock data for top products
const mockTopProducts = [
  {
    id: '1',
    name: 'Elegant Silk Saree',
    category: 'Silk Sarees',
    sales: 156,
    revenue: 234000,
    image: '/api/placeholder/60/60',
    rating: 4.8,
    growth: 12.5,
  },
  {
    id: '2',
    name: 'Designer Cotton Saree',
    category: 'Cotton Sarees',
    sales: 142,
    revenue: 213000,
    image: '/api/placeholder/60/60',
    rating: 4.6,
    growth: 8.3,
  },
  {
    id: '3',
    name: 'Traditional Banarasi',
    category: 'Banarasi Sarees',
    sales: 128,
    revenue: 384000,
    image: '/api/placeholder/60/60',
    rating: 4.9,
    growth: 15.2,
  },
  {
    id: '4',
    name: 'Georgette Party Wear',
    category: 'Georgette Sarees',
    sales: 98,
    revenue: 147000,
    image: '/api/placeholder/60/60',
    rating: 4.4,
    growth: -2.1,
  },
  {
    id: '5',
    name: 'Chiffon Casual Saree',
    category: 'Chiffon Sarees',
    sales: 87,
    revenue: 130500,
    image: '/api/placeholder/60/60',
    rating: 4.3,
    growth: 5.7,
  },
];

// Product Row Component
const ProductRow: React.FC<{ product: typeof mockTopProducts[0]; index: number; maxSales: number }> = ({ 
  product, 
  index, 
  maxSales 
}) => {
  const salesPercentage = (product.sales / maxSales) * 100;
  const isPositiveGrowth = product.growth >= 0;

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="flex items-center space-x-4 p-3 rounded-lg hover:bg-muted/50 transition-colors"
    >
      {/* Product Image */}
      <div className="flex-shrink-0">
        <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center">
          <Package className="h-6 w-6 text-orange-600" />
        </div>
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {product.name}
          </h4>
          <Badge variant="secondary" className="text-xs">
            #{index + 1}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-xs text-gray-500">{product.category}</span>
          <div className="flex items-center space-x-1">
            <Star className="h-3 w-3 text-yellow-400 fill-current" />
            <span className="text-xs text-gray-600">{product.rating}</span>
          </div>
        </div>

        {/* Sales Progress */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">{product.sales} sales</span>
            <span className="font-medium">₹{product.revenue.toLocaleString()}</span>
          </div>
          <Progress value={salesPercentage} className="h-1" />
        </div>
      </div>

      {/* Growth Indicator */}
      <div className="flex-shrink-0 text-right">
        <div className={`flex items-center space-x-1 ${
          isPositiveGrowth ? 'text-green-600' : 'text-red-600'
        }`}>
          <TrendingUp className={`h-3 w-3 ${!isPositiveGrowth ? 'rotate-180' : ''}`} />
          <span className="text-xs font-medium">
            {isPositiveGrowth ? '+' : ''}{product.growth.toFixed(1)}%
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export const TopProducts: React.FC = () => {
  const maxSales = Math.max(...mockTopProducts.map(p => p.sales));

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5" />
          <span>Top Products</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {mockTopProducts.map((product, index) => (
            <ProductRow
              key={product.id}
              product={product}
              index={index}
              maxSales={maxSales}
            />
          ))}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold">
                {mockTopProducts.reduce((sum, p) => sum + p.sales, 0)}
              </div>
              <div className="text-xs text-muted-foreground">Total Sales</div>
            </div>
            <div>
              <div className="text-lg font-semibold">
                ₹{mockTopProducts.reduce((sum, p) => sum + p.revenue, 0).toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">Total Revenue</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
