/**
 * Recent Orders Component
 * Displays the most recent orders in the dashboard
 */

import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import {
  ShoppingCart,
  Eye,
  MoreHorizontal,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { getRecentAdminOrders } from '../../services/adminOrderService';
import type { AdminOrder } from '../../services/adminOrderService';
import type { AppDispatch } from '../../store';

// Order Status Badge Component
const OrderStatusBadge: React.FC<{ status: AdminOrder['status'] }> = ({ status }) => {
  const statusConfig = {
    pending: {
      label: 'Pending',
      variant: 'secondary' as const,
      icon: Clock,
      color: 'text-yellow-600',
    },
    confirmed: {
      label: 'Confirmed',
      variant: 'default' as const,
      icon: CheckCircle,
      color: 'text-blue-600',
    },
    processing: {
      label: 'Processing',
      variant: 'default' as const,
      icon: Package,
      color: 'text-purple-600',
    },
    shipped: {
      label: 'Shipped',
      variant: 'default' as const,
      icon: Truck,
      color: 'text-orange-600',
    },
    delivered: {
      label: 'Delivered',
      variant: 'default' as const,
      icon: CheckCircle,
      color: 'text-green-600',
    },
    cancelled: {
      label: 'Cancelled',
      variant: 'destructive' as const,
      icon: XCircle,
      color: 'text-red-600',
    },
    refunded: {
      label: 'Refunded',
      variant: 'outline' as const,
      icon: XCircle,
      color: 'text-gray-600',
    },
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center space-x-1">
      <Icon className="h-3 w-3" />
      <span>{config.label}</span>
    </Badge>
  );
};

// Order Row Component
const OrderRow: React.FC<{ order: AdminOrder; index: number }> = ({ order, index }) => {
  const customerName = `${order.userId.profile.firstName || ''} ${order.userId.profile.lastName || ''}`.trim() || 'Unknown Customer';
  const customerInitials = customerName.split(' ').map(n => n[0]).join('').toUpperCase();

  return (
    <motion.tr
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="hover:bg-muted/50"
    >
      <TableCell>
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src="" alt={customerName} />
            <AvatarFallback className="text-xs">{customerInitials}</AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium text-sm">{order.orderNumber}</div>
            <div className="text-xs text-muted-foreground">{customerName}</div>
          </div>
        </div>
      </TableCell>
      <TableCell>
        <div className="text-sm">
          {order.items.length} item{order.items.length !== 1 ? 's' : ''}
        </div>
        <div className="text-xs text-muted-foreground">
          {order.items[0]?.productDetails.name}
          {order.items.length > 1 && ` +${order.items.length - 1} more`}
        </div>
      </TableCell>
      <TableCell>
        <div className="font-medium">₹{order.totals.total.toLocaleString()}</div>
        <div className="text-xs text-muted-foreground">
          {order.paymentMethod.type}
        </div>
      </TableCell>
      <TableCell>
        <OrderStatusBadge status={order.status} />
      </TableCell>
      <TableCell>
        <div className="text-sm">
          {format(new Date(order.placedAt), 'MMM dd')}
        </div>
        <div className="text-xs text-muted-foreground">
          {format(new Date(order.placedAt), 'HH:mm')}
        </div>
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Package className="h-4 w-4 mr-2" />
              Update Status
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Truck className="h-4 w-4 mr-2" />
              Track Order
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </motion.tr>
  );
};

export const RecentOrders: React.FC = () => {
  const [orders, setOrders] = React.useState<AdminOrder[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  useEffect(() => {
    const fetchRecentOrders = async () => {
      try {
        setLoading(true);
        const response = await getRecentAdminOrders(10);
        setOrders(response.orders);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch recent orders');
      } finally {
        setLoading(false);
      }
    };

    fetchRecentOrders();
  }, []);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <ShoppingCart className="h-5 w-5" />
            <span>Recent Orders</span>
          </CardTitle>
          <Button variant="outline" size="sm">
            View All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-32 text-muted-foreground">
            <div className="text-center">
              <XCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>{error}</p>
            </div>
          </div>
        ) : orders.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-muted-foreground">
            <div className="text-center">
              <ShoppingCart className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No recent orders</p>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orders.map((order, index) => (
                  <OrderRow key={order._id} order={order} index={index} />
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
