/**
 * Real Time Metrics Component
 * Displays live metrics and activity in the dashboard
 */

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Users, 
  ShoppingCart, 
  DollarSign, 
  TrendingUp,
  Eye,
  MousePointer,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';

// Mock real-time data
const generateMockRealTimeData = () => ({
  activeUsers: Math.floor(Math.random() * 50) + 120,
  currentOrders: Math.floor(Math.random() * 10) + 15,
  liveSales: Math.floor(Math.random() * 5000) + 25000,
  cartAdditions: Math.floor(Math.random() * 20) + 45,
  pageViews: Math.floor(Math.random() * 100) + 500,
  conversionRate: (Math.random() * 2 + 3).toFixed(2),
  recentActivities: [
    {
      id: 1,
      type: 'order',
      description: 'New order placed - ₹2,450',
      timestamp: new Date().toISOString(),
      icon: ShoppingCart,
      color: 'text-green-600',
    },
    {
      id: 2,
      type: 'user',
      description: 'New user registered',
      timestamp: new Date(Date.now() - 30000).toISOString(),
      icon: Users,
      color: 'text-blue-600',
    },
    {
      id: 3,
      type: 'cart',
      description: 'Product added to cart',
      timestamp: new Date(Date.now() - 60000).toISOString(),
      icon: MousePointer,
      color: 'text-orange-600',
    },
    {
      id: 4,
      type: 'view',
      description: 'Product page viewed',
      timestamp: new Date(Date.now() - 90000).toISOString(),
      icon: Eye,
      color: 'text-purple-600',
    },
  ],
});

// Live Metric Card Component
const LiveMetricCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  change?: number;
}> = ({ title, value, icon, color, change }) => {
  return (
    <motion.div
      initial={{ scale: 0.95 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.3 }}
      className="p-4 border border-gray-200 rounded-lg bg-white"
    >
      <div className="flex items-center justify-between mb-2">
        <div className={`p-2 rounded-lg ${color} text-white`}>
          {icon}
        </div>
        <Badge variant="secondary" className="animate-pulse">
          Live
        </Badge>
      </div>
      <div className="text-2xl font-bold">{value}</div>
      <div className="text-sm text-muted-foreground">{title}</div>
      {change !== undefined && (
        <div className="flex items-center mt-1">
          <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
          <span className="text-xs text-green-600">+{change}% from last hour</span>
        </div>
      )}
    </motion.div>
  );
};

// Activity Item Component
const ActivityItem: React.FC<{
  activity: {
    id: number;
    type: string;
    description: string;
    timestamp: string;
    icon: React.ComponentType<any>;
    color: string;
  };
  index: number;
}> = ({ activity, index }) => {
  const Icon = activity.icon;
  const timeAgo = Math.floor((Date.now() - new Date(activity.timestamp).getTime()) / 1000);
  
  const formatTimeAgo = (seconds: number) => {
    if (seconds < 60) return `${seconds}s ago`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
    return `${Math.floor(seconds / 3600)}h ago`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50"
    >
      <div className={`p-1.5 rounded-full bg-gray-100 ${activity.color}`}>
        <Icon className="h-3 w-3" />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm text-gray-900">{activity.description}</p>
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <Clock className="h-3 w-3" />
          <span>{formatTimeAgo(timeAgo)}</span>
        </div>
      </div>
    </motion.div>
  );
};

export const RealTimeMetrics: React.FC = () => {
  const [metrics, setMetrics] = useState(generateMockRealTimeData());
  const [isLive, setIsLive] = useState(true);

  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      setMetrics(generateMockRealTimeData());
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isLive]);

  const toggleLive = () => {
    setIsLive(!isLive);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Real-Time Activity</span>
          </CardTitle>
          <button
            onClick={toggleLive}
            className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
              isLive 
                ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {isLive ? '● Live' : '○ Paused'}
          </button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Live Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <LiveMetricCard
            title="Active Users"
            value={metrics.activeUsers}
            icon={<Users className="h-4 w-4" />}
            color="bg-blue-500"
            change={5.2}
          />
          <LiveMetricCard
            title="Current Orders"
            value={metrics.currentOrders}
            icon={<ShoppingCart className="h-4 w-4" />}
            color="bg-green-500"
            change={12.8}
          />
          <LiveMetricCard
            title="Live Sales"
            value={`₹${metrics.liveSales.toLocaleString()}`}
            icon={<DollarSign className="h-4 w-4" />}
            color="bg-orange-500"
            change={8.4}
          />
          <LiveMetricCard
            title="Cart Additions"
            value={metrics.cartAdditions}
            icon={<MousePointer className="h-4 w-4" />}
            color="bg-purple-500"
            change={3.7}
          />
        </div>

        {/* Additional Metrics */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-lg font-semibold text-blue-600">
              {metrics.pageViews.toLocaleString()}
            </div>
            <div className="text-xs text-gray-600">Page Views (Last Hour)</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">
              {metrics.conversionRate}%
            </div>
            <div className="text-xs text-gray-600">Conversion Rate</div>
          </div>
        </div>

        {/* Recent Activities */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Recent Activity</span>
          </h4>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            <AnimatePresence>
              {metrics.recentActivities.map((activity, index) => (
                <ActivityItem
                  key={activity.id}
                  activity={activity}
                  index={index}
                />
              ))}
            </AnimatePresence>
          </div>
        </div>

        {/* Status Indicator */}
        <div className="flex items-center justify-center pt-4 border-t">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
            <span>{isLive ? 'Live updates active' : 'Updates paused'}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
