/**
 * Quick Actions Component
 * Provides quick access to common admin tasks
 */

import React from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Package,
  ShoppingCart,
  Users,
  Image,
  FileText,
  Settings,
  BarChart3,
  Upload,
  Download,
  Mail,
  Bell,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';

// Quick Action Item Component
interface QuickActionItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick: () => void;
  badge?: string;
  color?: 'blue' | 'green' | 'orange' | 'red' | 'purple';
}

const QuickActionItem: React.FC<QuickActionItemProps> = ({
  icon,
  title,
  description,
  onClick,
  badge,
  color = 'blue',
}) => {
  const colorClasses = {
    blue: 'hover:bg-blue-50 hover:border-blue-200',
    green: 'hover:bg-green-50 hover:border-green-200',
    orange: 'hover:bg-orange-50 hover:border-orange-200',
    red: 'hover:bg-red-50 hover:border-red-200',
    purple: 'hover:bg-purple-50 hover:border-purple-200',
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`p-3 border border-gray-200 rounded-lg cursor-pointer transition-colors ${colorClasses[color]}`}
      onClick={onClick}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">{title}</h4>
            {badge && (
              <Badge variant="secondary" className="text-xs">
                {badge}
              </Badge>
            )}
          </div>
          <p className="text-xs text-gray-500 mt-1">{description}</p>
        </div>
      </div>
    </motion.div>
  );
};

export const QuickActions: React.FC = () => {
  const handleAction = (action: string) => {
    console.log(`Quick action: ${action}`);
    // TODO: Implement navigation or action handlers
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>Quick Actions</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Product Management */}
        <div>
          <h5 className="text-sm font-medium text-gray-700 mb-3">Product Management</h5>
          <div className="space-y-2">
            <QuickActionItem
              icon={<Plus className="h-5 w-5 text-blue-500" />}
              title="Add New Product"
              description="Create a new product listing"
              onClick={() => handleAction('add-product')}
              color="blue"
            />
            <QuickActionItem
              icon={<Package className="h-5 w-5 text-green-500" />}
              title="Manage Inventory"
              description="Update stock levels and variants"
              onClick={() => handleAction('manage-inventory')}
              badge="12 low"
              color="green"
            />
            <QuickActionItem
              icon={<Upload className="h-5 w-5 text-purple-500" />}
              title="Bulk Import"
              description="Import products from CSV"
              onClick={() => handleAction('bulk-import')}
              color="purple"
            />
          </div>
        </div>

        <Separator />

        {/* Order Management */}
        <div>
          <h5 className="text-sm font-medium text-gray-700 mb-3">Order Management</h5>
          <div className="space-y-2">
            <QuickActionItem
              icon={<ShoppingCart className="h-5 w-5 text-orange-500" />}
              title="Process Orders"
              description="Review and fulfill pending orders"
              onClick={() => handleAction('process-orders')}
              badge="8 pending"
              color="orange"
            />
            <QuickActionItem
              icon={<Download className="h-5 w-5 text-blue-500" />}
              title="Export Orders"
              description="Download order reports"
              onClick={() => handleAction('export-orders')}
              color="blue"
            />
          </div>
        </div>

        <Separator />

        {/* Customer Management */}
        <div>
          <h5 className="text-sm font-medium text-gray-700 mb-3">Customer Management</h5>
          <div className="space-y-2">
            <QuickActionItem
              icon={<Users className="h-5 w-5 text-green-500" />}
              title="View Customers"
              description="Manage customer accounts"
              onClick={() => handleAction('view-customers')}
              color="green"
            />
            <QuickActionItem
              icon={<Mail className="h-5 w-5 text-purple-500" />}
              title="Send Newsletter"
              description="Create and send email campaigns"
              onClick={() => handleAction('send-newsletter')}
              color="purple"
            />
          </div>
        </div>

        <Separator />

        {/* Content Management */}
        <div>
          <h5 className="text-sm font-medium text-gray-700 mb-3">Content Management</h5>
          <div className="space-y-2">
            <QuickActionItem
              icon={<Image className="h-5 w-5 text-orange-500" />}
              title="Manage Banners"
              description="Update homepage banners"
              onClick={() => handleAction('manage-banners')}
              color="orange"
            />
            <QuickActionItem
              icon={<Bell className="h-5 w-5 text-red-500" />}
              title="Send Notification"
              description="Push notifications to customers"
              onClick={() => handleAction('send-notification')}
              color="red"
            />
          </div>
        </div>

        <Separator />

        {/* Analytics & Reports */}
        <div>
          <h5 className="text-sm font-medium text-gray-700 mb-3">Analytics & Reports</h5>
          <div className="space-y-2">
            <QuickActionItem
              icon={<BarChart3 className="h-5 w-5 text-blue-500" />}
              title="View Analytics"
              description="Detailed sales and customer insights"
              onClick={() => handleAction('view-analytics')}
              color="blue"
            />
            <QuickActionItem
              icon={<FileText className="h-5 w-5 text-green-500" />}
              title="Generate Report"
              description="Create custom business reports"
              onClick={() => handleAction('generate-report')}
              color="green"
            />
          </div>
        </div>

        <Separator />

        {/* System Actions */}
        <div className="pt-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => handleAction('system-settings')}
          >
            <Settings className="h-4 w-4 mr-2" />
            System Settings
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
