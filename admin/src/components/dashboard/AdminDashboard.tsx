/**
 * Admin Dashboard Component
 * Main dashboard with overview statistics, charts, and quick actions
 */

import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Users,
  Package,
  DollarSign,
  AlertTriangle,
  Eye,
  Activity,
  Calendar,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { SalesChart } from './SalesChart';
import { RecentOrders } from './RecentOrders';
import { TopProducts } from './TopProducts';
import { CustomerInsights } from './CustomerInsights';
import { RealTimeMetrics } from './RealTimeMetrics';
import { QuickActions } from './QuickActions';
import {
  fetchDashboardStats,
  fetchSalesChart,
  fetchRealTimeMetrics,
  selectDashboardStats,
  selectSalesChart,
  selectRealTimeMetrics,
  selectAnalyticsLoading,
  setSelectedPeriod,
  selectSelectedPeriod,
} from '../../store/slices/adminAnalyticsSlice';
import type { AppDispatch, RootState } from '../../store';

// Stat Card Component
interface StatCardProps {
  title: string;
  value: string | number;
  change: number;
  changeLabel: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'orange' | 'red' | 'purple';
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  change,
  changeLabel,
  icon,
  color,
}) => {
  const isPositive = change >= 0;
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500',
    purple: 'bg-purple-500',
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <div className={`p-2 rounded-lg ${colorClasses[color]} text-white`}>
            {icon}
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{value}</div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            {isPositive ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500" />
            )}
            <span className={isPositive ? 'text-green-500' : 'text-red-500'}>
              {isPositive ? '+' : ''}{change}%
            </span>
            <span>{changeLabel}</span>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Alert Card Component
interface AlertCardProps {
  title: string;
  description: string;
  type: 'warning' | 'error' | 'info';
  action?: {
    label: string;
    onClick: () => void;
  };
}

const AlertCard: React.FC<AlertCardProps> = ({ title, description, type, action }) => {
  const typeStyles = {
    warning: 'border-orange-200 bg-orange-50',
    error: 'border-red-200 bg-red-50',
    info: 'border-blue-200 bg-blue-50',
  };

  const iconStyles = {
    warning: 'text-orange-500',
    error: 'text-red-500',
    info: 'text-blue-500',
  };

  return (
    <Card className={typeStyles[type]}>
      <CardContent className="pt-6">
        <div className="flex items-start space-x-3">
          <AlertTriangle className={`h-5 w-5 mt-0.5 ${iconStyles[type]}`} />
          <div className="flex-1">
            <h4 className="font-medium">{title}</h4>
            <p className="text-sm text-muted-foreground mt-1">{description}</p>
            {action && (
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={action.onClick}
              >
                {action.label}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const AdminDashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const dashboardStats = useSelector(selectDashboardStats);
  const salesChart = useSelector(selectSalesChart);
  const realTimeMetrics = useSelector(selectRealTimeMetrics);
  const loading = useSelector(selectAnalyticsLoading);
  const selectedPeriod = useSelector(selectSelectedPeriod);

  useEffect(() => {
    // Fetch initial dashboard data
    dispatch(fetchDashboardStats());
    dispatch(fetchSalesChart(selectedPeriod));
    dispatch(fetchRealTimeMetrics());

    // Set up real-time updates
    const realTimeInterval = setInterval(() => {
      dispatch(fetchRealTimeMetrics());
    }, 30000); // Update every 30 seconds

    return () => clearInterval(realTimeInterval);
  }, [dispatch, selectedPeriod]);

  const handlePeriodChange = (period: 'day' | 'week' | 'month' | 'year') => {
    dispatch(setSelectedPeriod(period));
    dispatch(fetchSalesChart(period));
  };

  if (loading.dashboard) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your store.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Last 30 days
          </Button>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            View Reports
          </Button>
        </div>
      </div>

      {/* Real-time Metrics */}
      {realTimeMetrics && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Live Activity</span>
                <Badge variant="secondary" className="bg-white/20 text-white">
                  Live
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{realTimeMetrics.active_users}</div>
                  <div className="text-sm opacity-80">Active Users</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{realTimeMetrics.current_orders}</div>
                  <div className="text-sm opacity-80">Current Orders</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">₹{realTimeMetrics.live_sales}</div>
                  <div className="text-sm opacity-80">Live Sales</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{realTimeMetrics.cart_additions}</div>
                  <div className="text-sm opacity-80">Cart Additions</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Key Statistics */}
      {dashboardStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Revenue"
            value={`₹${dashboardStats.sales.this_month.toLocaleString()}`}
            change={dashboardStats.sales.growth_rate}
            changeLabel="from last month"
            icon={<DollarSign className="h-4 w-4" />}
            color="green"
          />
          <StatCard
            title="Total Orders"
            value={dashboardStats.orders.this_month.toLocaleString()}
            change={dashboardStats.orders.growth_rate}
            changeLabel="from last month"
            icon={<ShoppingCart className="h-4 w-4" />}
            color="blue"
          />
          <StatCard
            title="Total Customers"
            value={dashboardStats.customers.total.toLocaleString()}
            change={dashboardStats.customers.growth_rate}
            changeLabel="from last month"
            icon={<Users className="h-4 w-4" />}
            color="purple"
          />
          <StatCard
            title="Active Products"
            value={dashboardStats.products.active.toLocaleString()}
            change={0}
            changeLabel="total products"
            icon={<Package className="h-4 w-4" />}
            color="orange"
          />
        </div>
      )}

      {/* Alerts */}
      {dashboardStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {dashboardStats.products.low_stock > 0 && (
            <AlertCard
              title="Low Stock Alert"
              description={`${dashboardStats.products.low_stock} products are running low on stock`}
              type="warning"
              action={{
                label: 'View Products',
                onClick: () => {/* Navigate to products */}
              }}
            />
          )}
          {dashboardStats.products.out_of_stock > 0 && (
            <AlertCard
              title="Out of Stock"
              description={`${dashboardStats.products.out_of_stock} products are out of stock`}
              type="error"
              action={{
                label: 'Restock Now',
                onClick: () => {/* Navigate to inventory */}
              }}
            />
          )}
          {dashboardStats.orders.pending > 10 && (
            <AlertCard
              title="Pending Orders"
              description={`${dashboardStats.orders.pending} orders are waiting for processing`}
              type="info"
              action={{
                label: 'Process Orders',
                onClick: () => {/* Navigate to orders */}
              }}
            />
          )}
        </div>
      )}

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sales Chart */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Sales Overview</CardTitle>
                <Tabs value={selectedPeriod} onValueChange={handlePeriodChange}>
                  <TabsList>
                    <TabsTrigger value="day">Day</TabsTrigger>
                    <TabsTrigger value="week">Week</TabsTrigger>
                    <TabsTrigger value="month">Month</TabsTrigger>
                    <TabsTrigger value="year">Year</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </CardHeader>
            <CardContent>
              <SalesChart data={salesChart} loading={loading.sales} />
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div>
          <QuickActions />
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentOrders />
        <TopProducts />
      </div>

      {/* Customer Insights */}
      <CustomerInsights />
    </div>
  );
};
