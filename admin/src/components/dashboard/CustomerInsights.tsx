/**
 * Customer Insights Component
 * Displays customer analytics and insights in the dashboard
 */

import React from 'react';
import { motion } from 'framer-motion';
import { Users, TrendingUp, MapPin, Calendar, UserPlus, Repeat } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';

// Mock customer insights data
const mockCustomerInsights = {
  overview: {
    totalCustomers: 12847,
    newThisMonth: 342,
    activeCustomers: 8934,
    retentionRate: 78.5,
    averageOrderValue: 2450,
    lifetimeValue: 18750,
  },
  demographics: {
    byAge: [
      { range: '18-25', count: 2156, percentage: 16.8 },
      { range: '26-35', count: 4523, percentage: 35.2 },
      { range: '36-45', count: 3845, percentage: 29.9 },
      { range: '46-55', count: 1876, percentage: 14.6 },
      { range: '55+', count: 447, percentage: 3.5 },
    ],
    byLocation: [
      { city: 'Mumbai', count: 2847, percentage: 22.1 },
      { city: 'Delhi', count: 2156, percentage: 16.8 },
      { city: 'Bangalore', count: 1934, percentage: 15.1 },
      { city: 'Chennai', count: 1523, percentage: 11.9 },
      { city: 'Others', count: 4387, percentage: 34.1 },
    ],
  },
  behavior: {
    repeatCustomers: 67.3,
    averageOrdersPerCustomer: 3.2,
    mostPopularCategory: 'Silk Sarees',
    peakShoppingTime: '7-9 PM',
  },
};

// Insight Card Component
const InsightCard: React.FC<{
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  trend?: number;
  color?: string;
}> = ({ title, value, subtitle, icon, trend, color = 'blue' }) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    orange: 'bg-orange-500',
    purple: 'bg-purple-500',
  };

  return (
    <div className="p-4 border border-gray-200 rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <div className={`p-2 rounded-lg ${colorClasses[color as keyof typeof colorClasses]} text-white`}>
          {icon}
        </div>
        {trend !== undefined && (
          <Badge variant={trend >= 0 ? 'default' : 'destructive'} className="text-xs">
            {trend >= 0 ? '+' : ''}{trend}%
          </Badge>
        )}
      </div>
      <div className="text-lg font-semibold">{value}</div>
      <div className="text-sm text-muted-foreground">{title}</div>
      {subtitle && (
        <div className="text-xs text-muted-foreground mt-1">{subtitle}</div>
      )}
    </div>
  );
};

// Demographics Chart Component
const DemographicsChart: React.FC<{
  title: string;
  data: Array<{ range?: string; city?: string; count: number; percentage: number }>;
  icon: React.ReactNode;
}> = ({ title, data, icon }) => {
  const maxCount = Math.max(...data.map(item => item.count));

  return (
    <div className="space-y-4">
      <h4 className="flex items-center space-x-2 text-sm font-medium">
        {icon}
        <span>{title}</span>
      </h4>
      <div className="space-y-3">
        {data.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="space-y-2"
          >
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-700">{item.range || item.city}</span>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">{item.count.toLocaleString()}</span>
                <Badge variant="secondary" className="text-xs">
                  {item.percentage}%
                </Badge>
              </div>
            </div>
            <Progress value={item.percentage} className="h-2" />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export const CustomerInsights: React.FC = () => {
  const { overview, demographics, behavior } = mockCustomerInsights;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5" />
          <span>Customer Insights</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overview Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <InsightCard
            title="Total Customers"
            value={overview.totalCustomers.toLocaleString()}
            icon={<Users className="h-4 w-4" />}
            trend={8.2}
            color="blue"
          />
          <InsightCard
            title="New This Month"
            value={overview.newThisMonth.toLocaleString()}
            icon={<UserPlus className="h-4 w-4" />}
            trend={12.5}
            color="green"
          />
          <InsightCard
            title="Active Customers"
            value={overview.activeCustomers.toLocaleString()}
            subtitle={`${((overview.activeCustomers / overview.totalCustomers) * 100).toFixed(1)}% of total`}
            icon={<TrendingUp className="h-4 w-4" />}
            color="orange"
          />
          <InsightCard
            title="Retention Rate"
            value={`${overview.retentionRate}%`}
            icon={<Repeat className="h-4 w-4" />}
            trend={3.1}
            color="purple"
          />
        </div>

        {/* Demographics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <DemographicsChart
            title="Age Distribution"
            data={demographics.byAge}
            icon={<Calendar className="h-4 w-4" />}
          />
          <DemographicsChart
            title="Geographic Distribution"
            data={demographics.byLocation}
            icon={<MapPin className="h-4 w-4" />}
          />
        </div>

        {/* Behavior Insights */}
        <div className="border-t pt-6">
          <h4 className="text-sm font-medium mb-4">Customer Behavior</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">
                {behavior.repeatCustomers}%
              </div>
              <div className="text-xs text-muted-foreground">Repeat Customers</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600">
                {behavior.averageOrdersPerCustomer}
              </div>
              <div className="text-xs text-muted-foreground">Avg Orders/Customer</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-orange-600">
                {behavior.mostPopularCategory}
              </div>
              <div className="text-xs text-muted-foreground">Top Category</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-purple-600">
                {behavior.peakShoppingTime}
              </div>
              <div className="text-xs text-muted-foreground">Peak Hours</div>
            </div>
          </div>
        </div>

        {/* Value Metrics */}
        <div className="border-t pt-6">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">
                ₹{overview.averageOrderValue.toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Average Order Value</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                ₹{overview.lifetimeValue.toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Customer Lifetime Value</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
