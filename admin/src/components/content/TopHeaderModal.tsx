import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';

// TopHeader interface for this component
interface TopHeader {
  _id: string;
  text: string;
  isActive: boolean;
  order: number;
  backgroundColor: string;
  textColor: string;
  link?: string;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface TopHeaderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  editingHeader: TopHeader | null;
}

const TopHeaderModal: React.FC<TopHeaderModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingHeader
}) => {
  const [formData, setFormData] = useState({
    text: '',
    isActive: true,
    order: 0,
    backgroundColor: '#27272a',
    textColor: '#ffffff',
    link: '',
    startDate: '',
    endDate: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when modal opens/closes or editing header changes
  useEffect(() => {
    if (isOpen) {
      if (editingHeader) {
        setFormData({
          text: editingHeader.text,
          isActive: editingHeader.isActive,
          order: editingHeader.order,
          backgroundColor: editingHeader.backgroundColor,
          textColor: editingHeader.textColor,
          link: editingHeader.link || '',
          startDate: editingHeader.startDate ? editingHeader.startDate.split('T')[0] : '',
          endDate: editingHeader.endDate ? editingHeader.endDate.split('T')[0] : ''
        });
      } else {
        setFormData({
          text: '',
          isActive: true,
          order: 0,
          backgroundColor: '#27272a',
          textColor: '#ffffff',
          link: '',
          startDate: '',
          endDate: ''
        });
      }
      setErrors({});
    }
  }, [isOpen, editingHeader]);

  // Handle input changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.text.trim()) {
      newErrors.text = 'Text is required';
    } else if (formData.text.length > 200) {
      newErrors.text = 'Text must be less than 200 characters';
    }

    if (formData.order < 0) {
      newErrors.order = 'Order must be a positive number';
    }

    if (formData.link && !isValidUrl(formData.link)) {
      newErrors.link = 'Please enter a valid URL';
    }

    if (formData.startDate && formData.endDate && formData.startDate > formData.endDate) {
      newErrors.endDate = 'End date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // URL validation helper
  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Prepare data for submission
    const submitData = {
      ...formData,
      link: formData.link || undefined,
      startDate: formData.startDate || undefined,
      endDate: formData.endDate || undefined
    };

    onSave(submitData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">
            {editingHeader ? 'Edit Top Header' : 'Create Top Header'}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Text */}
          <div>
            <Label htmlFor="text">Text *</Label>
            <Textarea
              id="text"
              value={formData.text}
              onChange={(e) => handleChange('text', e.target.value)}
              placeholder="Enter promotional text..."
              className={errors.text ? 'border-red-500' : ''}
              rows={3}
            />
            {errors.text && <p className="text-red-500 text-sm mt-1">{errors.text}</p>}
            <p className="text-gray-500 text-sm mt-1">
              {formData.text.length}/200 characters
            </p>
          </div>

          {/* Active Status */}
          <div className="flex items-center justify-between">
            <Label htmlFor="isActive">Active</Label>
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => handleChange('isActive', checked)}
            />
          </div>

          {/* Order */}
          <div>
            <Label htmlFor="order">Display Order</Label>
            <Input
              id="order"
              type="number"
              value={formData.order}
              onChange={(e) => handleChange('order', parseInt(e.target.value) || 0)}
              className={errors.order ? 'border-red-500' : ''}
              min="0"
            />
            {errors.order && <p className="text-red-500 text-sm mt-1">{errors.order}</p>}
          </div>

          {/* Colors */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="backgroundColor">Background Color</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="backgroundColor"
                  type="color"
                  value={formData.backgroundColor}
                  onChange={(e) => handleChange('backgroundColor', e.target.value)}
                  className="w-12 h-10 p-1"
                />
                <Input
                  value={formData.backgroundColor}
                  onChange={(e) => handleChange('backgroundColor', e.target.value)}
                  placeholder="#27272a"
                  className="flex-1"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="textColor">Text Color</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="textColor"
                  type="color"
                  value={formData.textColor}
                  onChange={(e) => handleChange('textColor', e.target.value)}
                  className="w-12 h-10 p-1"
                />
                <Input
                  value={formData.textColor}
                  onChange={(e) => handleChange('textColor', e.target.value)}
                  placeholder="#ffffff"
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* Link */}
          <div>
            <Label htmlFor="link">Link (Optional)</Label>
            <Input
              id="link"
              type="url"
              value={formData.link}
              onChange={(e) => handleChange('link', e.target.value)}
              placeholder="https://example.com"
              className={errors.link ? 'border-red-500' : ''}
            />
            {errors.link && <p className="text-red-500 text-sm mt-1">{errors.link}</p>}
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Start Date (Optional)</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleChange('startDate', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="endDate">End Date (Optional)</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleChange('endDate', e.target.value)}
                className={errors.endDate ? 'border-red-500' : ''}
              />
              {errors.endDate && <p className="text-red-500 text-sm mt-1">{errors.endDate}</p>}
            </div>
          </div>

          {/* Preview */}
          <div>
            <Label>Preview</Label>
            <div
              className="p-3 text-center text-sm font-medium rounded border"
              style={{
                backgroundColor: formData.backgroundColor,
                color: formData.textColor
              }}
            >
              {formData.text || 'Enter text to see preview...'}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              {editingHeader ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TopHeaderModal;
