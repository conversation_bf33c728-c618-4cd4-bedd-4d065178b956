import React, { useState, useEffect } from 'react';
import { X, Upload, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectItem } from '@/components/ui/select';

// Banner interfaces
interface Banner {
  _id?: string;
  title: string;
  subtitle?: string;
  description?: string;
  bannerType: 'home-main' | 'category-slider' | 'category-banner' | 'promotional' | 'seasonal' | 'collection';
  category: string;
  desktopImage: string;
  mobileImage?: string;
  thumbnailImage?: string;
  ctaText: string;
  ctaLink: string;
  isActive: boolean;
  displayOrder: number;
  textColor: string;
  backgroundColor: string;
  overlayOpacity: number;
  textAlignment: 'left' | 'center' | 'right';
  startDate?: string;
  endDate?: string;
  altText?: string;
  seoTitle?: string;
  seoDescription?: string;
  tags: string[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

interface BannerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Banner) => void;
  editingBanner: Banner | null;
}

const BannerModal: React.FC<BannerModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingBanner
}) => {
  const [formData, setFormData] = useState<Banner>({
    title: '',
    subtitle: '',
    description: '',
    bannerType: 'home-main',
    category: 'general',
    desktopImage: '',
    mobileImage: '',
    thumbnailImage: '',
    ctaText: 'Shop Now',
    ctaLink: '/products',
    isActive: true,
    displayOrder: 0,
    textColor: '#ffffff',
    backgroundColor: 'transparent',
    overlayOpacity: 0.5,
    textAlignment: 'center',
    startDate: '',
    endDate: '',
    altText: '',
    seoTitle: '',
    seoDescription: '',
    tags: [],
    priority: 'medium'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [tagInput, setTagInput] = useState('');

  // Reset form when modal opens/closes or editing banner changes
  useEffect(() => {
    if (isOpen) {
      if (editingBanner) {
        setFormData({
          ...editingBanner,
          tags: editingBanner.tags || []
        });
      } else {
        setFormData({
          title: '',
          subtitle: '',
          description: '',
          bannerType: 'home-main',
          category: 'general',
          desktopImage: '',
          mobileImage: '',
          thumbnailImage: '',
          ctaText: 'Shop Now',
          ctaLink: '/products',
          isActive: true,
          displayOrder: 0,
          textColor: '#ffffff',
          backgroundColor: 'transparent',
          overlayOpacity: 0.5,
          textAlignment: 'center',
          startDate: '',
          endDate: '',
          altText: '',
          seoTitle: '',
          seoDescription: '',
          tags: [],
          priority: 'medium'
        });
      }
      setErrors({});
      setTagInput('');
    }
  }, [isOpen, editingBanner]);

  // Handle input changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle tag management
  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.desktopImage.trim()) {
      newErrors.desktopImage = 'Desktop image is required';
    }

    if (!formData.ctaText.trim()) {
      newErrors.ctaText = 'CTA text is required';
    }

    if (!formData.ctaLink.trim()) {
      newErrors.ctaLink = 'CTA link is required';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">
            {editingBanner ? 'Edit Banner' : 'Create Banner'}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleChange('title', e.target.value)}
                placeholder="Enter banner title"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
            </div>

            <div>
              <Label htmlFor="subtitle">Subtitle</Label>
              <Input
                id="subtitle"
                value={formData.subtitle}
                onChange={(e) => handleChange('subtitle', e.target.value)}
                placeholder="Enter banner subtitle"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="Enter banner description"
              rows={3}
            />
          </div>

          {/* Banner Type and Category */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="bannerType">Banner Type *</Label>
              <Select value={formData.bannerType} onValueChange={(value) => handleChange('bannerType', value)}>
                <SelectItem value="home-main">Home Main</SelectItem>
                <SelectItem value="category-slider">Category Slider</SelectItem>
                <SelectItem value="promotional">Promotional</SelectItem>
                <SelectItem value="seasonal">Seasonal</SelectItem>
                <SelectItem value="collection">Collection</SelectItem>
              </Select>
            </div>

            <div>
              <Label htmlFor="category">Category *</Label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => handleChange('category', e.target.value)}
                placeholder="e.g., kurti, lehenga, general"
                className={errors.category ? 'border-red-500' : ''}
              />
              {errors.category && <p className="text-red-500 text-sm mt-1">{errors.category}</p>}
            </div>

            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select value={formData.priority} onValueChange={(value) => handleChange('priority', value)}>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </Select>
            </div>
          </div>

          {/* Images */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Images</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="desktopImage">Desktop Image URL *</Label>
                <Input
                  id="desktopImage"
                  value={formData.desktopImage}
                  onChange={(e) => handleChange('desktopImage', e.target.value)}
                  placeholder="https://example.com/desktop-image.jpg"
                  className={errors.desktopImage ? 'border-red-500' : ''}
                />
                {errors.desktopImage && <p className="text-red-500 text-sm mt-1">{errors.desktopImage}</p>}
              </div>

              <div>
                <Label htmlFor="mobileImage">Mobile Image URL</Label>
                <Input
                  id="mobileImage"
                  value={formData.mobileImage}
                  onChange={(e) => handleChange('mobileImage', e.target.value)}
                  placeholder="https://example.com/mobile-image.jpg"
                />
              </div>
            </div>

            {/* Image Preview */}
            {formData.desktopImage && (
              <div>
                <Label>Preview</Label>
                <div className="relative w-full h-32 rounded-lg overflow-hidden border">
                  <img
                    src={formData.desktopImage}
                    alt={formData.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x200?text=Image+Not+Found';
                    }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Call to Action */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="ctaText">CTA Text *</Label>
              <Input
                id="ctaText"
                value={formData.ctaText}
                onChange={(e) => handleChange('ctaText', e.target.value)}
                placeholder="Shop Now"
                className={errors.ctaText ? 'border-red-500' : ''}
              />
              {errors.ctaText && <p className="text-red-500 text-sm mt-1">{errors.ctaText}</p>}
            </div>

            <div>
              <Label htmlFor="ctaLink">CTA Link *</Label>
              <Input
                id="ctaLink"
                value={formData.ctaLink}
                onChange={(e) => handleChange('ctaLink', e.target.value)}
                placeholder="/products"
                className={errors.ctaLink ? 'border-red-500' : ''}
              />
              {errors.ctaLink && <p className="text-red-500 text-sm mt-1">{errors.ctaLink}</p>}
            </div>
          </div>

          {/* Display Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="displayOrder">Display Order</Label>
              <Input
                id="displayOrder"
                type="number"
                value={formData.displayOrder}
                onChange={(e) => handleChange('displayOrder', parseInt(e.target.value) || 0)}
                placeholder="0"
              />
            </div>

            <div>
              <Label htmlFor="textAlignment">Text Alignment</Label>
              <Select value={formData.textAlignment} onValueChange={(value) => handleChange('textAlignment', value)}>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </Select>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="isActive">Active Banner</Label>
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleChange('isActive', checked)}
              />
            </div>
          </div>

          {/* Styling */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="textColor">Text Color</Label>
              <Input
                id="textColor"
                type="color"
                value={formData.textColor}
                onChange={(e) => handleChange('textColor', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="backgroundColor">Background Color</Label>
              <Input
                id="backgroundColor"
                value={formData.backgroundColor}
                onChange={(e) => handleChange('backgroundColor', e.target.value)}
                placeholder="transparent"
              />
            </div>

            <div>
              <Label htmlFor="overlayOpacity">Overlay Opacity</Label>
              <Input
                id="overlayOpacity"
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={formData.overlayOpacity}
                onChange={(e) => handleChange('overlayOpacity', parseFloat(e.target.value) || 0)}
              />
            </div>
          </div>

          {/* Tags */}
          <div>
            <Label htmlFor="tags">Tags</Label>
            <div className="flex gap-2 mb-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                placeholder="Add a tag"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag} variant="outline">
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-1">
              {formData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm cursor-pointer"
                  onClick={() => removeTag(tag)}
                >
                  {tag} ×
                </span>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              {editingBanner ? 'Update Banner' : 'Create Banner'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BannerModal;
