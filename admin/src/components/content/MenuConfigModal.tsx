import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, GripVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectItem } from '@/components/ui/select';

// Menu interfaces
interface MenuSubcategory {
  id: string;
  name: string;
  slug: string;
  path: string;
  isActive: boolean;
  order: number;
}

interface MenuCategory {
  id: string;
  name: string;
  slug: string;
  path: string;
  subcategories: MenuSubcategory[];
  featured: boolean;
  isActive: boolean;
  order: number;
  icon?: string;
  description?: string;
}

interface MenuImage {
  title: string;
  subtitle?: string;
  description?: string;
  imageUrl: string;
  linkUrl?: string;
  buttonText: string;
  isActive: boolean;
  position: 'mega-menu' | 'mobile-menu' | 'both';
}

interface MenuConfig {
  _id?: string;
  name: string;
  type: 'mega-menu' | 'mobile-menu' | 'both';
  categories: MenuCategory[];
  menuImage: MenuImage;
  isActive: boolean;
  version?: number;
}

interface MenuConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: MenuConfig) => void;
  editingConfig: MenuConfig | null;
}

const MenuConfigModal: React.FC<MenuConfigModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingConfig
}) => {
  const [formData, setFormData] = useState<MenuConfig>({
    name: '',
    type: 'both',
    categories: [],
    menuImage: {
      title: 'Best Sellers',
      subtitle: 'Trending Now',
      description: 'Discover our most popular items this season',
      imageUrl: 'https://picsum.photos/400/600?random=1',
      linkUrl: '/category/collections/bestsellers',
      buttonText: 'Shop Now',
      isActive: true,
      position: 'both'
    },
    isActive: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when modal opens/closes or editing config changes
  useEffect(() => {
    if (isOpen) {
      if (editingConfig) {
        setFormData(editingConfig);
      } else {
        setFormData({
          name: '',
          type: 'both',
          categories: [
            {
              id: 'clothing',
              name: 'Clothing',
              slug: 'clothing',
              path: '/category/clothing',
              subcategories: [
                { id: 'lehenga', name: 'Lehenga', slug: 'lehenga', path: '/category/clothing/lehenga', isActive: true, order: 1 },
                { id: 'sarees', name: 'Sarees', slug: 'sarees', path: '/category/clothing/sarees', isActive: true, order: 2 },
                { id: 'kurti', name: 'Kurti', slug: 'kurti', path: '/category/clothing/kurti', isActive: true, order: 3 }
              ],
              featured: true,
              isActive: true,
              order: 1,
              icon: 'shirt'
            },
            {
              id: 'fabrics',
              name: 'Fabrics',
              slug: 'fabrics',
              path: '/category/fabrics',
              subcategories: [
                { id: 'cotton', name: 'Cotton', slug: 'cotton', path: '/category/fabrics/cotton', isActive: true, order: 1 },
                { id: 'silk', name: 'Silk', slug: 'silk', path: '/category/fabrics/silk', isActive: true, order: 2 }
              ],
              featured: false,
              isActive: true,
              order: 2,
              icon: 'palette'
            }
          ],
          menuImage: {
            title: 'Best Sellers',
            subtitle: 'Trending Now',
            description: 'Discover our most popular items this season',
            imageUrl: 'https://picsum.photos/400/600?random=1',
            linkUrl: '/category/collections/bestsellers',
            buttonText: 'Shop Now',
            isActive: true,
            position: 'both'
          },
          isActive: true
        });
      }
      setErrors({});
    }
  }, [isOpen, editingConfig]);

  // Handle input changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle menu image changes
  const handleMenuImageChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      menuImage: { ...prev.menuImage, [field]: value }
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.menuImage.title.trim()) {
      newErrors.menuImageTitle = 'Menu image title is required';
    }

    if (!formData.menuImage.imageUrl.trim()) {
      newErrors.menuImageUrl = 'Menu image URL is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">
            {editingConfig ? 'Edit Menu Configuration' : 'Create Menu Configuration'}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Configuration Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="e.g., Main Navigation Menu"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            <div>
              <Label htmlFor="type">Menu Type</Label>
              <Select value={formData.type} onValueChange={(value) => handleChange('type', value)}>
                <SelectItem value="both">Both (Mega & Mobile)</SelectItem>
                <SelectItem value="mega-menu">Mega Menu Only</SelectItem>
                <SelectItem value="mobile-menu">Mobile Menu Only</SelectItem>
              </Select>
            </div>
          </div>

          {/* Active Status */}
          <div className="flex items-center justify-between">
            <Label htmlFor="isActive">Active Configuration</Label>
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => handleChange('isActive', checked)}
            />
          </div>

          {/* Menu Image Section */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4">Menu Image Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="menuImageTitle">Title *</Label>
                <Input
                  id="menuImageTitle"
                  value={formData.menuImage.title}
                  onChange={(e) => handleMenuImageChange('title', e.target.value)}
                  placeholder="e.g., Best Sellers"
                  className={errors.menuImageTitle ? 'border-red-500' : ''}
                />
                {errors.menuImageTitle && <p className="text-red-500 text-sm mt-1">{errors.menuImageTitle}</p>}
              </div>

              <div>
                <Label htmlFor="menuImageSubtitle">Subtitle</Label>
                <Input
                  id="menuImageSubtitle"
                  value={formData.menuImage.subtitle || ''}
                  onChange={(e) => handleMenuImageChange('subtitle', e.target.value)}
                  placeholder="e.g., Trending Now"
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="menuImageDescription">Description</Label>
                <Textarea
                  id="menuImageDescription"
                  value={formData.menuImage.description || ''}
                  onChange={(e) => handleMenuImageChange('description', e.target.value)}
                  placeholder="e.g., Discover our most popular items this season"
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="menuImageUrl">Image URL *</Label>
                <Input
                  id="menuImageUrl"
                  value={formData.menuImage.imageUrl}
                  onChange={(e) => handleMenuImageChange('imageUrl', e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className={errors.menuImageUrl ? 'border-red-500' : ''}
                />
                {errors.menuImageUrl && <p className="text-red-500 text-sm mt-1">{errors.menuImageUrl}</p>}
              </div>

              <div>
                <Label htmlFor="menuImageLink">Link URL</Label>
                <Input
                  id="menuImageLink"
                  value={formData.menuImage.linkUrl || ''}
                  onChange={(e) => handleMenuImageChange('linkUrl', e.target.value)}
                  placeholder="/category/collections/bestsellers"
                />
              </div>

              <div>
                <Label htmlFor="menuImageButton">Button Text</Label>
                <Input
                  id="menuImageButton"
                  value={formData.menuImage.buttonText}
                  onChange={(e) => handleMenuImageChange('buttonText', e.target.value)}
                  placeholder="Shop Now"
                />
              </div>

              <div>
                <Label htmlFor="menuImagePosition">Position</Label>
                <Select
                  value={formData.menuImage.position}
                  onValueChange={(value) => handleMenuImageChange('position', value)}
                >
                  <SelectItem value="both">Both Menus</SelectItem>
                  <SelectItem value="mega-menu">Mega Menu Only</SelectItem>
                  <SelectItem value="mobile-menu">Mobile Menu Only</SelectItem>
                </Select>
              </div>
            </div>

            {/* Image Preview */}
            <div className="mt-4">
              <Label>Preview</Label>
              <div className="relative w-32 h-32 rounded-lg overflow-hidden border">
                <img
                  src={formData.menuImage.imageUrl}
                  alt={formData.menuImage.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400x600?text=Image+Not+Found';
                  }}
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              {editingConfig ? 'Update Configuration' : 'Create Configuration'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MenuConfigModal;
