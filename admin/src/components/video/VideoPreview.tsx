import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Star, 
  Eye, 
  ThumbsUp, 
  Share2,
  Calendar,
  User,
  Tag,
  Settings
} from 'lucide-react';
import { type AdminVideo } from '@/services/adminVideoService';

interface VideoPreviewProps {
  video: AdminVideo;
  onClose: () => void;
}

const VideoPreview: React.FC<VideoPreviewProps> = ({ video, onClose }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(video.muted);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="space-y-6">
      {/* Video Player */}
      <div className="relative bg-black rounded-lg overflow-hidden">
        <video
          src={video.videoUrl}
          poster={video.thumbnailUrl}
          controls={video.showControls}
          autoPlay={video.autoplay}
          loop={video.loop}
          muted={isMuted}
          className="w-full h-64 md:h-96 object-cover"
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
        />
        
        {/* Custom Controls Overlay */}
        <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between text-white">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
              onClick={() => setIsMuted(!isMuted)}
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
            {video.duration && (
              <span className="text-sm">{video.duration}</span>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/20"
          >
            <Maximize className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Video Information */}
      <div className="space-y-4">
        {/* Title and Status */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h2 className="text-2xl font-bold">{video.title}</h2>
            <p className="text-gray-600 mt-1">{video.description}</p>
          </div>
          <div className="flex flex-col gap-2 ml-4">
            <Badge variant={video.isActive ? "default" : "secondary"}>
              {video.isActive ? 'Active' : 'Inactive'}
            </Badge>
            {video.isFeatured && (
              <Badge variant="outline" className="text-yellow-600">
                <Star className="h-3 w-3 mr-1" />
                Featured
              </Badge>
            )}
            {video.isPublished && (
              <Badge variant="outline" className="text-green-600">
                Published
              </Badge>
            )}
          </div>
        </div>

        {/* Analytics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Eye className="h-4 w-4 text-gray-500 mr-1" />
              <span className="text-sm text-gray-500">Views</span>
            </div>
            <div className="text-xl font-bold">{formatNumber(video.analytics.views)}</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <ThumbsUp className="h-4 w-4 text-gray-500 mr-1" />
              <span className="text-sm text-gray-500">Likes</span>
            </div>
            <div className="text-xl font-bold">{formatNumber(video.analytics.likes)}</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Share2 className="h-4 w-4 text-gray-500 mr-1" />
              <span className="text-sm text-gray-500">Shares</span>
            </div>
            <div className="text-xl font-bold">{formatNumber(video.analytics.shares)}</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Settings className="h-4 w-4 text-gray-500 mr-1" />
              <span className="text-sm text-gray-500">Engagement</span>
            </div>
            <div className="text-xl font-bold">{video.analytics.engagement_rate}%</div>
          </div>
        </div>

        {/* Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h3 className="font-semibold">Video Details</h3>
            
            <div className="flex items-center gap-2">
              <Tag className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">Category:</span>
              <Badge variant="outline">
                {video.category.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">Created:</span>
              <span className="text-sm">{formatDate(video.createdAt)}</span>
            </div>

            {video.publishedAt && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-500">Published:</span>
                <span className="text-sm">{formatDate(video.publishedAt)}</span>
              </div>
            )}

            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">Source:</span>
              <Badge variant="outline">
                {video.uploadSource.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">Display Order:</span>
              <span className="text-sm">{video.displayOrder}</span>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="font-semibold">Settings</h3>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Autoplay:</span>
                <Badge variant={video.autoplay ? "default" : "secondary"}>
                  {video.autoplay ? 'Yes' : 'No'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Show Controls:</span>
                <Badge variant={video.showControls ? "default" : "secondary"}>
                  {video.showControls ? 'Yes' : 'No'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Loop:</span>
                <Badge variant={video.loop ? "default" : "secondary"}>
                  {video.loop ? 'Yes' : 'No'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Muted by Default:</span>
                <Badge variant={video.muted ? "default" : "secondary"}>
                  {video.muted ? 'Yes' : 'No'}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Tags */}
        {video.tags.length > 0 && (
          <div>
            <h3 className="font-semibold mb-2">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {video.tags.map((tag, index) => (
                <Badge key={index} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* SEO Information */}
        {(video.seoTitle || video.seoDescription || video.altText) && (
          <div className="space-y-3">
            <h3 className="font-semibold">SEO & Accessibility</h3>
            
            {video.seoTitle && (
              <div>
                <span className="text-sm text-gray-500">SEO Title:</span>
                <p className="text-sm mt-1">{video.seoTitle}</p>
              </div>
            )}
            
            {video.seoDescription && (
              <div>
                <span className="text-sm text-gray-500">SEO Description:</span>
                <p className="text-sm mt-1">{video.seoDescription}</p>
              </div>
            )}
            
            {video.altText && (
              <div>
                <span className="text-sm text-gray-500">Alt Text:</span>
                <p className="text-sm mt-1">{video.altText}</p>
              </div>
            )}
          </div>
        )}

        {/* Related Content */}
        {(video.relatedProducts.length > 0 || video.relatedCategories.length > 0) && (
          <div className="space-y-3">
            <h3 className="font-semibold">Related Content</h3>
            
            {video.relatedCategories.length > 0 && (
              <div>
                <span className="text-sm text-gray-500">Related Categories:</span>
                <div className="flex flex-wrap gap-2 mt-1">
                  {video.relatedCategories.map((category, index) => (
                    <Badge key={index} variant="outline">
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {video.relatedProducts.length > 0 && (
              <div>
                <span className="text-sm text-gray-500">Related Products:</span>
                <p className="text-sm mt-1">{video.relatedProducts.length} products linked</p>
              </div>
            )}
          </div>
        )}

        {/* Moderation Notes */}
        {video.moderationNotes && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">Moderation Notes</h3>
            <p className="text-sm text-yellow-700">{video.moderationNotes}</p>
            {video.moderatedAt && (
              <p className="text-xs text-yellow-600 mt-2">
                Moderated on {formatDate(video.moderatedAt)}
              </p>
            )}
          </div>
        )}

        {/* Transcript */}
        {video.transcript && (
          <div className="space-y-2">
            <h3 className="font-semibold">Transcript</h3>
            <div className="p-4 bg-gray-50 rounded-lg max-h-40 overflow-y-auto">
              <p className="text-sm whitespace-pre-wrap">{video.transcript}</p>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-end pt-4 border-t">
        <Button onClick={onClose}>
          Close Preview
        </Button>
      </div>
    </div>
  );
};

export default VideoPreview;
