import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { X, Upload, Play } from 'lucide-react';
import { toast } from 'sonner';
import {
  createAdminVideo,
  updateAdminVideo,
  uploadVideoFile,
  uploadVideoThumbnail,
  type AdminVideo,
  type AdminVideoCreate
} from '@/services/adminVideoService';

interface VideoFormProps {
  video?: AdminVideo | null;
  onSuccess: () => void;
  onCancel: () => void;
}

const VideoForm: React.FC<VideoFormProps> = ({ video, onSuccess, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);
  const [uploadingThumbnail, setUploadingThumbnail] = useState(false);
  const [tags, setTags] = useState<string[]>(video?.tags || []);
  const [newTag, setNewTag] = useState('');
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [videoPreview, setVideoPreview] = useState<string>(video?.videoUrl || '');
  const [thumbnailPreview, setThumbnailPreview] = useState<string>(video?.thumbnailUrl || '');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<AdminVideoCreate>({
    defaultValues: {
      title: video?.title || '',
      description: video?.description || '',
      videoUrl: video?.videoUrl || '',
      thumbnailUrl: video?.thumbnailUrl || '',
      duration: video?.duration || '',
      category: video?.category || 'product_showcase',
      isActive: video?.isActive ?? true,
      isFeatured: video?.isFeatured ?? false,
      isPublished: video?.isPublished ?? false,
      displayOrder: video?.displayOrder || 0,
      autoplay: video?.autoplay ?? false,
      showControls: video?.showControls ?? true,
      loop: video?.loop ?? false,
      muted: video?.muted ?? true,
      seoTitle: video?.seoTitle || '',
      seoDescription: video?.seoDescription || '',
      altText: video?.altText || '',
      moderationNotes: video?.moderationNotes || '',
      transcript: video?.transcript || '',
    }
  });

  const categories = [
    { value: 'product_showcase', label: 'Product Showcase' },
    { value: 'fashion_tips', label: 'Fashion Tips' },
    { value: 'styling_guide', label: 'Styling Guide' },
    { value: 'behind_scenes', label: 'Behind the Scenes' },
    { value: 'customer_stories', label: 'Customer Stories' },
    { value: 'brand_story', label: 'Brand Story' },
    { value: 'tutorial', label: 'Tutorial' },
    { value: 'collection_launch', label: 'Collection Launch' },
    { value: 'seasonal', label: 'Seasonal' },
    { value: 'promotional', label: 'Promotional' },
    { value: 'testimonial', label: 'Testimonial' },
  ];

  // Handle video file upload
  const handleVideoUpload = async (file: File) => {
    if (!file) return;

    setUploadingVideo(true);
    try {
      const metadata = {
        title: watch('title') || 'Untitled Video',
        description: watch('description'),
        category: watch('category'),
        tags: tags,
      };

      const response = await uploadVideoFile(file, metadata);
      setVideoPreview(response.video.videoUrl);
      setValue('videoUrl', response.video.videoUrl);
      toast.success('Video uploaded successfully');
    } catch (error) {
      console.error('Error uploading video:', error);
      toast.error('Failed to upload video');
    } finally {
      setUploadingVideo(false);
    }
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = async (file: File) => {
    if (!file || !video?._id) return;

    setUploadingThumbnail(true);
    try {
      const response = await uploadVideoThumbnail(video._id, file);
      setThumbnailPreview(response.thumbnailUrl);
      setValue('thumbnailUrl', response.thumbnailUrl);
      toast.success('Thumbnail uploaded successfully');
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      toast.error('Failed to upload thumbnail');
    } finally {
      setUploadingThumbnail(false);
    }
  };

  // Handle tag management
  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Handle form submission
  const onSubmit = async (data: AdminVideoCreate) => {
    setLoading(true);
    try {
      const videoData = {
        ...data,
        tags,
      };

      if (video) {
        await updateAdminVideo(video._id, videoData);
        toast.success('Video updated successfully');
      } else {
        await createAdminVideo(videoData);
        toast.success('Video created successfully');
      }

      onSuccess();
    } catch (error) {
      console.error('Error saving video:', error);
      toast.error('Failed to save video');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              {...register('title', { required: 'Title is required' })}
              placeholder="Enter video title"
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              {...register('description', { required: 'Description is required' })}
              placeholder="Enter video description"
              rows={4}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="category">Category *</Label>
            <select
              id="category"
              {...register('category', { required: 'Category is required' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {categories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="text-sm text-red-600">{errors.category.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="duration">Duration</Label>
            <Input
              id="duration"
              {...register('duration')}
              placeholder="e.g., 2:30"
            />
          </div>
        </div>

        <div className="space-y-4">
          {/* Video Upload */}
          <div>
            <Label>Video File</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              {videoPreview ? (
                <div className="space-y-2">
                  <video
                    src={videoPreview}
                    controls
                    className="w-full h-32 object-cover rounded"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setVideoPreview('');
                      setValue('videoUrl', '');
                    }}
                  >
                    Remove Video
                  </Button>
                </div>
              ) : (
                <div className="text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-2">
                    <label htmlFor="video-upload" className="cursor-pointer">
                      <span className="text-blue-600 hover:text-blue-500">
                        Upload a video
                      </span>
                      <input
                        id="video-upload"
                        type="file"
                        accept="video/*"
                        className="hidden"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            setVideoFile(file);
                            handleVideoUpload(file);
                          }
                        }}
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">MP4, WebM up to 100MB</p>
                </div>
              )}
              {uploadingVideo && (
                <div className="text-center text-sm text-gray-500">
                  Uploading video...
                </div>
              )}
            </div>
          </div>

          {/* Video URL (alternative) */}
          <div>
            <Label htmlFor="videoUrl">Video URL (Alternative)</Label>
            <Input
              id="videoUrl"
              {...register('videoUrl')}
              placeholder="https://example.com/video.mp4"
            />
          </div>

          {/* Thumbnail Upload */}
          <div>
            <Label>Thumbnail</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              {thumbnailPreview ? (
                <div className="space-y-2">
                  <img
                    src={thumbnailPreview}
                    alt="Thumbnail"
                    className="w-full h-24 object-cover rounded"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setThumbnailPreview('');
                      setValue('thumbnailUrl', '');
                    }}
                  >
                    Remove Thumbnail
                  </Button>
                </div>
              ) : (
                <div className="text-center">
                  <label htmlFor="thumbnail-upload" className="cursor-pointer">
                    <span className="text-blue-600 hover:text-blue-500">
                      Upload thumbnail
                    </span>
                    <input
                      id="thumbnail-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          setThumbnailFile(file);
                          if (video?._id) {
                            handleThumbnailUpload(file);
                          } else {
                            // For new videos, just preview
                            const url = URL.createObjectURL(file);
                            setThumbnailPreview(url);
                          }
                        }
                      }}
                    />
                  </label>
                  <p className="text-xs text-gray-500">JPG, PNG up to 5MB</p>
                </div>
              )}
              {uploadingThumbnail && (
                <div className="text-center text-sm text-gray-500">
                  Uploading thumbnail...
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tags */}
      <div>
        <Label>Tags</Label>
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addTag();
                }
              }}
            />
            <Button type="button" onClick={addTag} variant="outline">
              Add
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {tag}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeTag(tag)}
                />
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="isActive">Active</Label>
            <Switch
              id="isActive"
              checked={watch('isActive')}
              onCheckedChange={(checked) => setValue('isActive', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="isFeatured">Featured</Label>
            <Switch
              id="isFeatured"
              checked={watch('isFeatured')}
              onCheckedChange={(checked) => setValue('isFeatured', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="isPublished">Published</Label>
            <Switch
              id="isPublished"
              checked={watch('isPublished')}
              onCheckedChange={(checked) => setValue('isPublished', checked)}
            />
          </div>

          <div>
            <Label htmlFor="displayOrder">Display Order</Label>
            <Input
              id="displayOrder"
              type="number"
              {...register('displayOrder', { valueAsNumber: true })}
              placeholder="0"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="autoplay">Autoplay</Label>
            <Switch
              id="autoplay"
              checked={watch('autoplay')}
              onCheckedChange={(checked) => setValue('autoplay', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="showControls">Show Controls</Label>
            <Switch
              id="showControls"
              checked={watch('showControls')}
              onCheckedChange={(checked) => setValue('showControls', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="loop">Loop</Label>
            <Switch
              id="loop"
              checked={watch('loop')}
              onCheckedChange={(checked) => setValue('loop', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="muted">Muted</Label>
            <Switch
              id="muted"
              checked={watch('muted')}
              onCheckedChange={(checked) => setValue('muted', checked)}
            />
          </div>
        </div>
      </div>

      {/* SEO */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">SEO & Metadata</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="seoTitle">SEO Title</Label>
            <Input
              id="seoTitle"
              {...register('seoTitle')}
              placeholder="SEO optimized title"
            />
          </div>

          <div>
            <Label htmlFor="altText">Alt Text</Label>
            <Input
              id="altText"
              {...register('altText')}
              placeholder="Alternative text for accessibility"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="seoDescription">SEO Description</Label>
          <Textarea
            id="seoDescription"
            {...register('seoDescription')}
            placeholder="SEO meta description"
            rows={2}
          />
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-4 pt-6 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? 'Saving...' : video ? 'Update Video' : 'Create Video'}
        </Button>
      </div>
    </form>
  );
};

export default VideoForm;
