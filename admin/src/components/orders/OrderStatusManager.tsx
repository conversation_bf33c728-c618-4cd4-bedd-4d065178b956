import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  XCircle, 
  RotateCcw,
  AlertTriangle,
  Calendar,
  User,
  Phone,
  MapPin
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Order {
  _id: string;
  orderNumber: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  shippingAddress?: {
    fullName?: string;
    phone?: string;
    city?: string;
    state?: string;
  };
  totals?: {
    finalTotal?: number;
    total?: number;
  };
  items: Array<{
    quantity: number;
    price: number;
    productDetails?: {
      name: string;
      product_id: string;
    };
  }>;
  statusHistory?: Array<{
    status: string;
    timestamp: string;
    message?: string;
    updatedBy?: string;
  }>;
}

interface OrderStatusManagerProps {
  order: Order;
  onStatusUpdate: (orderId: string, status: string, notes?: string) => Promise<void>;
  isUpdating?: boolean;
}

const OrderStatusManager: React.FC<OrderStatusManagerProps> = ({
  order,
  onStatusUpdate,
  isUpdating = false
}) => {
  const [selectedStatus, setSelectedStatus] = useState(order.status);
  const [notes, setNotes] = useState('');

  const statusOptions = [
    { value: 'pending', label: 'Pending', icon: Clock, color: 'bg-yellow-100 text-yellow-800' },
    { value: 'confirmed', label: 'Confirmed', icon: CheckCircle, color: 'bg-blue-100 text-blue-800' },
    { value: 'processing', label: 'Processing', icon: Package, color: 'bg-purple-100 text-purple-800' },
    { value: 'shipped', label: 'Shipped', icon: Truck, color: 'bg-indigo-100 text-indigo-800' },
    { value: 'delivered', label: 'Delivered', icon: CheckCircle, color: 'bg-green-100 text-green-800' },
    { value: 'cancelled', label: 'Cancelled', icon: XCircle, color: 'bg-red-100 text-red-800' },
    { value: 'returned', label: 'Returned', icon: RotateCcw, color: 'bg-orange-100 text-orange-800' },
    { value: 'refunded', label: 'Refunded', icon: AlertTriangle, color: 'bg-gray-100 text-gray-800' }
  ];

  const getCurrentStatusInfo = () => {
    return statusOptions.find(option => option.value === order.status) || statusOptions[0];
  };

  const getSelectedStatusInfo = () => {
    return statusOptions.find(option => option.value === selectedStatus) || statusOptions[0];
  };

  const handleStatusUpdate = async () => {
    if (selectedStatus === order.status && !notes.trim()) {
      toast.error('Please select a different status or add notes');
      return;
    }

    try {
      await onStatusUpdate(order._id, selectedStatus, notes.trim() || undefined);
      setNotes('');
      toast.success('Order status updated successfully!');
    } catch (error) {
      toast.error('Failed to update order status');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getOrderTotal = () => {
    return order.totals?.finalTotal || order.totals?.total || 0;
  };

  const currentStatusInfo = getCurrentStatusInfo();
  const selectedStatusInfo = getSelectedStatusInfo();
  const CurrentIcon = currentStatusInfo.icon;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CurrentIcon className="h-6 w-6" />
            <div>
              <span className="text-lg font-semibold">Order #{order.orderNumber}</span>
              <Badge className={`ml-3 ${currentStatusInfo.color}`}>
                {currentStatusInfo.label}
              </Badge>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Total</p>
            <p className="text-lg font-bold">₹{getOrderTotal().toFixed(2)}</p>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Order Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Created: {formatDate(order.createdAt)}</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <User className="h-4 w-4 mr-2" />
              <span>Customer ID: {order.userId}</span>
            </div>
            {order.shippingAddress?.phone && (
              <div className="flex items-center text-sm text-gray-600">
                <Phone className="h-4 w-4 mr-2" />
                <span>{order.shippingAddress.phone}</span>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            {order.shippingAddress && (
              <div className="flex items-start text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">{order.shippingAddress.fullName}</p>
                  <p>{order.shippingAddress.city}, {order.shippingAddress.state}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Order Items */}
        <div>
          <h4 className="font-medium text-gray-900 mb-2">Order Items ({order.items.length})</h4>
          <div className="space-y-2">
            {order.items.slice(0, 3).map((item, index) => (
              <div key={index} className="flex justify-between items-center text-sm bg-gray-50 p-2 rounded">
                <div>
                  <span className="font-medium">{item.productDetails?.name || 'Product'}</span>
                  <span className="text-gray-500 ml-2">({item.productDetails?.product_id})</span>
                </div>
                <div className="text-right">
                  <span className="font-medium">Qty: {item.quantity}</span>
                  <span className="text-gray-500 ml-2">₹{(item.price || 0).toFixed(2)}</span>
                </div>
              </div>
            ))}
            {order.items.length > 3 && (
              <p className="text-sm text-gray-500">... and {order.items.length - 3} more items</p>
            )}
          </div>
        </div>

        {/* Status Update Section */}
        <div className="border-t pt-4">
          <h4 className="font-medium text-gray-900 mb-3">Update Order Status</h4>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                New Status
              </label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => {
                    const Icon = option.icon;
                    return (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center">
                          <Icon className="h-4 w-4 mr-2" />
                          {option.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes (Optional)
              </label>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add any notes about this status update..."
                rows={3}
              />
            </div>

            <Button
              onClick={handleStatusUpdate}
              disabled={isUpdating || (selectedStatus === order.status && !notes.trim())}
              className="w-full"
            >
              {isUpdating ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Updating Status...
                </>
              ) : (
                <>
                  <selectedStatusInfo.icon className="h-4 w-4 mr-2" />
                  Update to {selectedStatusInfo.label}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Status History */}
        {order.statusHistory && order.statusHistory.length > 0 && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">Status History</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {order.statusHistory.map((status, index) => {
                const statusInfo = statusOptions.find(opt => opt.value === status.status);
                const StatusIcon = statusInfo?.icon || Clock;
                
                return (
                  <div key={index} className="flex items-start space-x-3 text-sm">
                    <StatusIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium capitalize">{status.status}</span>
                        <span className="text-gray-500">{formatDate(status.timestamp)}</span>
                      </div>
                      {status.message && (
                        <p className="text-gray-600 mt-1">{status.message}</p>
                      )}
                      {status.updatedBy && (
                        <p className="text-gray-500 text-xs mt-1">Updated by: {status.updatedBy}</p>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default OrderStatusManager;
