import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { updateAdminOrderTracking } from '@/services/adminOrderService';
import {
  Truck,
  Package,
  Calendar,
  ExternalLink,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface TrackingInfo {
  trackingNumber?: string;
  carrier?: string;
  estimatedDelivery?: string;
  trackingUrl?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  status: string;
  createdAt: string;
  items: Array<{
    productDetails?: {
      name: string;
    };
    quantity: number;
    tracking?: TrackingInfo;
  }>;
  delivery?: {
    estimatedDate?: string;
    type?: string;
  };
  timeline?: Array<{
    status: string;
    timestamp: string;
    message?: string;
    updatedBy?: string;
  }>;
}

interface TrackingManagerProps {
  order: Order;
  onTrackingUpdate?: (orderId: string, trackingData: TrackingInfo & { notes?: string }) => Promise<void>;
  isUpdating?: boolean;
}

const TrackingManager: React.FC<TrackingManagerProps> = ({
  order,
  onTrackingUpdate,
  isUpdating = false
}) => {
  const [trackingData, setTrackingData] = useState<TrackingInfo & { notes?: string }>({
    trackingNumber: order.items[0]?.tracking?.trackingNumber || '',
    carrier: order.items[0]?.tracking?.carrier || '',
    estimatedDelivery: order.delivery?.estimatedDate ? 
      new Date(order.delivery.estimatedDate).toISOString().split('T')[0] : '',
    trackingUrl: order.items[0]?.tracking?.trackingUrl || '',
    notes: ''
  });

  const carriers = [
    'India Post',
    'Blue Dart',
    'DTDC',
    'FedEx',
    'DHL',
    'Aramex',
    'Ecom Express',
    'Delhivery',
    'Xpressbees',
    'Ekart',
    'Other'
  ];

  const handleInputChange = (field: string, value: string) => {
    setTrackingData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    if (!trackingData.trackingNumber?.trim()) {
      toast.error('Please enter a tracking number');
      return;
    }

    if (!trackingData.carrier?.trim()) {
      toast.error('Please select a carrier');
      return;
    }

    try {
      // Use admin service directly if no callback provided
      if (onTrackingUpdate) {
        await onTrackingUpdate(order._id, trackingData);
      } else {
        await updateAdminOrderTracking(order._id, trackingData);
      }

      toast.success('Tracking information updated successfully!');

      // Clear notes after successful update
      setTrackingData(prev => ({ ...prev, notes: '' }));
    } catch (error) {
      console.error('Error updating tracking:', error);
      toast.error('Failed to update tracking information');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'shipped':
        return <Truck className="h-4 w-4 text-blue-500" />;
      case 'processing':
        return <Package className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'shipped':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-orange-100 text-orange-800';
      case 'confirmed':
        return 'bg-purple-100 text-purple-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Order #{order.orderNumber}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              {getStatusIcon(order.status)}
              <div>
                <p className="font-medium">Status</p>
                <Badge className={getStatusColor(order.status)}>
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <p className="font-medium">Order Date</p>
                <p className="text-sm text-gray-600">{formatDate(order.createdAt)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Package className="h-4 w-4 text-gray-500" />
              <div>
                <p className="font-medium">Items</p>
                <p className="text-sm text-gray-600">{order.items.length} item(s)</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Tracking Information */}
      {order.items.some(item => item.tracking?.trackingNumber) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Current Tracking Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {order.items.map((item, index) => (
                item.tracking?.trackingNumber && (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <p className="font-medium">{item.productDetails?.name || 'Product'}</p>
                        <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">Tracking Number</p>
                        <p className="text-gray-600 font-mono">{item.tracking.trackingNumber}</p>
                      </div>
                      {item.tracking.carrier && (
                        <div>
                          <p className="font-medium">Carrier</p>
                          <p className="text-gray-600">{item.tracking.carrier}</p>
                        </div>
                      )}
                    </div>
                    
                    {item.tracking.trackingUrl && (
                      <div className="mt-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(item.tracking.trackingUrl, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Track on Carrier Website
                        </Button>
                      </div>
                    )}
                  </div>
                )
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Update Tracking Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Update Tracking Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="trackingNumber">Tracking Number *</Label>
                <Input
                  id="trackingNumber"
                  value={trackingData.trackingNumber}
                  onChange={(e) => handleInputChange('trackingNumber', e.target.value)}
                  placeholder="Enter tracking number"
                  className="font-mono"
                />
              </div>
              
              <div>
                <Label htmlFor="carrier">Carrier *</Label>
                <Select
                  value={trackingData.carrier}
                  onValueChange={(value) => handleInputChange('carrier', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select carrier" />
                  </SelectTrigger>
                  <SelectContent>
                    {carriers.map((carrier) => (
                      <SelectItem key={carrier} value={carrier}>
                        {carrier}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="estimatedDelivery">Estimated Delivery Date</Label>
                <Input
                  id="estimatedDelivery"
                  type="date"
                  value={trackingData.estimatedDelivery}
                  onChange={(e) => handleInputChange('estimatedDelivery', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              
              <div>
                <Label htmlFor="trackingUrl">Tracking URL (Optional)</Label>
                <Input
                  id="trackingUrl"
                  value={trackingData.trackingUrl}
                  onChange={(e) => handleInputChange('trackingUrl', e.target.value)}
                  placeholder="https://track.carrier.com/..."
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                value={trackingData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Add any notes about this tracking update..."
                rows={3}
              />
            </div>
            
            <Separator />
            
            <div className="flex justify-end">
              <Button
                onClick={handleSubmit}
                disabled={isUpdating || !trackingData.trackingNumber?.trim() || !trackingData.carrier?.trim()}
                className="min-w-[120px]"
              >
                {isUpdating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Tracking
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Timeline */}
      {order.timeline && order.timeline.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Order Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {order.timeline.map((event, index) => (
                <div key={index} className="flex gap-4">
                  <div className="flex flex-col items-center">
                    {getStatusIcon(event.status)}
                    {index < order.timeline!.length - 1 && (
                      <div className="w-px h-8 bg-gray-200 mt-2" />
                    )}
                  </div>
                  <div className="flex-1 pb-4">
                    <div className="flex justify-between items-start">
                      <p className="font-medium capitalize">{event.status}</p>
                      <p className="text-xs text-gray-500">{formatDate(event.timestamp)}</p>
                    </div>
                    {event.message && (
                      <p className="text-sm text-gray-600 mt-1">{event.message}</p>
                    )}
                    {event.updatedBy && (
                      <p className="text-xs text-gray-400 mt-1">Updated by: {event.updatedBy}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TrackingManager;
