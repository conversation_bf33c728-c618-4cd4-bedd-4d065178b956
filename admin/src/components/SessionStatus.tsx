/**
 * Session Status Component
 * Shows current session information and time until expiry
 */

import * as React from "react";
import { ClockIcon, ShieldCheckIcon, AlertTriangleIcon } from "lucide-react";
import { 
  adminTokenExpiryService, 
  formatTimeUntilExpiry,
  type TokenStatus 
} from "@/services/adminTokenExpiryService";

interface SessionStatusProps {
  className?: string;
  showDetails?: boolean;
}

export function SessionStatus({ className = "", showDetails = false }: SessionStatusProps) {
  const [tokenStatus, setTokenStatus] = React.useState<TokenStatus | null>(null);

  React.useEffect(() => {
    // Update token status every minute
    const updateStatus = () => {
      const status = adminTokenExpiryService.getTokenStatus();
      setTokenStatus(status);
    };

    updateStatus(); // Initial update
    const interval = setInterval(updateStatus, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  if (!tokenStatus || !tokenStatus.isValid) {
    return null;
  }

  const isExpiringSoon = tokenStatus.willExpireSoon;
  const timeLeftFormatted = formatTimeUntilExpiry(tokenStatus.timeUntilExpiry);

  return (
    <div className={`flex items-center gap-2 text-sm ${className}`}>
      <div className={`p-1 rounded-full ${isExpiringSoon ? 'bg-yellow-100 dark:bg-yellow-900/30' : 'bg-green-100 dark:bg-green-900/30'}`}>
        {isExpiringSoon ? (
          <AlertTriangleIcon className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
        ) : (
          <ShieldCheckIcon className="h-3 w-3 text-green-600 dark:text-green-400" />
        )}
      </div>
      
      <div className="flex items-center gap-1">
        <ClockIcon className="h-3 w-3 text-muted-foreground" />
        <span className={`${isExpiringSoon ? 'text-yellow-600 dark:text-yellow-400' : 'text-muted-foreground'}`}>
          {timeLeftFormatted}
        </span>
      </div>

      {showDetails && tokenStatus.expiryDate && (
        <span className="text-xs text-muted-foreground">
          (expires {tokenStatus.expiryDate.toLocaleString()})
        </span>
      )}
    </div>
  );
}

/**
 * Hook to get current session status
 */
export function useSessionStatus() {
  const [tokenStatus, setTokenStatus] = React.useState<TokenStatus | null>(null);

  React.useEffect(() => {
    const updateStatus = () => {
      const status = adminTokenExpiryService.getTokenStatus();
      setTokenStatus(status);
    };

    updateStatus();
    const interval = setInterval(updateStatus, 60000);

    return () => clearInterval(interval);
  }, []);

  return tokenStatus;
}
