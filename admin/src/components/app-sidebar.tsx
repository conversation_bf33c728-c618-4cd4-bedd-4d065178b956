import * as React from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import {
  BarChart3Icon,
  BoxIcon,
  HomeIcon,
  PackageIcon,
  ShoppingCartIcon,
  UsersIcon,
  SettingsIcon,
  LogOutIcon,
  TagIcon,
  ClipboardListIcon,
  TruckIcon,
  CreditCardIcon,
  BellIcon,
  UserIcon,
  StoreIcon,
  PercentIcon,
  LineChartIcon,
  PieChartIcon,
  UserPlusIcon,
  SearchIcon,
  HelpCircleIcon,
  GlobeIcon,
  MessageSquareIcon,
  XIcon,
  PlayIcon,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarSeparator,
  SidebarInput,
} from "@/components/ui/sidebar";

export function AppSidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const isActive = (path: string) => location.pathname === path;
  const isActiveStartsWith = (path: string) => location.pathname.startsWith(path);
  const [searchTerm, setSearchTerm] = React.useState("");
  const [searchResults, setSearchResults] = React.useState<Array<{path: string, label: string, icon: React.ReactNode}>>([]);
  const [showSearchResults, setShowSearchResults] = React.useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = React.useState(false);

  const { logout, user } = useAuth();

  // Define menu items for search
  const menuItems = React.useMemo(() => [
    { path: "/", label: "Dashboard", icon: <HomeIcon className="h-4 w-4" /> },
    { path: "/products", label: "Products", icon: <PackageIcon className="h-4 w-4" /> },
    { path: "/products/add", label: "Add Product", icon: <PackageIcon className="h-4 w-4" /> },
    { path: "/products/bulk-upload", label: "Bulk Upload", icon: <PackageIcon className="h-4 w-4" /> },
    { path: "/products/categories", label: "Product Categories", icon: <TagIcon className="h-4 w-4" /> },
    { path: "/products/inventory", label: "Inventory", icon: <ClipboardListIcon className="h-4 w-4" /> },
    { path: "/orders", label: "Orders", icon: <ShoppingCartIcon className="h-4 w-4" /> },
    { path: "/orders/pending", label: "Pending Orders", icon: <ShoppingCartIcon className="h-4 w-4" /> },
    { path: "/orders/processing", label: "Processing Orders", icon: <ShoppingCartIcon className="h-4 w-4" /> },
    { path: "/orders/completed", label: "Completed Orders", icon: <ShoppingCartIcon className="h-4 w-4" /> },
    { path: "/customers", label: "Customers", icon: <UsersIcon className="h-4 w-4" /> },
    { path: "/content/top-headers", label: "Top Headers", icon: <MessageSquareIcon className="h-4 w-4" /> },
    { path: "/content/menu-config", label: "Menu Configuration", icon: <GlobeIcon className="h-4 w-4" /> },
    { path: "/content/banners", label: "Banner Management", icon: <MessageSquareIcon className="h-4 w-4" /> },
    { path: "/videos", label: "Video Management", icon: <PlayIcon className="h-4 w-4" /> },
    { path: "/analytics", label: "Analytics", icon: <BarChart3Icon className="h-4 w-4" /> },
    { path: "/analytics/sales", label: "Sales Report", icon: <LineChartIcon className="h-4 w-4" /> },
    { path: "/analytics/products", label: "Products Report", icon: <PieChartIcon className="h-4 w-4" /> },
    { path: "/analytics/customers", label: "Customers Report", icon: <UserPlusIcon className="h-4 w-4" /> },
    { path: "/settings", label: "Settings", icon: <SettingsIcon className="h-4 w-4" /> },
    { path: "/settings/profile", label: "Profile Settings", icon: <UserIcon className="h-4 w-4" /> },
    { path: "/settings/store", label: "Store Settings", icon: <StoreIcon className="h-4 w-4" /> },
    { path: "/settings/payment", label: "Payment Settings", icon: <CreditCardIcon className="h-4 w-4" /> },
    { path: "/settings/shipping", label: "Shipping Settings", icon: <TruckIcon className="h-4 w-4" /> },
    { path: "/settings/tax", label: "Tax Settings", icon: <PercentIcon className="h-4 w-4" /> },
    { path: "/settings/notifications", label: "Notification Settings", icon: <BellIcon className="h-4 w-4" /> },
    { path: "/settings/users", label: "User Settings", icon: <UsersIcon className="h-4 w-4" /> },
  ], []);

  // Handle search
  const handleSearch = React.useCallback((term: string) => {
    setSearchTerm(term);

    if (term.trim() === "") {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    const results = menuItems.filter(item =>
      item.label.toLowerCase().includes(term.toLowerCase())
    );

    setSearchResults(results);
    setShowSearchResults(true);
  }, [menuItems]);

  // Handle search result click
  const handleSearchResultClick = (path: string) => {
    navigate(path);
    setSearchTerm("");
    setShowSearchResults(false);
  };

  // Close search results when clicking outside
  React.useEffect(() => {
    const handleClickOutside = () => {
      setShowSearchResults(false);
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
  };

  const handleConfirmLogout = () => {
    // Call the logout function from auth context
    logout();
    // Navigate to login page
    navigate("/login");
    // Close the confirmation dialog
    setShowLogoutConfirm(false);
  };

  const handleCancelLogout = () => {
    setShowLogoutConfirm(false);
  };

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex items-center gap-3 px-3 py-4 mb-1 border-b border-border/40">
          <div className="bg-gradient-to-br from-primary/20 to-primary/40 p-2.5 rounded-lg shadow-sm">
            <BoxIcon className="h-6 w-6 text-primary-foreground" />
          </div>
          <div className="flex flex-col">
            <span className="text-lg font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">Sajawat Sarees</span>
            <span className="text-xs text-muted-foreground flex items-center gap-1.5">
              <span className="inline-block h-1.5 w-1.5 rounded-full bg-green-500 animate-pulse"></span>
              Admin Dashboard
            </span>
          </div>
        </div>

        {user && (
          <div className="mx-2 mb-3 p-3 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border border-primary/10 shadow-sm flex items-center gap-3 transition-all duration-300 hover:shadow-md hover:border-primary/20">
            <div className="bg-gradient-to-br from-primary/20 to-primary/30 p-2 rounded-full shadow-inner flex items-center justify-center">
              <UserIcon className="h-5 w-5 text-primary" />
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-semibold truncate">{user.name}</span>
              <span className="text-xs text-muted-foreground truncate flex items-center gap-1">
                <span className="inline-block h-1.5 w-1.5 rounded-full bg-green-500"></span>
                {user.role}
              </span>
            </div>
            <div className="ml-auto">
              <Link to="/settings/profile" className="text-xs text-primary/70 hover:text-primary transition-colors">
                <SettingsIcon className="h-3.5 w-3.5" />
              </Link>
            </div>
          </div>
        )}
        <div className="px-2 pb-3 relative">
          <div className="relative group">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors duration-200" />
            <SidebarInput
              placeholder="Search dashboard..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="bg-muted/30 pl-10 pr-4 border border-border/50 focus:border-primary/30 focus:ring-1 focus:ring-primary/20 transition-all duration-200 shadow-sm"
              onClick={(e) => {
                e.stopPropagation();
                if (searchTerm.trim() !== "") {
                  setShowSearchResults(true);
                }
              }}
            />
            {searchTerm && (
              <button
                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-primary transition-colors duration-200"
                onClick={() => {
                  setSearchTerm("");
                  setSearchResults([]);
                  setShowSearchResults(false);
                }}
              >
                <XIcon className="h-4 w-4" />
              </button>
            )}
          </div>

          {showSearchResults && searchResults.length > 0 && (
            <div
              className="absolute z-50 mt-1 w-full bg-card rounded-md shadow-lg border border-border/60 overflow-hidden animate-in fade-in-50 duration-200"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="max-h-64 overflow-y-auto py-1">
                <div className="px-3 py-1.5 text-xs font-medium text-muted-foreground border-b border-border/60">
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''}
                </div>
                {searchResults.map((result, index) => (
                  <button
                    key={index}
                    className="w-full text-left px-3 py-2.5 hover:bg-primary/5 flex items-center gap-2.5 text-sm transition-colors duration-150 border-l-2 border-transparent hover:border-primary/50"
                    onClick={() => handleSearchResultClick(result.path)}
                  >
                    <div className="text-primary/70">{result.icon}</div>
                    <span className="font-medium">{result.label}</span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {searchTerm && searchResults.length === 0 && (
            <div className="absolute z-50 mt-1 w-full bg-card rounded-md shadow-lg border border-border/60 overflow-hidden animate-in fade-in-50 duration-200 p-4 text-center">
              <div className="text-muted-foreground text-sm">No results found for "{searchTerm}"</div>
            </div>
          )}
        </div>
      </SidebarHeader>
      <SidebarContent className="px-1.5">
        <div className="mb-2 px-3">
          <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Main Navigation</h3>
        </div>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/")}
              tooltip="Dashboard"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
            >
              <Link to="/">
                <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                  <HomeIcon className="h-[1.15rem] w-[1.15rem]" />
                </div>
                <span>Dashboard</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActiveStartsWith("/products")}
              tooltip="Products"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
            >
              <Link to="/products">
                <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                  <PackageIcon className="h-[1.15rem] w-[1.15rem]" />
                </div>
                <span>Products</span>
                <span className="ml-auto bg-primary/10 text-primary text-xs px-1.5 py-0.5 rounded-full font-medium">
                  New
                </span>
              </Link>
            </SidebarMenuButton>
            <SidebarMenuSub>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/products/add")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/products/add" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Add New Product
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/products/bulk-upload")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/products/bulk-upload" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Bulk Upload
                    <span className="ml-auto bg-blue-100 text-blue-700 text-xs px-1.5 py-0.5 rounded-full font-medium">
                      CSV
                    </span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/products/categories")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/products/categories" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Categories
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/products/inventory")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/products/inventory" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Inventory
                    <span className="ml-auto bg-amber-100 text-amber-700 text-xs px-1.5 py-0.5 rounded-full font-medium">
                      5 Low
                    </span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            </SidebarMenuSub>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActiveStartsWith("/orders")}
              tooltip="Orders"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
            >
              <Link to="/orders">
                <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                  <ShoppingCartIcon className="h-[1.15rem] w-[1.15rem]" />
                </div>
                <span>Orders</span>
                <span className="ml-auto bg-red-100 text-red-700 text-xs px-1.5 py-0.5 rounded-full font-medium">
                  3 New
                </span>
              </Link>
            </SidebarMenuButton>
            <SidebarMenuSub>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/orders/pending")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/orders/pending" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Pending
                    <span className="ml-auto bg-red-100 text-red-700 text-xs px-1.5 py-0.5 rounded-full font-medium">
                      3
                    </span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/orders/processing")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/orders/processing" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Processing
                    <span className="ml-auto bg-blue-100 text-blue-700 text-xs px-1.5 py-0.5 rounded-full font-medium">
                      7
                    </span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/orders/completed")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/orders/completed" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Completed
                    <span className="ml-auto bg-green-100 text-green-700 text-xs px-1.5 py-0.5 rounded-full font-medium">
                      12
                    </span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            </SidebarMenuSub>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActiveStartsWith("/customers")}
              tooltip="Customers"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
            >
              <Link to="/customers">
                <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                  <UsersIcon className="h-[1.15rem] w-[1.15rem]" />
                </div>
                <span>Customers</span>
                <span className="ml-auto text-xs text-muted-foreground">
                  12
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActiveStartsWith("/content")}
              tooltip="Content"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
            >
              <Link to="/content/top-headers">
                <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                  <MessageSquareIcon className="h-[1.15rem] w-[1.15rem]" />
                </div>
                <span>Content</span>
              </Link>
            </SidebarMenuButton>
            <SidebarMenuSub>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/content/top-headers")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/content/top-headers" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Top Headers
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/content/menu-config")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/content/menu-config" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Menu Configuration
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/content/banners")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/content/banners" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    Banner Management
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            </SidebarMenuSub>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActiveStartsWith("/videos")}
              tooltip="Video Management"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
            >
              <Link to="/videos">
                <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                  <PlayIcon className="h-[1.15rem] w-[1.15rem]" />
                </div>
                <span>Videos</span>
                <span className="ml-auto bg-purple-100 text-purple-700 text-xs px-1.5 py-0.5 rounded-full font-medium">
                  New
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActiveStartsWith("/analytics")}
              tooltip="Analytics"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
            >
              <Link to="/analytics">
                <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                  <BarChart3Icon className="h-[1.15rem] w-[1.15rem]" />
                </div>
                <span>Analytics</span>
              </Link>
            </SidebarMenuButton>
            <SidebarMenuSub>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/analytics/sales")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/analytics/sales" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    <LineChartIcon className="h-3.5 w-3.5 text-primary/70" />
                    <span>Sales Report</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/analytics/products")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/analytics/products" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    <PieChartIcon className="h-3.5 w-3.5 text-primary/70" />
                    <span>Products Report</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/analytics/customers")}
                  className="hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium transition-colors"
                >
                  <Link to="/analytics/customers" className="flex items-center gap-2">
                    <div className="h-1 w-1 rounded-full bg-primary/50"></div>
                    <UserPlusIcon className="h-3.5 w-3.5 text-primary/70" />
                    <span>Customers Report</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            </SidebarMenuSub>
          </SidebarMenuItem>
        </SidebarMenu>

        <SidebarSeparator className="my-3" />

        <div className="mb-2 px-3">
          <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">System</h3>
        </div>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActiveStartsWith("/settings")}
              tooltip="Settings"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
            >
              <Link to="/settings">
                <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                  <SettingsIcon className="h-[1.15rem] w-[1.15rem]" />
                </div>
                <span>Settings</span>
              </Link>
            </SidebarMenuButton>
            <SidebarMenuSub>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/settings/profile")}
                >
                  <Link to="/settings/profile">
                    <UserIcon className="h-4 w-4" />
                    <span>Profile</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/settings/store")}
                >
                  <Link to="/settings/store">
                    <StoreIcon className="h-4 w-4" />
                    <span>Store</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/settings/payment")}
                >
                  <Link to="/settings/payment">
                    <CreditCardIcon className="h-4 w-4" />
                    <span>Payment</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/settings/shipping")}
                >
                  <Link to="/settings/shipping">
                    <TruckIcon className="h-4 w-4" />
                    <span>Shipping</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/settings/tax")}
                >
                  <Link to="/settings/tax">
                    <PercentIcon className="h-4 w-4" />
                    <span>Tax</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/settings/notifications")}
                >
                  <Link to="/settings/notifications">
                    <BellIcon className="h-4 w-4" />
                    <span>Notifications</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
              <SidebarMenuSubItem>
                <SidebarMenuSubButton
                  asChild
                  isActive={isActive("/settings/users")}
                >
                  <Link to="/settings/users">
                    <UsersIcon className="h-4 w-4" />
                    <span>Users</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            </SidebarMenuSub>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              tooltip="Help"
              className="group transition-all duration-200 hover:bg-primary/5 data-[active=true]:bg-primary/10 data-[active=true]:text-primary data-[active=true]:font-medium"
              onClick={() => {
                // Open in a new window and handle potential errors
                const helpUrl = "https://sajawatsarees.com/help";
                const newWindow = window.open(helpUrl, "_blank", "noopener,noreferrer");

                // If the window couldn't be opened (e.g., popup blocked), provide fallback
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                  alert(`Please visit our help center at: ${helpUrl}`);
                }
              }}
            >
              <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-primary group-data-[active=true]:text-primary transition-colors">
                <HelpCircleIcon className="h-[1.15rem] w-[1.15rem]" />
              </div>
              <span>Help Center</span>
              <span className="ml-auto text-xs px-1.5 py-0.5 rounded-full font-medium bg-gradient-to-r from-primary/20 to-primary/30 text-primary">
                Support
              </span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="border-t border-border/40 pt-2">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              tooltip="Logout"
              onClick={handleLogoutClick}
              className="group transition-all duration-200 hover:bg-red-50 hover:text-red-600 hover:font-medium"
            >
              <div className="flex items-center justify-center h-5 w-5 mr-2 text-muted-foreground group-hover:text-red-500 transition-colors">
                <LogOutIcon className="h-[1.15rem] w-[1.15rem]" />
              </div>
              <span>Logout</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
        <div className="p-3 text-xs text-center text-muted-foreground flex items-center justify-center gap-1.5">
          <GlobeIcon className="h-3 w-3 text-primary/60" />
          <span>Sajawat Sarees Admin v1.0.0</span>
        </div>
      </SidebarFooter>

      {/* Logout Confirmation Dialog */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm animate-in fade-in-0 duration-200">
          <div className="bg-card p-6 rounded-xl shadow-xl max-w-md w-full border border-border/60 animate-in zoom-in-95 duration-150">
            <div className="flex items-center gap-3 text-red-500 mb-5">
              <div className="bg-red-50 p-2 rounded-full">
                <LogOutIcon className="h-6 w-6" />
              </div>
              <h2 className="text-xl font-semibold">Confirm Logout</h2>
            </div>
            <p className="mb-6 text-muted-foreground">
              Are you sure you want to log out? Any unsaved changes will be lost.
            </p>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={handleCancelLogout}
                className="border-border hover:bg-muted/50 transition-colors"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmLogout}
                className="bg-red-500 hover:bg-red-600 transition-colors"
              >
                <LogOutIcon className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      )}
    </Sidebar>
  );
}
