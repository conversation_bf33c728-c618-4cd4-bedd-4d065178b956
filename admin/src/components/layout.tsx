import * as React from "react";
import { Outlet } from "react-router-dom";
import { SidebarProvider, SidebarTrigger, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { BellIcon, MoonIcon, SunIcon, UserIcon, SearchIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

export function Layout() {
  const [isDarkMode, setIsDarkMode] = React.useState(false);
  const [notifications, setNotifications] = React.useState(3);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Toggle dark mode
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
    // In a real app, you would apply dark mode to the document here
    if (!isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full bg-background dark:bg-gray-950 transition-colors duration-200">
        <AppSidebar />
        <main className="flex-1 w-full overflow-x-hidden">
          {/* Header */}
          <div className="sticky top-0 z-10 bg-background/80 dark:bg-gray-950/80 backdrop-blur-sm p-4 border-b border-border">
            <div className="flex justify-between items-center max-w-[1600px] mx-auto">
              <div className="flex items-center gap-4">
                <SidebarTrigger className="p-2 rounded-lg hover:bg-muted transition-colors" />

                {/* Search bar - visible on larger screens */}
                <div className="hidden md:flex relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    <SearchIcon className="h-4 w-4" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 pr-4 py-2 bg-muted/50 dark:bg-gray-800/50 rounded-lg w-[300px] focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                  />
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* Dark mode toggle */}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleDarkMode}
                  className="rounded-lg"
                >
                  {isDarkMode ? (
                    <SunIcon className="h-5 w-5" />
                  ) : (
                    <MoonIcon className="h-5 w-5" />
                  )}
                  <span className="sr-only">Toggle dark mode</span>
                </Button>

                {/* Notifications */}
                <Button variant="ghost" size="icon" className="rounded-lg relative">
                  <BellIcon className="h-5 w-5" />
                  {notifications > 0 && (
                    <span className="absolute top-1 right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                      {notifications}
                    </span>
                  )}
                  <span className="sr-only">Notifications</span>
                </Button>

                {/* User profile */}
                <div className="flex items-center gap-2">
                  <div className="hidden sm:block text-right">
                    <p className="text-sm font-medium">Admin User</p>
                    <p className="text-xs text-muted-foreground">Administrator</p>
                  </div>
                  <Button variant="ghost" size="icon" className="rounded-full bg-primary/10">
                    <UserIcon className="h-5 w-5 text-primary" />
                    <span className="sr-only">User profile</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile search - visible only on small screens */}
          <div className="block md:hidden p-2 bg-background dark:bg-gray-950 border-b border-border">
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                <SearchIcon className="h-4 w-4" />
              </div>
              <input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 pr-4 py-2 bg-muted/50 dark:bg-gray-800/50 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
              />
            </div>
          </div>

          {/* Main content */}
          <div className="p-4 sm:p-6">
            <Outlet />
          </div>

          {/* Footer */}
          <footer className="border-t border-border py-4 px-6 text-center text-sm text-muted-foreground">
            <p>© 2023 Sajawat Sarees. All rights reserved.</p>
          </footer>
        </main>
      </div>
    </SidebarProvider>
  );
}
