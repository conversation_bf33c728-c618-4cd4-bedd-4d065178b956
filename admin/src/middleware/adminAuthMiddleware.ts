/**
 * Admin Authentication Middleware
 * Handles JWT token validation and automatic refresh for admin panel
 */

import { adminTokenService, isAdminAuthenticated } from '../services/adminTokenService';
import { verifyAdminAuth, clearAdminAuth } from '../services/adminAuthService';

/**
 * Check if admin is authenticated before accessing protected routes
 */
export const requireAdminAuth = async (): Promise<boolean> => {
  try {
    // First check local token
    if (!isAdminAuthenticated()) {
      return false;
    }

    // If token will expire soon, try to refresh or verify
    if (adminTokenService.willExpireSoon()) {
      try {
        // Try to verify with backend
        await verifyAdminAuth();
        return true;
      } catch (error) {
        // Token is invalid, clear it
        clearAdminAuth();
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Admin auth check failed:', error);
    clearAdminAuth();
    return false;
  }
};

/**
 * Redirect to login if not authenticated
 */
export const redirectToLoginIfNotAuth = async (): Promise<void> => {
  const isAuth = await requireAdminAuth();
  if (!isAuth) {
    window.location.href = '/admin/login';
  }
};

/**
 * Higher-order component for protecting admin routes
 */
export const withAdminAuth = <T extends object>(
  WrappedComponent: React.ComponentType<T>
): React.ComponentType<T> => {
  return (props: T) => {
    const [isChecking, setIsChecking] = React.useState(true);
    const [isAuthenticated, setIsAuthenticated] = React.useState(false);

    React.useEffect(() => {
      const checkAuth = async () => {
        const authResult = await requireAdminAuth();
        setIsAuthenticated(authResult);
        setIsChecking(false);

        if (!authResult) {
          window.location.href = '/admin/login';
        }
      };

      checkAuth();
    }, []);

    if (isChecking) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return null; // Will redirect to login
    }

    return <WrappedComponent {...props} />;
  };
};

/**
 * Hook for checking admin authentication status
 */
export const useAdminAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);
  const [user, setUser] = React.useState(null);

  React.useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsLoading(true);
        const authResult = await requireAdminAuth();
        setIsAuthenticated(authResult);
        
        if (authResult) {
          const storedUser = adminTokenService.getStoredUser();
          setUser(storedUser);
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  return {
    isAuthenticated,
    isLoading,
    user,
    checkAuth: requireAdminAuth,
    logout: clearAdminAuth
  };
};

/**
 * Token refresh utility
 */
export const refreshTokenIfNeeded = async (): Promise<boolean> => {
  try {
    if (adminTokenService.willExpireSoon()) {
      // Try to verify/refresh token
      await verifyAdminAuth();
      return true;
    }
    return true;
  } catch (error) {
    console.error('Token refresh failed:', error);
    clearAdminAuth();
    return false;
  }
};

/**
 * Setup automatic token refresh
 */
export const setupTokenRefresh = (): (() => void) => {
  const interval = setInterval(async () => {
    if (isAdminAuthenticated()) {
      await refreshTokenIfNeeded();
    }
  }, 5 * 60 * 1000); // Check every 5 minutes

  // Return cleanup function
  return () => clearInterval(interval);
};
