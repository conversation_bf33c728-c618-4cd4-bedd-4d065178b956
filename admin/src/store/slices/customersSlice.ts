import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import {
  getAdminCustomers,
  getAdminCustomer,
  updateAdminCustomer,
  deleteAdminCustomer,
  type AdminCustomer,
  type AdminCustomerFilters
} from '@/services/adminCustomerService';

export interface Customer {
  _id: string;
  email?: string;
  phone?: string;
  isVerified: boolean;
  profile: {
    firstName?: string;
    lastName?: string;
    dateOfBirth?: string;
    gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
    profilePhoto?: {
      url: string;
      publicId: string;
    };
    bio?: string;
  };
  preferences: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
      marketing: boolean;
    };
  };
  addresses?: Array<{
    _id: string;
    type: 'home' | 'work' | 'other';
    label?: string;
    fullName: string;
    phone: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    isDefault: boolean;
    isActive: boolean;
  }>;
  orderStats: {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: string;
  };
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  fullName: string;
  // Legacy fields for compatibility
  id?: number;
  name?: string;
  orders?: number;
  totalSpent?: number;
}

interface CustomersState {
  items: Customer[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: AdminCustomerFilters;
}

const initialState: CustomersState = {
  items: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  },
  filters: {}
};

// Async thunks for API calls
export const fetchCustomers = createAsyncThunk(
  'customers/fetchCustomers',
  async (filters: AdminCustomerFilters = {}, { rejectWithValue }) => {
    try {
      const response = await getAdminCustomers(filters);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch customers');
    }
  }
);

export const fetchCustomerById = createAsyncThunk(
  'customers/fetchCustomerById',
  async (customerId: string, { rejectWithValue }) => {
    try {
      const response = await getAdminCustomer(customerId);
      return response; // Return the entire response to let the reducer handle data extraction
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch customer');
    }
  }
);

export const updateCustomerAsync = createAsyncThunk(
  'customers/updateCustomer',
  async ({ id, customerData }: { id: string; customerData: any }, { rejectWithValue }) => {
    try {
      const response = await updateAdminCustomer(id, customerData);
      return response; // Return the entire response to let the reducer handle data extraction
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update customer');
    }
  }
);

export const deleteCustomerAsync = createAsyncThunk(
  'customers/deleteCustomer',
  async ({ id, reason }: { id: string; reason?: string }, { rejectWithValue }) => {
    try {
      await deleteAdminCustomer(id, reason);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete customer');
    }
  }
);

const customersSlice = createSlice({
  name: 'customers',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<AdminCustomerFilters>) => {
      state.filters = action.payload;
    },
    setPagination: (state, action: PayloadAction<{ page: number; limit: number }>) => {
      state.pagination.page = action.payload.page;
      state.pagination.limit = action.payload.limit;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch customers
    builder
      .addCase(fetchCustomers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCustomers.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        // Transform backend data to match frontend interface
        const customers = action.payload.data?.customers || action.payload.customers || [];
        state.items = customers.map((customer: AdminCustomer) => ({
          ...customer,
          // Add legacy compatibility fields
          id: customer._id ? parseInt(customer._id.slice(-6), 16) : 0, // Generate numeric ID from MongoDB ObjectId
          name: customer.fullName || `${customer.profile?.firstName || ''} ${customer.profile?.lastName || ''}`.trim() || 'N/A',
          orders: customer.orderStats?.totalOrders || 0,
          totalSpent: customer.orderStats?.totalSpent || 0
        }));
        state.pagination = action.payload.data?.pagination || action.payload.pagination || state.pagination;
      })
      .addCase(fetchCustomers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Fetch customer by ID
    builder
      .addCase(fetchCustomerById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCustomerById.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        // Handle case where payload might be undefined
        if (!action.payload) {
          state.error = 'No customer data received';
          return;
        }

        const customer = action.payload.data?.customer || action.payload.customer || action.payload;

        // Ensure customer data exists
        if (!customer || !customer._id) {
          state.error = 'Invalid customer data received';
          return;
        }

        const existingIndex = state.items.findIndex(item => item._id === customer._id);
        const transformedCustomer = {
          ...customer,
          id: customer._id ? parseInt(customer._id.slice(-6), 16) : 0,
          name: customer.fullName || `${customer.profile?.firstName || ''} ${customer.profile?.lastName || ''}`.trim() || 'N/A',
          orders: customer.orderStats?.totalOrders || 0,
          totalSpent: customer.orderStats?.totalSpent || 0
        };

        if (existingIndex !== -1) {
          state.items[existingIndex] = transformedCustomer;
        } else {
          state.items.push(transformedCustomer);
        }
      })
      .addCase(fetchCustomerById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Update customer
    builder
      .addCase(updateCustomerAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCustomerAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        // Handle case where payload might be undefined
        if (!action.payload) {
          state.error = 'No customer data received';
          return;
        }

        const customer = action.payload.data?.customer || action.payload.customer || action.payload;

        // Ensure customer data exists
        if (!customer || !customer._id) {
          state.error = 'Invalid customer data received';
          return;
        }

        const index = state.items.findIndex(item => item._id === customer._id);
        if (index !== -1) {
          state.items[index] = {
            ...customer,
            id: customer._id ? parseInt(customer._id.slice(-6), 16) : 0,
            name: customer.fullName || `${customer.profile?.firstName || ''} ${customer.profile?.lastName || ''}`.trim() || 'N/A',
            orders: customer.orderStats?.totalOrders || 0,
            totalSpent: customer.orderStats?.totalSpent || 0
          };
        }
      })
      .addCase(updateCustomerAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Delete customer
    builder
      .addCase(deleteCustomerAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteCustomerAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.items = state.items.filter(item => item._id !== action.payload);
      })
      .addCase(deleteCustomerAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const {
  setFilters,
  setPagination,
  setLoading,
  setError,
  clearError
} = customersSlice.actions;

export default customersSlice.reducer;
