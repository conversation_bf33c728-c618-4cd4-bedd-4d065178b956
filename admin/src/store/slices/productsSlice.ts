import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import AdminProductService, { type ProductFilters } from '../../services/adminProductService';

export interface Product {
  _id: string;
  product_id: string;
  name: string;
  slug: string;
  brand: string;
  category: {
    _id: string;
    name: string;
    slug: string;
  };
  subcategory?: string;
  description: string;
  short_description?: string;
  price: {
    original: number;
    current: number;
    currency: string;
    discount_percentage: number;
    savings: number;
  };
  colors: Array<{
    color_name: string;
    color_hex: string;
    image_url: string;
    is_available: boolean;
  }>;
  sizes: Array<{
    size_name: string;
    available_stock: number;
    is_available: boolean;
  }>;
  material?: string;
  style?: string;
  occasion?: string;
  season?: string;
  care_instructions?: string;
  tags: string[];
  rating: {
    average_rating: number;
    reviews_count: number;
    rating_breakdown: {
      "5_star": number;
      "4_star": number;
      "3_star": number;
      "2_star": number;
      "1_star": number;
    };
  };
  availability: 'In Stock' | 'Out of Stock' | 'Pre-order' | 'Discontinued';
  stock_status: 'Good Stock' | 'Limited Stock' | 'Low Stock' | 'Out of Stock';
  inventory: {
    total_stock: number;
    reserved_stock: number;
    available_stock: number;
    low_stock_threshold: number;
    track_inventory: boolean;
  };
  delivery_info: {
    free_shipping: boolean;
    delivery_time: string;
    delivery_cost: number;
    express_delivery?: {
      available: boolean;
      cost: number;
      delivery_time: string;
    };
  };
  product_images: Array<{
    image_url: string;
    alt_text: string;
    is_primary: boolean;
    sort_order: number;
  }>;
  related_products: string[];
  social_proof: {
    likes: number;
    shares: number;
    comments: number;
    wishlist_count: number;
    view_count: number;
  };
  seo: {
    meta_title?: string;
    meta_description?: string;
    keywords: string[];
    canonical_url?: string;
  };
  product_url: string;
  video_url?: string;
  is_featured: boolean;
  is_bestseller: boolean;
  is_new_arrival: boolean;
  is_trending: boolean;
  is_active: boolean;
  created_by?: string;
  updated_by?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProductsState {
  items: Product[];
  loading: boolean;
  error: string | null;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalProducts: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// Async thunk for fetching products
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (filters: ProductFilters = {}, { rejectWithValue }) => {
    try {
      const response = await AdminProductService.getAllProducts(filters);
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.message || 'Failed to fetch products');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch products');
    }
  }
);

const initialState: ProductsState = {
  items: [],
  loading: false,
  error: null,
  pagination: undefined,
};

const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setProducts: (state, action: PayloadAction<Product[]>) => {
      state.items = action.payload;
    },
    addProduct: (state, action: PayloadAction<Product>) => {
      state.items.push(action.payload);
    },
    updateProduct: (state, action: PayloadAction<Product>) => {
      const index = state.items.findIndex(product => product._id === action.payload._id);
      if (index !== -1) {
        state.items[index] = action.payload;
      }
    },
    deleteProduct: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(product => product._id !== action.payload);
    },
    updateProductCategory: (state, action: PayloadAction<{
      productId: string;
      oldCategory: string;
      newCategory: string;
    }>) => {
      const { productId, oldCategory, newCategory } = action.payload;
      const index = state.items.findIndex(product => product._id === productId);

      if (index !== -1 && state.items[index].category.name === oldCategory) {
        state.items[index].category.name = newCategory;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.data?.products || action.payload.products || [];
        state.pagination = action.payload.data?.pagination || action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setProducts,
  addProduct,
  updateProduct,
  deleteProduct,
  updateProductCategory,
  setLoading,
  setError
} = productsSlice.actions;

export default productsSlice.reducer;
