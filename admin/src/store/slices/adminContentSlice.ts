/**
 * Admin Content Redux Slice
 * Manages content data including banners, videos, media files, campaigns
 */

import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import {
  getAdminBanners,
  getAdminBanner,
  createAdminBanner,
  updateAdminBanner,
  deleteAdminBanner,
  uploadAdminBannerImages,
  getAdminVideos,
  getAdminVideo,
  createAdminVideo,
  updateAdminVideo,
  deleteAdminVideo,
  uploadAdminVideo,
  getAdminMediaFiles,
  uploadAdminMediaFile,
  deleteAdminMediaFile,
  getAdminCampaigns,
  createAdminCampaign,
  updateAdminCampaign,
  deleteAdminCampaign,
  getAdminContentAnalytics,
  type AdminBanner,
  type AdminVideo,
  type AdminMediaFile,
  type AdminCampaign,
  type AdminBannerCreate,
  type AdminVideoCreate,
} from '../../services/adminContentService';

// Content State Interface
export interface AdminContentState {
  // Banners
  banners: AdminBanner[];
  selectedBanner: AdminBanner | null;
  bannerFilters: {
    type?: string;
    isActive?: boolean;
    page: number;
    limit: number;
  };
  bannerPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;

  // Videos
  videos: AdminVideo[];
  selectedVideo: AdminVideo | null;
  videoFilters: {
    category?: string;
    isActive?: boolean;
    isFeatured?: boolean;
    page: number;
    limit: number;
  };
  videoPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;

  // Media Files
  mediaFiles: AdminMediaFile[];
  selectedMediaFile: AdminMediaFile | null;
  mediaFilters: {
    category?: string;
    mimeType?: string;
    tags?: string[];
    page: number;
    limit: number;
  };
  mediaPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;

  // Campaigns
  campaigns: AdminCampaign[];
  selectedCampaign: AdminCampaign | null;
  campaignFilters: {
    type?: string;
    status?: string;
    page: number;
    limit: number;
  };
  campaignPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;

  // Analytics
  contentAnalytics: any | null;

  // Loading states
  isBannersLoading: boolean;
  isBannerLoading: boolean;
  isBannerSaving: boolean;
  isBannerDeleting: boolean;
  isBannerUploading: boolean;

  isVideosLoading: boolean;
  isVideoLoading: boolean;
  isVideoSaving: boolean;
  isVideoDeleting: boolean;
  isVideoUploading: boolean;

  isMediaLoading: boolean;
  isMediaUploading: boolean;
  isMediaDeleting: boolean;

  isCampaignsLoading: boolean;
  isCampaignLoading: boolean;
  isCampaignSaving: boolean;
  isCampaignDeleting: boolean;

  isAnalyticsLoading: boolean;

  // Error states
  bannersError: string | null;
  bannerError: string | null;
  videosError: string | null;
  videoError: string | null;
  mediaError: string | null;
  campaignsError: string | null;
  campaignError: string | null;
  analyticsError: string | null;
}

// Initial state
const initialState: AdminContentState = {
  banners: [],
  selectedBanner: null,
  bannerFilters: { page: 1, limit: 10 },
  bannerPagination: null,

  videos: [],
  selectedVideo: null,
  videoFilters: { page: 1, limit: 10 },
  videoPagination: null,

  mediaFiles: [],
  selectedMediaFile: null,
  mediaFilters: { page: 1, limit: 20 },
  mediaPagination: null,

  campaigns: [],
  selectedCampaign: null,
  campaignFilters: { page: 1, limit: 10 },
  campaignPagination: null,

  contentAnalytics: null,

  isBannersLoading: false,
  isBannerLoading: false,
  isBannerSaving: false,
  isBannerDeleting: false,
  isBannerUploading: false,

  isVideosLoading: false,
  isVideoLoading: false,
  isVideoSaving: false,
  isVideoDeleting: false,
  isVideoUploading: false,

  isMediaLoading: false,
  isMediaUploading: false,
  isMediaDeleting: false,

  isCampaignsLoading: false,
  isCampaignLoading: false,
  isCampaignSaving: false,
  isCampaignDeleting: false,

  isAnalyticsLoading: false,

  bannersError: null,
  bannerError: null,
  videosError: null,
  videoError: null,
  mediaError: null,
  campaignsError: null,
  campaignError: null,
  analyticsError: null,
};

// Async Thunks

/**
 * Fetch banners
 */
export const fetchBanners = createAsyncThunk(
  'adminContent/fetchBanners',
  async (filters?: { type?: string; isActive?: boolean; page?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await getAdminBanners(filters);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch banners');
    }
  }
);

/**
 * Fetch single banner
 */
export const fetchBanner = createAsyncThunk(
  'adminContent/fetchBanner',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await getAdminBanner(id);
      return response.banner;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch banner');
    }
  }
);

/**
 * Create banner
 */
export const createBanner = createAsyncThunk(
  'adminContent/createBanner',
  async (bannerData: AdminBannerCreate, { rejectWithValue }) => {
    try {
      const response = await createAdminBanner(bannerData);
      return response.banner;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create banner');
    }
  }
);

/**
 * Update banner
 */
export const updateBanner = createAsyncThunk(
  'adminContent/updateBanner',
  async ({ id, bannerData }: { id: string; bannerData: Partial<AdminBannerCreate> }, { rejectWithValue }) => {
    try {
      const response = await updateAdminBanner(id, bannerData);
      return response.banner;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update banner');
    }
  }
);

/**
 * Delete banner
 */
export const deleteBanner = createAsyncThunk(
  'adminContent/deleteBanner',
  async (id: string, { rejectWithValue }) => {
    try {
      await deleteAdminBanner(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete banner');
    }
  }
);

/**
 * Upload banner images
 */
export const uploadBannerImages = createAsyncThunk(
  'adminContent/uploadBannerImages',
  async (files: { desktop: File; mobile: File; tablet?: File }, { rejectWithValue }) => {
    try {
      const response = await uploadAdminBannerImages(files);
      return response.images;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to upload banner images');
    }
  }
);

/**
 * Fetch videos
 */
export const fetchVideos = createAsyncThunk(
  'adminContent/fetchVideos',
  async (filters?: { category?: string; isActive?: boolean; isFeatured?: boolean; page?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await getAdminVideos(filters);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch videos');
    }
  }
);

/**
 * Create video
 */
export const createVideo = createAsyncThunk(
  'adminContent/createVideo',
  async (videoData: AdminVideoCreate, { rejectWithValue }) => {
    try {
      const response = await createAdminVideo(videoData);
      return response.video;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create video');
    }
  }
);

/**
 * Upload video
 */
export const uploadVideo = createAsyncThunk(
  'adminContent/uploadVideo',
  async ({ file, metadata }: { file: File; metadata: { title: string; description?: string; category: AdminVideo['category']; tags?: string[] } }, { rejectWithValue }) => {
    try {
      const response = await uploadAdminVideo(file, metadata);
      return response.video;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to upload video');
    }
  }
);

/**
 * Delete video
 */
export const deleteVideo = createAsyncThunk(
  'adminContent/deleteVideo',
  async (id: string, { rejectWithValue }) => {
    try {
      await deleteAdminVideo(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete video');
    }
  }
);

/**
 * Fetch media files
 */
export const fetchMediaFiles = createAsyncThunk(
  'adminContent/fetchMediaFiles',
  async (filters?: { category?: string; mimeType?: string; tags?: string[]; page?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await getAdminMediaFiles(filters);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch media files');
    }
  }
);

/**
 * Upload media file
 */
export const uploadMediaFile = createAsyncThunk(
  'adminContent/uploadMediaFile',
  async ({ file, metadata }: { file: File; metadata: { category?: string; tags?: string[]; alt_text?: string } }, { rejectWithValue }) => {
    try {
      const response = await uploadAdminMediaFile(file, metadata);
      return response.file;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to upload media file');
    }
  }
);

/**
 * Delete media file
 */
export const deleteMediaFile = createAsyncThunk(
  'adminContent/deleteMediaFile',
  async (id: string, { rejectWithValue }) => {
    try {
      await deleteAdminMediaFile(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete media file');
    }
  }
);

/**
 * Fetch campaigns
 */
export const fetchCampaigns = createAsyncThunk(
  'adminContent/fetchCampaigns',
  async (filters?: { type?: string; status?: string; page?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await getAdminCampaigns(filters);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch campaigns');
    }
  }
);

/**
 * Create campaign
 */
export const createCampaign = createAsyncThunk(
  'adminContent/createCampaign',
  async (campaignData: Omit<AdminCampaign, '_id' | 'analytics' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await createAdminCampaign(campaignData);
      return response.campaign;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create campaign');
    }
  }
);

/**
 * Fetch content analytics
 */
export const fetchContentAnalytics = createAsyncThunk(
  'adminContent/fetchContentAnalytics',
  async (dateRange?: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await getAdminContentAnalytics(dateRange);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch content analytics');
    }
  }
);

// Admin Content Slice
const adminContentSlice = createSlice({
  name: 'adminContent',
  initialState,
  reducers: {
    // Set selected banner
    setSelectedBanner: (state, action: PayloadAction<AdminBanner | null>) => {
      state.selectedBanner = action.payload;
    },

    // Set banner filters
    setBannerFilters: (state, action: PayloadAction<Partial<typeof initialState.bannerFilters>>) => {
      state.bannerFilters = { ...state.bannerFilters, ...action.payload };
    },

    // Set selected video
    setSelectedVideo: (state, action: PayloadAction<AdminVideo | null>) => {
      state.selectedVideo = action.payload;
    },

    // Set video filters
    setVideoFilters: (state, action: PayloadAction<Partial<typeof initialState.videoFilters>>) => {
      state.videoFilters = { ...state.videoFilters, ...action.payload };
    },

    // Set selected media file
    setSelectedMediaFile: (state, action: PayloadAction<AdminMediaFile | null>) => {
      state.selectedMediaFile = action.payload;
    },

    // Set media filters
    setMediaFilters: (state, action: PayloadAction<Partial<typeof initialState.mediaFilters>>) => {
      state.mediaFilters = { ...state.mediaFilters, ...action.payload };
    },

    // Set selected campaign
    setSelectedCampaign: (state, action: PayloadAction<AdminCampaign | null>) => {
      state.selectedCampaign = action.payload;
    },

    // Set campaign filters
    setCampaignFilters: (state, action: PayloadAction<Partial<typeof initialState.campaignFilters>>) => {
      state.campaignFilters = { ...state.campaignFilters, ...action.payload };
    },

    // Clear errors
    clearContentErrors: (state) => {
      state.bannersError = null;
      state.bannerError = null;
      state.videosError = null;
      state.videoError = null;
      state.mediaError = null;
      state.campaignsError = null;
      state.campaignError = null;
      state.analyticsError = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch Banners
    builder
      .addCase(fetchBanners.pending, (state) => {
        state.isBannersLoading = true;
        state.bannersError = null;
      })
      .addCase(fetchBanners.fulfilled, (state, action) => {
        state.isBannersLoading = false;
        state.banners = action.payload.banners;
        state.bannerPagination = action.payload.pagination;
      })
      .addCase(fetchBanners.rejected, (state, action) => {
        state.isBannersLoading = false;
        state.bannersError = action.payload as string;
      });

    // Create Banner
    builder
      .addCase(createBanner.pending, (state) => {
        state.isBannerSaving = true;
        state.bannerError = null;
      })
      .addCase(createBanner.fulfilled, (state, action) => {
        state.isBannerSaving = false;
        state.banners.unshift(action.payload);
      })
      .addCase(createBanner.rejected, (state, action) => {
        state.isBannerSaving = false;
        state.bannerError = action.payload as string;
      });

    // Update Banner
    builder
      .addCase(updateBanner.pending, (state) => {
        state.isBannerSaving = true;
        state.bannerError = null;
      })
      .addCase(updateBanner.fulfilled, (state, action) => {
        state.isBannerSaving = false;
        const index = state.banners.findIndex(banner => banner._id === action.payload._id);
        if (index !== -1) {
          state.banners[index] = action.payload;
        }
        if (state.selectedBanner?._id === action.payload._id) {
          state.selectedBanner = action.payload;
        }
      })
      .addCase(updateBanner.rejected, (state, action) => {
        state.isBannerSaving = false;
        state.bannerError = action.payload as string;
      });

    // Delete Banner
    builder
      .addCase(deleteBanner.pending, (state) => {
        state.isBannerDeleting = true;
        state.bannerError = null;
      })
      .addCase(deleteBanner.fulfilled, (state, action) => {
        state.isBannerDeleting = false;
        state.banners = state.banners.filter(banner => banner._id !== action.payload);
        if (state.selectedBanner?._id === action.payload) {
          state.selectedBanner = null;
        }
      })
      .addCase(deleteBanner.rejected, (state, action) => {
        state.isBannerDeleting = false;
        state.bannerError = action.payload as string;
      });

    // Fetch Videos
    builder
      .addCase(fetchVideos.pending, (state) => {
        state.isVideosLoading = true;
        state.videosError = null;
      })
      .addCase(fetchVideos.fulfilled, (state, action) => {
        state.isVideosLoading = false;
        state.videos = action.payload.videos;
        state.videoPagination = action.payload.pagination;
      })
      .addCase(fetchVideos.rejected, (state, action) => {
        state.isVideosLoading = false;
        state.videosError = action.payload as string;
      });

    // Upload Video
    builder
      .addCase(uploadVideo.pending, (state) => {
        state.isVideoUploading = true;
        state.videoError = null;
      })
      .addCase(uploadVideo.fulfilled, (state, action) => {
        state.isVideoUploading = false;
        state.videos.unshift(action.payload);
      })
      .addCase(uploadVideo.rejected, (state, action) => {
        state.isVideoUploading = false;
        state.videoError = action.payload as string;
      });

    // Fetch Media Files
    builder
      .addCase(fetchMediaFiles.pending, (state) => {
        state.isMediaLoading = true;
        state.mediaError = null;
      })
      .addCase(fetchMediaFiles.fulfilled, (state, action) => {
        state.isMediaLoading = false;
        state.mediaFiles = action.payload.files;
        state.mediaPagination = action.payload.pagination;
      })
      .addCase(fetchMediaFiles.rejected, (state, action) => {
        state.isMediaLoading = false;
        state.mediaError = action.payload as string;
      });

    // Upload Media File
    builder
      .addCase(uploadMediaFile.pending, (state) => {
        state.isMediaUploading = true;
        state.mediaError = null;
      })
      .addCase(uploadMediaFile.fulfilled, (state, action) => {
        state.isMediaUploading = false;
        state.mediaFiles.unshift(action.payload);
      })
      .addCase(uploadMediaFile.rejected, (state, action) => {
        state.isMediaUploading = false;
        state.mediaError = action.payload as string;
      });

    // Fetch Campaigns
    builder
      .addCase(fetchCampaigns.pending, (state) => {
        state.isCampaignsLoading = true;
        state.campaignsError = null;
      })
      .addCase(fetchCampaigns.fulfilled, (state, action) => {
        state.isCampaignsLoading = false;
        state.campaigns = action.payload.campaigns;
        state.campaignPagination = action.payload.pagination;
      })
      .addCase(fetchCampaigns.rejected, (state, action) => {
        state.isCampaignsLoading = false;
        state.campaignsError = action.payload as string;
      });

    // Fetch Content Analytics
    builder
      .addCase(fetchContentAnalytics.pending, (state) => {
        state.isAnalyticsLoading = true;
        state.analyticsError = null;
      })
      .addCase(fetchContentAnalytics.fulfilled, (state, action) => {
        state.isAnalyticsLoading = false;
        state.contentAnalytics = action.payload;
      })
      .addCase(fetchContentAnalytics.rejected, (state, action) => {
        state.isAnalyticsLoading = false;
        state.analyticsError = action.payload as string;
      });
  },
});

// Export actions
export const {
  setSelectedBanner,
  setBannerFilters,
  setSelectedVideo,
  setVideoFilters,
  setSelectedMediaFile,
  setMediaFilters,
  setSelectedCampaign,
  setCampaignFilters,
  clearContentErrors,
} = adminContentSlice.actions;

// Selectors
export const selectBanners = (state: { adminContent: AdminContentState }) => state.adminContent.banners;
export const selectSelectedBanner = (state: { adminContent: AdminContentState }) => state.adminContent.selectedBanner;
export const selectVideos = (state: { adminContent: AdminContentState }) => state.adminContent.videos;
export const selectSelectedVideo = (state: { adminContent: AdminContentState }) => state.adminContent.selectedVideo;
export const selectMediaFiles = (state: { adminContent: AdminContentState }) => state.adminContent.mediaFiles;
export const selectCampaigns = (state: { adminContent: AdminContentState }) => state.adminContent.campaigns;
export const selectContentAnalytics = (state: { adminContent: AdminContentState }) => state.adminContent.contentAnalytics;
export const selectContentLoading = (state: { adminContent: AdminContentState }) => ({
  banners: state.adminContent.isBannersLoading,
  videos: state.adminContent.isVideosLoading,
  media: state.adminContent.isMediaLoading,
  campaigns: state.adminContent.isCampaignsLoading,
  analytics: state.adminContent.isAnalyticsLoading,
});

export default adminContentSlice.reducer;
