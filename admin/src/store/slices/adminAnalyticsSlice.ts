/**
 * Admin Analytics Redux Slice
 * Manages analytics data, dashboard stats, reports
 */

import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import {
  getAdminDashboardStats,
  getAdminSalesChart,
  getAdminSalesAnalytics,
  getAdminCustomerAnalytics,
  getAdminProductAnalytics,
  getAdminRevenueAnalytics,
  getAdminConversionAnalytics,
  getAdminTrafficAnalytics,
  getAdminReports,
  generateAdminCustomReport,
  getAdminRealTimeMetrics,
  getAdminPerformanceMetrics,
  type AdminDashboardStats,
  type AdminSalesAnalytics,
  type AdminCustomerAnalytics,
  type AdminProductAnalytics,
  type AdminCustomReport,
} from '../../services/adminAnalyticsService';

// Analytics State Interface
export interface AdminAnalyticsState {
  // Dashboard data
  dashboardStats: AdminDashboardStats | null;
  salesChart: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string;
      borderColor?: string;
    }>;
  } | null;
  realTimeMetrics: {
    active_users: number;
    current_orders: number;
    live_sales: number;
    cart_additions: number;
    recent_activities: Array<{
      type: string;
      description: string;
      timestamp: string;
    }>;
  } | null;

  // Analytics data
  salesAnalytics: AdminSalesAnalytics | null;
  customerAnalytics: AdminCustomerAnalytics | null;
  productAnalytics: AdminProductAnalytics | null;
  revenueAnalytics: any | null;
  conversionAnalytics: any | null;
  trafficAnalytics: any | null;
  performanceMetrics: any | null;

  // Reports
  reports: Array<{
    id: string;
    name: string;
    description: string;
    type: string;
    created_at: string;
    last_generated: string;
  }>;
  customReports: AdminCustomReport[];

  // Loading states
  isDashboardLoading: boolean;
  isSalesLoading: boolean;
  isCustomerLoading: boolean;
  isProductLoading: boolean;
  isRevenueLoading: boolean;
  isConversionLoading: boolean;
  isTrafficLoading: boolean;
  isReportsLoading: boolean;
  isRealTimeLoading: boolean;
  isPerformanceLoading: boolean;

  // Error states
  dashboardError: string | null;
  salesError: string | null;
  customerError: string | null;
  productError: string | null;
  revenueError: string | null;
  conversionError: string | null;
  trafficError: string | null;
  reportsError: string | null;
  realTimeError: string | null;
  performanceError: string | null;

  // Filters
  dateRange: {
    start: string;
    end: string;
  } | null;
  selectedPeriod: 'day' | 'week' | 'month' | 'year';
}

// Initial state
const initialState: AdminAnalyticsState = {
  dashboardStats: null,
  salesChart: null,
  realTimeMetrics: null,
  salesAnalytics: null,
  customerAnalytics: null,
  productAnalytics: null,
  revenueAnalytics: null,
  conversionAnalytics: null,
  trafficAnalytics: null,
  performanceMetrics: null,
  reports: [],
  customReports: [],
  isDashboardLoading: false,
  isSalesLoading: false,
  isCustomerLoading: false,
  isProductLoading: false,
  isRevenueLoading: false,
  isConversionLoading: false,
  isTrafficLoading: false,
  isReportsLoading: false,
  isRealTimeLoading: false,
  isPerformanceLoading: false,
  dashboardError: null,
  salesError: null,
  customerError: null,
  productError: null,
  revenueError: null,
  conversionError: null,
  trafficError: null,
  reportsError: null,
  realTimeError: null,
  performanceError: null,
  dateRange: null,
  selectedPeriod: 'month',
};

// Async Thunks

/**
 * Fetch dashboard statistics
 */
export const fetchDashboardStats = createAsyncThunk(
  'adminAnalytics/fetchDashboardStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getAdminDashboardStats();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch dashboard stats');
    }
  }
);

/**
 * Fetch sales chart data
 */
export const fetchSalesChart = createAsyncThunk(
  'adminAnalytics/fetchSalesChart',
  async (period: 'day' | 'week' | 'month' | 'year', { rejectWithValue }) => {
    try {
      const response = await getAdminSalesChart(period);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch sales chart');
    }
  }
);

/**
 * Fetch sales analytics
 */
export const fetchSalesAnalytics = createAsyncThunk(
  'adminAnalytics/fetchSalesAnalytics',
  async (dateRange?: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await getAdminSalesAnalytics(dateRange);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch sales analytics');
    }
  }
);

/**
 * Fetch customer analytics
 */
export const fetchCustomerAnalytics = createAsyncThunk(
  'adminAnalytics/fetchCustomerAnalytics',
  async (dateRange?: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await getAdminCustomerAnalytics(dateRange);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch customer analytics');
    }
  }
);

/**
 * Fetch product analytics
 */
export const fetchProductAnalytics = createAsyncThunk(
  'adminAnalytics/fetchProductAnalytics',
  async (dateRange?: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await getAdminProductAnalytics(dateRange);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch product analytics');
    }
  }
);

/**
 * Fetch revenue analytics
 */
export const fetchRevenueAnalytics = createAsyncThunk(
  'adminAnalytics/fetchRevenueAnalytics',
  async (dateRange?: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await getAdminRevenueAnalytics(dateRange);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch revenue analytics');
    }
  }
);

/**
 * Fetch conversion analytics
 */
export const fetchConversionAnalytics = createAsyncThunk(
  'adminAnalytics/fetchConversionAnalytics',
  async (dateRange?: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await getAdminConversionAnalytics(dateRange);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch conversion analytics');
    }
  }
);

/**
 * Fetch traffic analytics
 */
export const fetchTrafficAnalytics = createAsyncThunk(
  'adminAnalytics/fetchTrafficAnalytics',
  async (dateRange?: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await getAdminTrafficAnalytics(dateRange);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch traffic analytics');
    }
  }
);

/**
 * Fetch reports
 */
export const fetchReports = createAsyncThunk(
  'adminAnalytics/fetchReports',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getAdminReports();
      return response.reports;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch reports');
    }
  }
);

/**
 * Generate custom report
 */
export const generateCustomReport = createAsyncThunk(
  'adminAnalytics/generateCustomReport',
  async (reportConfig: AdminCustomReport, { rejectWithValue }) => {
    try {
      await generateAdminCustomReport(reportConfig);
      return reportConfig;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to generate report');
    }
  }
);

/**
 * Fetch real-time metrics
 */
export const fetchRealTimeMetrics = createAsyncThunk(
  'adminAnalytics/fetchRealTimeMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getAdminRealTimeMetrics();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch real-time metrics');
    }
  }
);

/**
 * Fetch performance metrics
 */
export const fetchPerformanceMetrics = createAsyncThunk(
  'adminAnalytics/fetchPerformanceMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getAdminPerformanceMetrics();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch performance metrics');
    }
  }
);

// Admin Analytics Slice
const adminAnalyticsSlice = createSlice({
  name: 'adminAnalytics',
  initialState,
  reducers: {
    // Set date range
    setDateRange: (state, action: PayloadAction<{ start: string; end: string } | null>) => {
      state.dateRange = action.payload;
    },

    // Set selected period
    setSelectedPeriod: (state, action: PayloadAction<'day' | 'week' | 'month' | 'year'>) => {
      state.selectedPeriod = action.payload;
    },

    // Clear analytics data
    clearAnalyticsData: (state) => {
      state.salesAnalytics = null;
      state.customerAnalytics = null;
      state.productAnalytics = null;
      state.revenueAnalytics = null;
      state.conversionAnalytics = null;
      state.trafficAnalytics = null;
    },

    // Clear errors
    clearAnalyticsErrors: (state) => {
      state.dashboardError = null;
      state.salesError = null;
      state.customerError = null;
      state.productError = null;
      state.revenueError = null;
      state.conversionError = null;
      state.trafficError = null;
      state.reportsError = null;
      state.realTimeError = null;
      state.performanceError = null;
    },
  },
  extraReducers: (builder) => {
    // Dashboard Stats
    builder
      .addCase(fetchDashboardStats.pending, (state) => {
        state.isDashboardLoading = true;
        state.dashboardError = null;
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.isDashboardLoading = false;
        state.dashboardStats = action.payload;
      })
      .addCase(fetchDashboardStats.rejected, (state, action) => {
        state.isDashboardLoading = false;
        state.dashboardError = action.payload as string;
      });

    // Sales Chart
    builder
      .addCase(fetchSalesChart.pending, (state) => {
        state.isSalesLoading = true;
        state.salesError = null;
      })
      .addCase(fetchSalesChart.fulfilled, (state, action) => {
        state.isSalesLoading = false;
        state.salesChart = action.payload;
      })
      .addCase(fetchSalesChart.rejected, (state, action) => {
        state.isSalesLoading = false;
        state.salesError = action.payload as string;
      });

    // Sales Analytics
    builder
      .addCase(fetchSalesAnalytics.pending, (state) => {
        state.isSalesLoading = true;
        state.salesError = null;
      })
      .addCase(fetchSalesAnalytics.fulfilled, (state, action) => {
        state.isSalesLoading = false;
        state.salesAnalytics = action.payload;
      })
      .addCase(fetchSalesAnalytics.rejected, (state, action) => {
        state.isSalesLoading = false;
        state.salesError = action.payload as string;
      });

    // Customer Analytics
    builder
      .addCase(fetchCustomerAnalytics.pending, (state) => {
        state.isCustomerLoading = true;
        state.customerError = null;
      })
      .addCase(fetchCustomerAnalytics.fulfilled, (state, action) => {
        state.isCustomerLoading = false;
        state.customerAnalytics = action.payload;
      })
      .addCase(fetchCustomerAnalytics.rejected, (state, action) => {
        state.isCustomerLoading = false;
        state.customerError = action.payload as string;
      });

    // Product Analytics
    builder
      .addCase(fetchProductAnalytics.pending, (state) => {
        state.isProductLoading = true;
        state.productError = null;
      })
      .addCase(fetchProductAnalytics.fulfilled, (state, action) => {
        state.isProductLoading = false;
        state.productAnalytics = action.payload;
      })
      .addCase(fetchProductAnalytics.rejected, (state, action) => {
        state.isProductLoading = false;
        state.productError = action.payload as string;
      });

    // Revenue Analytics
    builder
      .addCase(fetchRevenueAnalytics.pending, (state) => {
        state.isRevenueLoading = true;
        state.revenueError = null;
      })
      .addCase(fetchRevenueAnalytics.fulfilled, (state, action) => {
        state.isRevenueLoading = false;
        state.revenueAnalytics = action.payload;
      })
      .addCase(fetchRevenueAnalytics.rejected, (state, action) => {
        state.isRevenueLoading = false;
        state.revenueError = action.payload as string;
      });

    // Real-time Metrics
    builder
      .addCase(fetchRealTimeMetrics.pending, (state) => {
        state.isRealTimeLoading = true;
        state.realTimeError = null;
      })
      .addCase(fetchRealTimeMetrics.fulfilled, (state, action) => {
        state.isRealTimeLoading = false;
        state.realTimeMetrics = action.payload;
      })
      .addCase(fetchRealTimeMetrics.rejected, (state, action) => {
        state.isRealTimeLoading = false;
        state.realTimeError = action.payload as string;
      });

    // Reports
    builder
      .addCase(fetchReports.pending, (state) => {
        state.isReportsLoading = true;
        state.reportsError = null;
      })
      .addCase(fetchReports.fulfilled, (state, action) => {
        state.isReportsLoading = false;
        state.reports = action.payload;
      })
      .addCase(fetchReports.rejected, (state, action) => {
        state.isReportsLoading = false;
        state.reportsError = action.payload as string;
      });

    // Generate Custom Report
    builder
      .addCase(generateCustomReport.fulfilled, (state, action) => {
        state.customReports.push(action.payload);
      });
  },
});

// Export actions
export const {
  setDateRange,
  setSelectedPeriod,
  clearAnalyticsData,
  clearAnalyticsErrors,
} = adminAnalyticsSlice.actions;

// Selectors
export const selectDashboardStats = (state: { adminAnalytics: AdminAnalyticsState }) => state.adminAnalytics.dashboardStats;
export const selectSalesChart = (state: { adminAnalytics: AdminAnalyticsState }) => state.adminAnalytics.salesChart;
export const selectRealTimeMetrics = (state: { adminAnalytics: AdminAnalyticsState }) => state.adminAnalytics.realTimeMetrics;
export const selectSalesAnalytics = (state: { adminAnalytics: AdminAnalyticsState }) => state.adminAnalytics.salesAnalytics;
export const selectCustomerAnalytics = (state: { adminAnalytics: AdminAnalyticsState }) => state.adminAnalytics.customerAnalytics;
export const selectProductAnalytics = (state: { adminAnalytics: AdminAnalyticsState }) => state.adminAnalytics.productAnalytics;
export const selectAnalyticsLoading = (state: { adminAnalytics: AdminAnalyticsState }) => ({
  dashboard: state.adminAnalytics.isDashboardLoading,
  sales: state.adminAnalytics.isSalesLoading,
  customer: state.adminAnalytics.isCustomerLoading,
  product: state.adminAnalytics.isProductLoading,
  revenue: state.adminAnalytics.isRevenueLoading,
  realTime: state.adminAnalytics.isRealTimeLoading,
});
export const selectDateRange = (state: { adminAnalytics: AdminAnalyticsState }) => state.adminAnalytics.dateRange;
export const selectSelectedPeriod = (state: { adminAnalytics: AdminAnalyticsState }) => state.adminAnalytics.selectedPeriod;

export default adminAnalyticsSlice.reducer;
