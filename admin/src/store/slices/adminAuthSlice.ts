/**
 * Admin Authentication Redux Slice
 * Manages admin user authentication state, login, logout, profile
 */

import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import {
  adminLogin,
  adminLogout,
  verifyAdminAuth,
  getAdminProfile,
  updateAdminProfile,
  changeAdminPassword,
  enableAdmin2FA,
  verifyAdmin2FA,
  disableAdmin2FA,
  type AdminUser,
  type AdminLoginRequest,
  type AdminProfileUpdateRequest,
  type AdminChangePasswordRequest,
  type Admin2FARequest,
} from '../../services/adminAuthService';

// Admin Auth State Interface
export interface AdminAuthState {
  // User data
  user: AdminUser | null;
  isAuthenticated: boolean;

  // Loading states
  isLoading: boolean;
  isLoginLoading: boolean;
  isLogoutLoading: boolean;
  isProfileUpdateLoading: boolean;
  isPasswordChangeLoading: boolean;
  is2FALoading: boolean;

  // Error states
  error: string | null;
  loginError: string | null;
  profileError: string | null;
  passwordError: string | null;
  twoFactorError: string | null;

  // 2FA state
  requiresTwoFactor: boolean;
  twoFactorSetup: {
    secret?: string;
    qrCode?: string;
    backupCodes?: string[];
  } | null;

  // Session info
  lastActivity: string | null;
  sessionExpiry: string | null;
}

// Initial state
const initialState: AdminAuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  isLoginLoading: false,
  isLogoutLoading: false,
  isProfileUpdateLoading: false,
  isPasswordChangeLoading: false,
  is2FALoading: false,
  error: null,
  loginError: null,
  profileError: null,
  passwordError: null,
  twoFactorError: null,
  requiresTwoFactor: false,
  twoFactorSetup: null,
  lastActivity: null,
  sessionExpiry: null,
};

// Async Thunks

/**
 * Admin login
 */
export const loginAdmin = createAsyncThunk(
  'adminAuth/login',
  async (credentials: AdminLoginRequest, { rejectWithValue }) => {
    try {
      const response = await adminLogin(credentials);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Login failed');
    }
  }
);

/**
 * Admin logout
 */
export const logoutAdmin = createAsyncThunk(
  'adminAuth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await adminLogout();
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Logout failed');
    }
  }
);

/**
 * Verify admin authentication
 */
export const verifyAdmin = createAsyncThunk(
  'adminAuth/verify',
  async (_, { rejectWithValue }) => {
    try {
      const response = await verifyAdminAuth();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Verification failed');
    }
  }
);

/**
 * Get admin profile
 */
export const fetchAdminProfile = createAsyncThunk(
  'adminAuth/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getAdminProfile();
      return response.user;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch profile');
    }
  }
);

/**
 * Update admin profile
 */
export const updateAdminProfileData = createAsyncThunk(
  'adminAuth/updateProfile',
  async (profileData: AdminProfileUpdateRequest, { rejectWithValue }) => {
    try {
      const response = await updateAdminProfile(profileData);
      return response.user;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update profile');
    }
  }
);

/**
 * Change admin password
 */
export const changePassword = createAsyncThunk(
  'adminAuth/changePassword',
  async (passwordData: AdminChangePasswordRequest, { rejectWithValue }) => {
    try {
      await changeAdminPassword(passwordData);
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to change password');
    }
  }
);

/**
 * Enable 2FA
 */
export const enable2FA = createAsyncThunk(
  'adminAuth/enable2FA',
  async (_, { rejectWithValue }) => {
    try {
      const response = await enableAdmin2FA();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to enable 2FA');
    }
  }
);

/**
 * Verify 2FA setup
 */
export const verify2FA = createAsyncThunk(
  'adminAuth/verify2FA',
  async (tokenData: Admin2FARequest, { rejectWithValue }) => {
    try {
      await verifyAdmin2FA(tokenData);
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to verify 2FA');
    }
  }
);

/**
 * Disable 2FA
 */
export const disable2FA = createAsyncThunk(
  'adminAuth/disable2FA',
  async (tokenData: Admin2FARequest, { rejectWithValue }) => {
    try {
      await disableAdmin2FA(tokenData);
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to disable 2FA');
    }
  }
);

// Admin Auth Slice
const adminAuthSlice = createSlice({
  name: 'adminAuth',
  initialState,
  reducers: {
    // Clear errors
    clearErrors: (state) => {
      state.error = null;
      state.loginError = null;
      state.profileError = null;
      state.passwordError = null;
      state.twoFactorError = null;
    },

    // Clear session
    clearSession: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.requiresTwoFactor = false;
      state.twoFactorSetup = null;
      state.lastActivity = null;
      state.sessionExpiry = null;
    },

    // Update last activity
    updateLastActivity: (state) => {
      state.lastActivity = new Date().toISOString();
    },

    // Set user data
    setUser: (state, action: PayloadAction<AdminUser>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },

    // Clear 2FA setup
    clear2FASetup: (state) => {
      state.twoFactorSetup = null;
      state.requiresTwoFactor = false;
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(loginAdmin.pending, (state) => {
        state.isLoginLoading = true;
        state.loginError = null;
      })
      .addCase(loginAdmin.fulfilled, (state, action) => {
        state.isLoginLoading = false;
        if (action.payload.requiresTwoFactor) {
          state.requiresTwoFactor = true;
        } else {
          state.user = action.payload.user;
          state.isAuthenticated = true;
          state.lastActivity = new Date().toISOString();
        }
      })
      .addCase(loginAdmin.rejected, (state, action) => {
        state.isLoginLoading = false;
        state.loginError = action.payload as string;
      });

    // Logout
    builder
      .addCase(logoutAdmin.pending, (state) => {
        state.isLogoutLoading = true;
      })
      .addCase(logoutAdmin.fulfilled, (state) => {
        state.isLogoutLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.requiresTwoFactor = false;
        state.twoFactorSetup = null;
        state.lastActivity = null;
        state.sessionExpiry = null;
      })
      .addCase(logoutAdmin.rejected, (state, action) => {
        state.isLogoutLoading = false;
        state.error = action.payload as string;
      });

    // Verify
    builder
      .addCase(verifyAdmin.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyAdmin.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
        state.lastActivity = new Date().toISOString();
      })
      .addCase(verifyAdmin.rejected, (state, action) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.error = action.payload as string;
      });

    // Fetch Profile
    builder
      .addCase(fetchAdminProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchAdminProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
      })
      .addCase(fetchAdminProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.profileError = action.payload as string;
      });

    // Update Profile
    builder
      .addCase(updateAdminProfileData.pending, (state) => {
        state.isProfileUpdateLoading = true;
        state.profileError = null;
      })
      .addCase(updateAdminProfileData.fulfilled, (state, action) => {
        state.isProfileUpdateLoading = false;
        state.user = action.payload;
      })
      .addCase(updateAdminProfileData.rejected, (state, action) => {
        state.isProfileUpdateLoading = false;
        state.profileError = action.payload as string;
      });

    // Change Password
    builder
      .addCase(changePassword.pending, (state) => {
        state.isPasswordChangeLoading = true;
        state.passwordError = null;
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.isPasswordChangeLoading = false;
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.isPasswordChangeLoading = false;
        state.passwordError = action.payload as string;
      });

    // Enable 2FA
    builder
      .addCase(enable2FA.pending, (state) => {
        state.is2FALoading = true;
        state.twoFactorError = null;
      })
      .addCase(enable2FA.fulfilled, (state, action) => {
        state.is2FALoading = false;
        state.twoFactorSetup = action.payload;
      })
      .addCase(enable2FA.rejected, (state, action) => {
        state.is2FALoading = false;
        state.twoFactorError = action.payload as string;
      });

    // Verify 2FA
    builder
      .addCase(verify2FA.pending, (state) => {
        state.is2FALoading = true;
        state.twoFactorError = null;
      })
      .addCase(verify2FA.fulfilled, (state) => {
        state.is2FALoading = false;
        if (state.user) {
          state.user.twoFactorAuth.enabled = true;
        }
        state.twoFactorSetup = null;
      })
      .addCase(verify2FA.rejected, (state, action) => {
        state.is2FALoading = false;
        state.twoFactorError = action.payload as string;
      });

    // Disable 2FA
    builder
      .addCase(disable2FA.pending, (state) => {
        state.is2FALoading = true;
        state.twoFactorError = null;
      })
      .addCase(disable2FA.fulfilled, (state) => {
        state.is2FALoading = false;
        if (state.user) {
          state.user.twoFactorAuth.enabled = false;
        }
      })
      .addCase(disable2FA.rejected, (state, action) => {
        state.is2FALoading = false;
        state.twoFactorError = action.payload as string;
      });
  },
});

// Export actions
export const {
  clearErrors,
  clearSession,
  updateLastActivity,
  setUser,
  clear2FASetup,
} = adminAuthSlice.actions;

// Selectors
export const selectAdminUser = (state: { adminAuth: AdminAuthState }) => state.adminAuth.user;
export const selectIsAdminAuthenticated = (state: { adminAuth: AdminAuthState }) => state.adminAuth.isAuthenticated;
export const selectAdminAuthLoading = (state: { adminAuth: AdminAuthState }) => state.adminAuth.isLoading;
export const selectAdminLoginLoading = (state: { adminAuth: AdminAuthState }) => state.adminAuth.isLoginLoading;
export const selectAdminLoginError = (state: { adminAuth: AdminAuthState }) => state.adminAuth.loginError;
export const selectAdminProfileError = (state: { adminAuth: AdminAuthState }) => state.adminAuth.profileError;
export const selectAdminRequires2FA = (state: { adminAuth: AdminAuthState }) => state.adminAuth.requiresTwoFactor;
export const selectAdmin2FASetup = (state: { adminAuth: AdminAuthState }) => state.adminAuth.twoFactorSetup;

export default adminAuthSlice.reducer;
