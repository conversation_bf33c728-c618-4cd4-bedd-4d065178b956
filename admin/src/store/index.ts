import { configureStore } from "@reduxjs/toolkit";
import productsReducer from "./slices/productsSlice";
import ordersReducer from "./slices/ordersSlice";
import customersReducer from "./slices/customersSlice";
import adminAuthReducer from "./slices/adminAuthSlice";
import adminAnalyticsReducer from "./slices/adminAnalyticsSlice";
import adminContentReducer from "./slices/adminContentSlice";

export const store = configureStore({
  reducer: {
    // Existing slices
    products: productsReducer,
    orders: ordersReducer,
    customers: customersReducer,

    // New admin slices
    adminAuth: adminAuthReducer,
    adminAnalytics: adminAnalyticsReducer,
    adminContent: adminContentReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        // Ignore these field paths in all actions
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        // Ignore these paths in the state
        ignoredPaths: ['items.dates'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
