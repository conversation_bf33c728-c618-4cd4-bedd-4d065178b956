/**
 * Video Types - Shared type definitions for video management
 */

export interface AdminVideo {
  _id: string;
  title: string;
  description: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: string;
  category: 'product_showcase' | 'fashion_tips' | 'styling_guide' | 'behind_scenes' | 
           'customer_stories' | 'brand_story' | 'tutorial' | 'collection_launch' | 
           'seasonal' | 'promotional' | 'testimonial';
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  isPublished: boolean;
  displayOrder: number;
  autoplay: boolean;
  showControls: boolean;
  loop: boolean;
  muted: boolean;
  analytics: {
    views: number;
    likes: number;
    shares: number;
    engagement_rate: number;
    average_watch_time: number;
  };
  seoTitle?: string;
  seoDescription?: string;
  altText?: string;
  relatedProducts: string[];
  relatedCategories: string[];
  publishedAt?: string;
  scheduledFor?: string;
  expiresAt?: string;
  uploadedBy?: string;
  moderatedBy?: string;
  moderatedAt?: string;
  moderationNotes?: string;
  encoding?: {
    codec: string;
    bitrate: string;
    framerate: string;
  };
  uploadSource: 'admin_panel' | 'api' | 'bulk_upload' | 'mobile_app';
  captions: Array<{
    language: string;
    captionUrl: string;
    isDefault: boolean;
  }>;
  transcript?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminVideoCreate {
  title: string;
  description: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: string;
  category: AdminVideo['category'];
  tags?: string[];
  isActive?: boolean;
  isFeatured?: boolean;
  isPublished?: boolean;
  displayOrder?: number;
  autoplay?: boolean;
  showControls?: boolean;
  loop?: boolean;
  muted?: boolean;
  seoTitle?: string;
  seoDescription?: string;
  altText?: string;
  relatedProducts?: string[];
  relatedCategories?: string[];
  publishedAt?: string;
  scheduledFor?: string;
  expiresAt?: string;
  moderationNotes?: string;
  encoding?: {
    codec: string;
    bitrate: string;
    framerate: string;
  };
  captions?: Array<{
    language: string;
    captionUrl: string;
    isDefault: boolean;
  }>;
  transcript?: string;
}


